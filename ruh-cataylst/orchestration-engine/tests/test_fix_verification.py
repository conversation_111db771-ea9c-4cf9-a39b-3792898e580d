#!/usr/bin/env python3
"""
Simple test script to verify that the loop executor fix is working.
This script tests that the loop executor now uses the new execution path
instead of the old direct execution bypass.
"""

import asyncio
import sys
import os
from unittest.mock import Mock, AsyncMock

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from services.loop_executor.loop_executor import LoopExecutor
from services.loop_executor.loop_body_chain_executor import LoopBodyChainExecutor


async def test_execution_path():
    """Test that the loop executor uses the new execution path."""

    print("🧪 Testing Loop Executor Execution Path Fix")
    print("=" * 50)

    # Create mock dependencies
    mock_state_manager = Mock()
    mock_transition_handler = Mock()
    mock_workflow_utils = Mock()
    mock_transitions_by_id = {
        "entry_transition_1": {"id": "entry_transition_1", "type": "standard"},
        "exit_transition_1": {"id": "exit_transition_1", "type": "standard"}
    }
    mock_result_callback = AsyncMock()
    mock_nodes = {}

    # Create loop executor
    loop_executor = LoopExecutor(
        state_manager=mock_state_manager,
        transition_handler=mock_transition_handler,
        workflow_utils=mock_workflow_utils,
        transitions_by_id=mock_transitions_by_id,
        user_id="test_user",
        result_callback=mock_result_callback,
        nodes=mock_nodes
    )

    # Mock the orchestration engine
    mock_orchestration_engine = Mock()
    mock_orchestration_engine.state_manager = Mock()
    mock_orchestration_engine.state_manager.pending_transitions = set()
    mock_orchestration_engine.transition_handler = mock_transition_handler
    mock_orchestration_engine.transitions_by_id = mock_transitions_by_id

    # Set the orchestration engine
    loop_executor.set_orchestration_engine(mock_orchestration_engine)

    # Verify that the loop body chain executor has the orchestration engine reference
    assert hasattr(loop_executor.loop_body_chain_executor, 'orchestration_engine'), \
        "❌ Loop body chain executor should have orchestration engine reference"
    print("✅ Loop body chain executor has orchestration engine reference")

    # Test the _execute_loop_workflow method directly to bypass validation issues
    loop_state = {
        "iteration_data": ["item1", "item2"],
        "loop_config": {
            "loop_type": "context_independent",
            "iteration_source": {
                "type": "list",
                "iteration_list": ["item1", "item2"]
            },
            "loop_body_configuration": {
                "entry_transitions": ["entry_transition_1"],
                "exit_transitions": ["exit_transition_1"],
                "chain_completion_detection": "explicit_exit_transitions"
            },
            "aggregation_config": {
                "type": "list"
            }
        }
    }

    # Mock the loop body chain executor's execute_loop_body_chain method
    # to track if it's being called (new path) vs old direct execution
    execution_calls = []

    async def mock_execute_loop_body_chain(*args, **kwargs):
        execution_calls.append(("new_path", args, kwargs))
        print("✅ NEW EXECUTION PATH: loop_body_chain_executor.execute_loop_body_chain called")
        return "mock_result"

    loop_executor.loop_body_chain_executor.execute_loop_body_chain = mock_execute_loop_body_chain

    # Mock the old _execute_iteration_chain method to track if it's called (old path)
    async def mock_execute_iteration_chain(*args, **kwargs):
        execution_calls.append(("old_path", args, kwargs))
        print("❌ OLD EXECUTION PATH: _execute_iteration_chain called (should not happen)")
        return {"success": True, "result": "old_mock_result"}

    loop_executor._execute_iteration_chain = mock_execute_iteration_chain

    # Mock the aggregation method to avoid issues
    async def mock_aggregate_iteration_results(results, loop_state):
        return {"aggregated_results": results}

    loop_executor._aggregate_iteration_results = mock_aggregate_iteration_results

    try:
        print("\n🔄 Testing loop workflow execution...")

        # Execute the loop workflow directly
        result = await loop_executor._execute_loop_workflow(loop_state)

        print(f"\n📊 Execution Results:")
        print(f"   - Total execution calls: {len(execution_calls)}")

        # Analyze execution calls
        new_path_calls = [call for call in execution_calls if call[0] == "new_path"]
        old_path_calls = [call for call in execution_calls if call[0] == "old_path"]

        print(f"   - New path calls: {len(new_path_calls)}")
        print(f"   - Old path calls: {len(old_path_calls)}")

        # Verify the fix
        if len(new_path_calls) > 0 and len(old_path_calls) == 0:
            print("\n🎉 SUCCESS: Loop executor is using the NEW execution path!")
            print("   ✅ No direct execution bypasses detected")
            print("   ✅ All iterations use loop_body_chain_executor")
            return True
        elif len(old_path_calls) > 0:
            print("\n❌ FAILURE: Loop executor is still using the OLD execution path!")
            print("   ❌ Direct execution bypasses detected")
            print("   ❌ Fix incomplete")
            return False
        else:
            print("\n⚠️  WARNING: No execution calls detected")
            print("   This might indicate a different issue")
            return False

    except Exception as e:
        print(f"\n❌ ERROR during execution: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Main test function."""
    success = await test_execution_path()
    
    if success:
        print("\n🎯 CONCLUSION: The loop executor fix is working correctly!")
        sys.exit(0)
    else:
        print("\n💥 CONCLUSION: The loop executor fix needs more work!")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
