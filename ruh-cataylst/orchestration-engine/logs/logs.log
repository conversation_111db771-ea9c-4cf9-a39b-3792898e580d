2025-06-30 14:29:50 - Main - INFO - Starting Server
2025-06-30 14:29:50 - Main - INFO - Connection at: **************:9092
2025-06-30 14:29:50 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-06-30 14:29:50 - NodeExecutor - INFO - NodeExecutor initialized.
2025-06-30 14:29:50 - AgentExecutor - INFO - AgentExecutor initialized.
2025-06-30 14:29:50 - KafkaWorkflowConsumer - INFO - Initializing database connections...
2025-06-30 14:29:50 - Red<PERSON><PERSON>anager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-30 14:29:52 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-30 14:29:52 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-30 14:29:53 - <PERSON><PERSON><PERSON><PERSON><PERSON> - INFO - Successfully connected to Redis on DB index: 6!
