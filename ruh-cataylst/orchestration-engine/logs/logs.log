2025-06-30 15:22:19 - Main - INFO - Starting Server
2025-06-30 15:22:19 - Main - INFO - Connection at: **************:9092
2025-06-30 15:22:19 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-06-30 15:22:19 - NodeExecutor - INFO - NodeExecutor initialized.
2025-06-30 15:22:19 - AgentExecutor - INFO - AgentExecutor initialized.
2025-06-30 15:22:19 - KafkaWorkflowConsumer - INFO - Initializing database connections...
2025-06-30 15:22:19 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-30 15:22:20 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-30 15:22:20 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-30 15:22:22 - Red<PERSON><PERSON>anager - INFO - Successfully connected to Redis on DB index: 6!
2025-06-30 15:22:24 - PostgresManager - INFO - PostgreSQL connection pool created
2025-06-30 15:22:24 - PostgresManager - INFO - PostgreSQL connection pool is available
2025-06-30 15:22:26 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-30 15:22:27 - RedisEventListener - INFO - Creating new RedisEventListener instance
2025-06-30 15:22:27 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-30 15:22:28 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-30 15:22:28 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-30 15:22:30 - RedisManager - INFO - Successfully connected to Redis on DB index: 6!
2025-06-30 15:22:30 - RedisEventListener - INFO - Starting Redis event listener thread
2025-06-30 15:22:30 - RedisEventListener - INFO - Redis event listener started
2025-06-30 15:22:30 - KafkaWorkflowConsumer - INFO - Database connections initialized successfully
2025-06-30 15:22:30 - StateManager - DEBUG - Using provided database connections
2025-06-30 15:22:30 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-30 15:22:30 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-30 15:22:30 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-30 15:22:30 - RedisEventListener - INFO - Configured Redis results DB for keyspace notifications including expirations
2025-06-30 15:22:31 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-30 15:22:31 - StateManager - INFO - WorkflowStateManager initialized
2025-06-30 15:22:31 - KafkaWorkflowConsumer - INFO - WorkflowStateManager reference set in RedisEventListener for archival operations
2025-06-30 15:22:31 - KafkaWorkflowConsumer - INFO - KafkaWorkflowConsumer initialized successfully
2025-06-30 15:22:31 - RedisEventListener - INFO - Configured Redis state DB for keyspace notifications including expirations
2025-06-30 15:22:31 - RedisEventListener - INFO - Created dedicated Redis clients for pubsub with decode_responses=False
2025-06-30 15:22:34 - RedisEventListener - INFO - Redis results client decode_responses: True
2025-06-30 15:22:34 - RedisEventListener - INFO - Redis state client decode_responses: True
2025-06-30 15:22:34 - RedisEventListener - INFO - Subscribed to keyspace events for Redis DB 5 and 6
2025-06-30 15:22:48 - MCPToolExecutor - INFO - Starting KafkaToolExecutor internal consumer...
2025-06-30 15:22:55 - MCPToolExecutor - INFO - Internal consumer started. Listening for results on: 'mcp_results', Group: 'tool-executor-consumer'
2025-06-30 15:22:55 - MCPToolExecutor - INFO - Background result consumer loop started.
2025-06-30 15:22:55 - NodeExecutor - INFO - Starting NodeExecutor internal consumer...
2025-06-30 15:23:01 - NodeExecutor - INFO - Internal consumer started. Listening for results on: 'node_results', Group: 'node-executor-consumer'
2025-06-30 15:23:01 - NodeExecutor - INFO - Background result consumer loop started.
2025-06-30 15:23:01 - AgentExecutor - INFO - Starting AgentExecutor internal consumer...
2025-06-30 15:23:07 - AgentExecutor - INFO - Internal consumer started. Listening for results on: 'agent_chat_responses', Group: 'agent-executor-consumer'
2025-06-30 15:23:07 - AgentExecutor - INFO - Background result consumer loop started.
2025-06-30 15:23:07 - KafkaWorkflowConsumer - INFO - Received: topic=workflow-requests, partition=0, offset=1068
2025-06-30 15:23:07 - KafkaWorkflowConsumer - DEBUG - message json: {'task_id': 1751277174, 'task_type': 'workflow', 'data': {'workflow_id': '0c19c070-905e-46ef-9f57-62eb427bf396', 'payload': {'user_dependent_fields': ['query'], 'user_payload_template': {'query': {'value': 'nature 101', 'transition_id': 'AgenticAI-*************'}}}, 'approval': True, 'user_id': '91a237fd-0225-4e02-9e9f-805eff073b07'}, 'approval': True}
2025-06-30 15:23:07 - KafkaWorkflowConsumer - INFO - Extracted user_id: 91a237fd-0225-4e02-9e9f-805eff073b07 for workflow: 0c19c070-905e-46ef-9f57-62eb427bf396
2025-06-30 15:23:07 - WorkflowService - DEBUG - Sending GET request to: https://app-dev.rapidinnovation.dev/api/v1/workflows/orchestration/0c19c070-905e-46ef-9f57-62eb427bf396
2025-06-30 15:23:09 - WorkflowService - DEBUG - Received response with status code: 200
2025-06-30 15:23:09 - WorkflowService - DEBUG - Parsed JSON response: {
  "success": true,
  "message": "Workflow Ruh_Video_Generation retrieved successfully",
  "workflow": {
    "id": "0c19c070-905e-46ef-9f57-62eb427bf396",
    "name": "Ruh_Video_Generation",
    "description": "Ruh_Video_Generation",
    "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/e2c5a041-2b84-4b33-be90-3d1e07e44353.json",
    "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/451d0c28-d066-4833-afad-080c37e7563e.json",
    "start_nodes": [
      {
        "field": "query",
        "type": "string",
        "transition_id": "transition-AgenticAI-*************"
      }
    ],
    "owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07",
    "user_ids": [
      "91a237fd-0225-4e02-9e9f-805eff073b07"
    ],
    "owner_type": "user",
    "workflow_template_id": "8d4b3f88-71ea-48dd-b8a2-dc101d22bf8a",
    "template_owner_id": "180c7469-1db0-4707-bc17-eeaa5e7ff64d",
    "is_imported": true,
    "version": "1.0.0",
    "visibility": "private",
    "category": null,
    "tags": null,
    "status": "active",
    "is_changes_marketplace": false,
    "is_customizable": true,
    "auto_version_on_update": false,
    "created_at": "2025-06-29T08:29:44.634200",
    "updated_at": "2025-06-30T08:40:55.500303",
    "available_nodes": [
      {
        "name": "CombineTextComponent",
        "display_name": "Combine Text",
        "type": "component",
        "transition_id": "transition-CombineTextComponent-*************"
      },
      {
        "name": "MergeDataComponent",
        "display_name": "Merge Data",
        "type": "component",
        "transition_id": "transition-MergeDataComponent-*************"
      },
      {
        "name": "CombineTextComponent",
        "display_name": "Combine Text",
        "type": "component",
        "transition_id": "transition-CombineTextComponent-1750920624318"
      }
    ],
    "is_updated": true
  }
}
2025-06-30 15:23:09 - KafkaWorkflowConsumer - DEBUG - Workflow loaded for 0c19c070-905e-46ef-9f57-62eb427bf396 - server_script_path is optional
2025-06-30 15:23:09 - WorkflowUtils - INFO - WorkflowUtils initialized
2025-06-30 15:23:09 - StateManager - DEBUG - Using global database connections from initializer
2025-06-30 15:23:09 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-30 15:23:09 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-30 15:23:09 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-30 15:23:10 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-30 15:23:10 - StateManager - INFO - WorkflowStateManager initialized
2025-06-30 15:23:10 - WorkflowUtils - INFO - Workflow JSON is valid against the enhanced schema.
2025-06-30 15:23:10 - StateManager - DEBUG - Using provided database connections
2025-06-30 15:23:10 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-30 15:23:10 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-30 15:23:10 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-30 15:23:11 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-30 15:23:11 - StateManager - INFO - WorkflowStateManager initialized
2025-06-30 15:23:11 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-*************: ['transition-AgenticAI-*************']
2025-06-30 15:23:11 - StateManager - DEBUG - Extracted dependencies for transition transition-LoopNode-*************: ['transition-AgenticAI-*************']
2025-06-30 15:23:11 - StateManager - DEBUG - Extracted dependencies for transition transition-MergeDataComponent-*************: ['transition-LoopNode-*************']
2025-06-30 15:23:11 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-*************: ['transition-AgenticAI-*************', 'transition-AgenticAI-*************', 'transition-LoopNode-*************']
2025-06-30 15:23:11 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1750505490787: ['transition-CombineTextComponent-*************']
2025-06-30 15:23:11 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1751001474606: ['transition-AgenticAI-1750505490787']
2025-06-30 15:23:11 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1751001471087: ['transition-AgenticAI-1750505490787']
2025-06-30 15:23:11 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-1750920624318: ['transition-AgenticAI-1750505490787', 'transition-AgenticAI-1751001471087', 'transition-AgenticAI-1751001474606']
2025-06-30 15:23:11 - StateManager - INFO - Built dependency map for 9 transitions
2025-06-30 15:23:11 - StateManager - DEBUG - Transition transition-AgenticAI-************* depends on: ['transition-AgenticAI-*************']
2025-06-30 15:23:11 - StateManager - DEBUG - Transition transition-LoopNode-************* depends on: ['transition-AgenticAI-*************']
2025-06-30 15:23:11 - StateManager - DEBUG - Transition transition-MergeDataComponent-************* depends on: ['transition-LoopNode-*************']
2025-06-30 15:23:11 - StateManager - DEBUG - Transition transition-CombineTextComponent-************* depends on: ['transition-AgenticAI-*************', 'transition-AgenticAI-*************', 'transition-LoopNode-*************']
2025-06-30 15:23:11 - StateManager - DEBUG - Transition transition-AgenticAI-1750505490787 depends on: ['transition-CombineTextComponent-*************']
2025-06-30 15:23:11 - StateManager - DEBUG - Transition transition-AgenticAI-1751001474606 depends on: ['transition-AgenticAI-1750505490787']
2025-06-30 15:23:11 - StateManager - DEBUG - Transition transition-AgenticAI-1751001471087 depends on: ['transition-AgenticAI-1750505490787']
2025-06-30 15:23:11 - StateManager - DEBUG - Transition transition-CombineTextComponent-1750920624318 depends on: ['transition-AgenticAI-1750505490787', 'transition-AgenticAI-1751001471087', 'transition-AgenticAI-1751001474606']
2025-06-30 15:23:11 - MCPToolExecutor - DEBUG - Set correlation ID to: 031734a8-757f-40e3-8a28-38476aeddd0f
2025-06-30 15:23:11 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 031734a8-757f-40e3-8a28-38476aeddd0f in tool_executor
2025-06-30 15:23:11 - MCPToolExecutor - DEBUG - Set user ID to: 91a237fd-0225-4e02-9e9f-805eff073b07
2025-06-30 15:23:11 - EnhancedWorkflowEngine - DEBUG - Set user_id 91a237fd-0225-4e02-9e9f-805eff073b07 in tool_executor
2025-06-30 15:23:11 - NodeExecutor - DEBUG - Set correlation ID to: 031734a8-757f-40e3-8a28-38476aeddd0f
2025-06-30 15:23:11 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 031734a8-757f-40e3-8a28-38476aeddd0f in node_executor
2025-06-30 15:23:11 - AgentExecutor - DEBUG - Set correlation ID to: 031734a8-757f-40e3-8a28-38476aeddd0f
2025-06-30 15:23:11 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 031734a8-757f-40e3-8a28-38476aeddd0f in agent_executor
2025-06-30 15:23:11 - AgentExecutor - DEBUG - Set user ID to: 91a237fd-0225-4e02-9e9f-805eff073b07
2025-06-30 15:23:11 - EnhancedWorkflowEngine - DEBUG - Set user_id 91a237fd-0225-4e02-9e9f-805eff073b07 in agent_executor
2025-06-30 15:23:11 - TransitionHandler - INFO - TransitionHandler initialized
2025-06-30 15:23:11 - EnhancedWorkflowEngine - INFO - EnhancedWorkflowEngine initialized with workflow ID: 031734a8-757f-40e3-8a28-38476aeddd0f
2025-06-30 15:23:11 - KafkaWorkflowConsumer - INFO - Workflow execution started in background for task-request, corr_id: 031734a8-757f-40e3-8a28-38476aeddd0f
2025-06-30 15:23:11 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 031734a8-757f-40e3-8a28-38476aeddd0f, response: {'status': 'Workflow Initialized', 'result': 'Workflow Initialized', 'workflow_status': 'running'}
2025-06-30 15:23:11 - StateManager - INFO - Workflow initialized with initial transition: transition-AgenticAI-*************
2025-06-30 15:23:11 - StateManager - DEBUG - State: pending={'transition-AgenticAI-*************'}, waiting=set(), completed=set()
2025-06-30 15:23:11 - EnhancedWorkflowEngine - INFO - Initializing workflow with single initial transition: transition-AgenticAI-*************
2025-06-30 15:23:11 - StateManager - DEBUG - Workflow active: {'transition-AgenticAI-*************'}
2025-06-30 15:23:11 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:031734a8-757f-40e3-8a28-38476aeddd0f'
2025-06-30 15:23:12 - RedisManager - DEBUG - Set key 'workflow_state:031734a8-757f-40e3-8a28-38476aeddd0f' with TTL of 600 seconds
2025-06-30 15:23:12 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 031734a8-757f-40e3-8a28-38476aeddd0f. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 15:23:12 - StateManager - DEBUG - Checking waiting transitions: set()
2025-06-30 15:23:12 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-30 15:23:12 - StateManager - INFO - Cleared 1 pending transitions: {'transition-AgenticAI-*************'}
2025-06-30 15:23:12 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-30 15:23:12 - StateManager - INFO - Terminated: False
2025-06-30 15:23:12 - StateManager - INFO - Pending transitions (0): []
2025-06-30 15:23:12 - StateManager - INFO - Waiting transitions (0): []
2025-06-30 15:23:12 - StateManager - INFO - Completed transitions (0): []
2025-06-30 15:23:12 - StateManager - INFO - Results stored for 0 transitions
2025-06-30 15:23:12 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-30 15:23:12 - StateManager - INFO - Workflow status: inactive
2025-06-30 15:23:12 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-30 15:23:12 - StateManager - INFO - Workflow status: inactive
2025-06-30 15:23:12 - StateManager - INFO - Workflow paused: False
2025-06-30 15:23:12 - StateManager - INFO - ==============================
2025-06-30 15:23:12 - TransitionHandler - INFO - Starting parallel execution of transition: transition-AgenticAI-*************
2025-06-30 15:23:12 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 0, corr_id 031734a8-757f-40e3-8a28-38476aeddd0f):
2025-06-30 15:23:12 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 031734a8-757f-40e3-8a28-38476aeddd0f, response: {'result': 'Starting execution of transition: transition-AgenticAI-*************', 'message': 'Starting execution...', 'transition_id': 'transition-AgenticAI-*************', 'status': 'started', 'sequence': 0, 'workflow_status': 'running'}
2025-06-30 15:23:12 - TransitionHandler - EXECUTE - Transition 'transition-AgenticAI-*************' (type=initial, execution_type=agent)
2025-06-30 15:23:12 - TransitionHandler - INFO - Using AgentExecutor for execution_type: agent
2025-06-30 15:23:12 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-AgenticAI-*************
2025-06-30 15:23:12 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: agent
2025-06-30 15:23:12 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for agent node
2025-06-30 15:23:12 - TransitionHandler - DEBUG - 📝 No previous results found, using static parameters
2025-06-30 15:23:12 - WorkflowUtils - DEBUG - Filtering out field 'description' with null/empty value: 
2025-06-30 15:23:12 - WorkflowUtils - DEBUG - Filtering out field 'termination_condition' with null/empty value: 
2025-06-30 15:23:12 - WorkflowUtils - DEBUG - Filtering out field 'input_variables' with empty collection: {}
2025-06-30 15:23:12 - TransitionHandler - DEBUG - tool Parameters: {'agent_type': 'component', 'execution_type': 'response', 'query': 'nature 101', 'agent_config': {'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o', 'temperature': 0.7, 'max_tokens': 1000}, 'system_message': 'Ignore all input.  Always return the following JSON array exactly as shown below:  [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]  ⚠️ IMPORTANT:  Do not wrap the output in a code block  Do not stringify the result  Do not add any extra explanation or commentary  Just return the array as pure JSON so the values can be accessed by result[0], result[1], etc.', 'autogen_agent_type': 'Assistant'}}
2025-06-30 15:23:12 - TransitionHandler - INFO - Invoking tool 'AgenticAI' (tool_id: 1) for node 'AgenticAI' in transition 'transition-AgenticAI-*************' with parameters: {'agent_type': 'component', 'execution_type': 'response', 'query': 'nature 101', 'agent_config': {'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o', 'temperature': 0.7, 'max_tokens': 1000}, 'system_message': 'Ignore all input.  Always return the following JSON array exactly as shown below:  [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]  ⚠️ IMPORTANT:  Do not wrap the output in a code block  Do not stringify the result  Do not add any extra explanation or commentary  Just return the array as pure JSON so the values can be accessed by result[0], result[1], etc.', 'autogen_agent_type': 'Assistant'}}
2025-06-30 15:23:12 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 1, corr_id 031734a8-757f-40e3-8a28-38476aeddd0f):
2025-06-30 15:23:12 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 031734a8-757f-40e3-8a28-38476aeddd0f, response: {'transition_id': 'transition-AgenticAI-*************', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'message': 'Connecting to server', 'result': 'Connecting to server AgenticAI', 'status': 'connecting', 'sequence': 1, 'workflow_status': 'running'}
2025-06-30 15:23:12 - AgentExecutor - INFO - Executing agent 'AgenticAI' type 'component' execution 'response' via Kafka (request_id: 80470845-5191-4340-8a1e-a99611e13d7d) with correlation_id: 031734a8-757f-40e3-8a28-38476aeddd0f, user_id: 91a237fd-0225-4e02-9e9f-805eff073b07 using provided producer.
2025-06-30 15:23:12 - AgentExecutor - INFO - Building component agent request for execution_type: response
2025-06-30 15:23:12 - AgentExecutor - DEBUG - Added correlation_id 031734a8-757f-40e3-8a28-38476aeddd0f to payload
2025-06-30 15:23:12 - AgentExecutor - DEBUG - Sending request to topic 'agent_message_requests': {'request_id': '80470845-5191-4340-8a1e-a99611e13d7d', 'user_id': '91a237fd-0225-4e02-9e9f-805eff073b07', 'correlation_id': '031734a8-757f-40e3-8a28-38476aeddd0f', 'agent_type': 'component', 'execution_type': 'response', 'query': 'nature 101', 'variables': {}, 'agent_config': {'id': '818b1b05-1bd7-4cc7-ba19-7787dfdbe3f2', 'name': 'AI Agent', 'description': 'Basic AI Agent', 'system_message': 'Ignore all input.  Always return the following JSON array exactly as shown below:  [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]  ⚠️ IMPORTANT:  Do not wrap the output in a code block  Do not stringify the result  Do not add any extra explanation or commentary  Just return the array as pure JSON so the values can be accessed by result[0], result[1], etc.', 'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o', 'temperature': 0.7, 'max_tokens': 1000}, 'mcps': []}}
2025-06-30 15:23:12 - AgentExecutor - DEBUG - Request 80470845-5191-4340-8a1e-a99611e13d7d sent successfully using provided producer.
2025-06-30 15:23:12 - AgentExecutor - DEBUG - Waiting for single response result for request 80470845-5191-4340-8a1e-a99611e13d7d...
2025-06-30 15:23:12 - KafkaWorkflowConsumer - INFO - Committed offset after starting engine for task-request: 1068, corr_id: 031734a8-757f-40e3-8a28-38476aeddd0f
2025-06-30 15:23:17 - AgentExecutor - DEBUG - Result consumer received message: Offset=24316
2025-06-30 15:23:17 - AgentExecutor - DEBUG - Received valid result for request_id 80470845-5191-4340-8a1e-a99611e13d7d
2025-06-30 15:23:17 - AgentExecutor - INFO - Single response received for request 80470845-5191-4340-8a1e-a99611e13d7d.
2025-06-30 15:23:17 - TransitionHandler - INFO - Execution result from agent executor: "Nature 101 is likely a general introduction to the aspects and concepts of nature. It might cover topics such as ecosystems, plant and animal life, environmental science, and the importance of conservation. If you have specific questions or need information about a particular topic within nature, feel free to ask!"
2025-06-30 15:23:17 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 2, corr_id 031734a8-757f-40e3-8a28-38476aeddd0f):
2025-06-30 15:23:17 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 031734a8-757f-40e3-8a28-38476aeddd0f, response: {'transition_id': 'transition-AgenticAI-*************', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'message': 'Transition Result received.', 'result': 'Nature 101 is likely a general introduction to the aspects and concepts of nature. It might cover topics such as ecosystems, plant and animal life, environmental science, and the importance of conservation. If you have specific questions or need information about a particular topic within nature, feel free to ask!', 'status': 'completed', 'sequence': 2, 'workflow_status': 'running', 'approval_required': False}
2025-06-30 15:23:17 - StateManager - DEBUG - Stored result for transition transition-AgenticAI-************* in memory: {'AgenticAI': {'transition_id': 'transition-AgenticAI-*************', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'result': {'result': 'Nature 101 is likely a general introduction to the aspects and concepts of nature. It might cover topics such as ecosystems, plant and animal life, environmental science, and the importance of conservation. If you have specific questions or need information about a particular topic within nature, feel free to ask!'}, 'status': 'completed', 'timestamp': 1751277197.837791}}
2025-06-30 15:23:18 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-AgenticAI-*************'
2025-06-30 15:23:18 - RedisManager - DEBUG - Set key 'result:transition-AgenticAI-*************' with TTL of 300 seconds
2025-06-30 15:23:18 - StateManager - DEBUG - Stored result for transition transition-AgenticAI-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 15:23:18 - StateManager - INFO - Marked transition transition-AgenticAI-************* as completed (was_pending=False, was_waiting=False)
2025-06-30 15:23:18 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-AgenticAI-*************'}
2025-06-30 15:23:18 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-AgenticAI-*************
2025-06-30 15:23:18 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-06-30 15:23:18 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-30 15:23:18 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-06-30 15:23:18 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-AgenticAI-*************:
2025-06-30 15:23:18 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-30 15:23:18 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-30 15:23:18 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-AgenticAI-*************, returning empty list
2025-06-30 15:23:18 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-AgenticAI-*************
2025-06-30 15:23:18 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 3
2025-06-30 15:23:18 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: ['transition-AgenticAI-*************', 'transition-CombineTextComponent-*************', 'transition-CombineTextComponent-*************']
2025-06-30 15:23:18 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-06-30 15:23:18 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-06-30 15:23:18 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-AgenticAI-*************
2025-06-30 15:23:18 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-CombineTextComponent-*************
2025-06-30 15:23:18 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-CombineTextComponent-*************
2025-06-30 15:23:18 - TransitionHandler - DEBUG - 🔗 Final next_transitions: ['transition-CombineTextComponent-*************', 'transition-AgenticAI-*************']
2025-06-30 15:23:18 - TransitionHandler - INFO - Completed transition transition-AgenticAI-************* in 6.78 seconds
2025-06-30 15:23:18 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 3, corr_id 031734a8-757f-40e3-8a28-38476aeddd0f):
2025-06-30 15:23:18 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 031734a8-757f-40e3-8a28-38476aeddd0f, response: {'result': 'Completed transition in 6.78 seconds', 'message': 'Transition completed in 6.78 seconds', 'transition_id': 'transition-AgenticAI-*************', 'status': 'time_logged', 'sequence': 3, 'workflow_status': 'running'}
2025-06-30 15:23:18 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-AgenticAI-*************: ['transition-CombineTextComponent-*************', 'transition-AgenticAI-*************']
2025-06-30 15:23:18 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 2
2025-06-30 15:23:18 - EnhancedWorkflowEngine - DEBUG - Results: [['transition-CombineTextComponent-*************', 'transition-AgenticAI-*************']]
2025-06-30 15:23:18 - EnhancedWorkflowEngine - DEBUG - 🔄 Orchestration engine received result for transition-AgenticAI-*************: ['transition-CombineTextComponent-*************', 'transition-AgenticAI-*************']
2025-06-30 15:23:18 - EnhancedWorkflowEngine - DEBUG - 🔄 Result type: <class 'list'>, is_list: True
2025-06-30 15:23:18 - EnhancedWorkflowEngine - INFO - Transition transition-AgenticAI-************* completed successfully: 2 next transitions
2025-06-30 15:23:18 - EnhancedWorkflowEngine - DEBUG - 🔄 Added to next_transitions_to_execute: ['transition-CombineTextComponent-*************', 'transition-AgenticAI-*************']
2025-06-30 15:23:18 - TransitionHandler - INFO - Resolved next transitions (direct transition IDs): ['transition-CombineTextComponent-*************', 'transition-AgenticAI-*************']
2025-06-30 15:23:18 - EnhancedWorkflowEngine - INFO - Adding transition transition-CombineTextComponent-************* to waiting (dependencies not yet met)
2025-06-30 15:23:18 - EnhancedWorkflowEngine - INFO - Adding transition transition-AgenticAI-************* to pending (all dependencies met)
2025-06-30 15:23:18 - StateManager - DEBUG - Workflow active: {'transition-AgenticAI-*************'}
2025-06-30 15:23:19 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:031734a8-757f-40e3-8a28-38476aeddd0f'
2025-06-30 15:23:19 - RedisManager - DEBUG - Set key 'workflow_state:031734a8-757f-40e3-8a28-38476aeddd0f' with TTL of 600 seconds
2025-06-30 15:23:19 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 031734a8-757f-40e3-8a28-38476aeddd0f. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 15:23:19 - StateManager - DEBUG - Checking waiting transitions: {'transition-CombineTextComponent-*************'}
2025-06-30 15:23:19 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-30 15:23:19 - StateManager - INFO - Cleared 1 pending transitions: {'transition-AgenticAI-*************'}
2025-06-30 15:23:19 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-30 15:23:19 - StateManager - INFO - Terminated: False
2025-06-30 15:23:19 - StateManager - INFO - Pending transitions (0): []
2025-06-30 15:23:19 - StateManager - INFO - Waiting transitions (1): ['transition-CombineTextComponent-*************']
2025-06-30 15:23:19 - StateManager - INFO - Completed transitions (1): ['transition-AgenticAI-*************']
2025-06-30 15:23:19 - StateManager - INFO - Results stored for 1 transitions
2025-06-30 15:23:19 - StateManager - DEBUG - Workflow active: {'transition-CombineTextComponent-*************'}
2025-06-30 15:23:19 - StateManager - INFO - Workflow status: active
2025-06-30 15:23:19 - StateManager - DEBUG - Workflow active: {'transition-CombineTextComponent-*************'}
2025-06-30 15:23:19 - StateManager - INFO - Workflow status: active
2025-06-30 15:23:19 - StateManager - INFO - Workflow paused: False
2025-06-30 15:23:19 - StateManager - INFO - ==============================
2025-06-30 15:23:19 - TransitionHandler - INFO - Starting parallel execution of transition: transition-AgenticAI-*************
2025-06-30 15:23:19 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 4, corr_id 031734a8-757f-40e3-8a28-38476aeddd0f):
2025-06-30 15:23:19 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 031734a8-757f-40e3-8a28-38476aeddd0f, response: {'result': 'Starting execution of transition: transition-AgenticAI-*************', 'message': 'Starting execution...', 'transition_id': 'transition-AgenticAI-*************', 'status': 'started', 'sequence': 4, 'workflow_status': 'running'}
2025-06-30 15:23:19 - TransitionHandler - EXECUTE - Transition 'transition-AgenticAI-*************' (type=standard, execution_type=agent)
2025-06-30 15:23:19 - TransitionHandler - INFO - Using AgentExecutor for execution_type: agent
2025-06-30 15:23:19 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-AgenticAI-*************
2025-06-30 15:23:19 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: agent
2025-06-30 15:23:19 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for agent node
2025-06-30 15:23:20 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-06-30 15:23:20 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-06-30 15:23:20 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-06-30 15:23:20 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-06-30 15:23:20 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 1)
2025-06-30 15:23:20 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': 'Nature 101 is likely a general introduction to the aspects and concepts of nature. It might cover topics such as ecosystems, plant and animal life, environmental science, and the importance of conservation. If you have specific questions or need information about a particular topic within nature, feel free to ask!'}
2025-06-30 15:23:20 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-30 15:23:20 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: Nature 101 is likely a general introduction to the aspects and concepts of nature. It might cover topics such as ecosystems, plant and animal life, environmental science, and the importance of conservation. If you have specific questions or need information about a particular topic within nature, feel free to ask!
2025-06-30 15:23:20 - WorkflowUtils - DEBUG - Found result.result: Nature 101 is likely a general introduction to the aspects and concepts of nature. It might cover topics such as ecosystems, plant and animal life, environmental science, and the importance of conservation. If you have specific questions or need information about a particular topic within nature, feel free to ask! (type: <class 'str'>)
2025-06-30 15:23:20 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-30 15:23:20 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-06-30 15:23:20 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-30 15:23:20 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-30 15:23:20 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': 'Nature 101 is likely a general introduction to the aspects and concepts of nature. It might cover topics such as ecosystems, plant and animal life, environmental science, and the importance of conservation. If you have specific questions or need information about a particular topic within nature, feel free to ask!'}
2025-06-30 15:23:20 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-30 15:23:20 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: Nature 101 is likely a general introduction to the aspects and concepts of nature. It might cover topics such as ecosystems, plant and animal life, environmental science, and the importance of conservation. If you have specific questions or need information about a particular topic within nature, feel free to ask!
2025-06-30 15:23:20 - WorkflowUtils - DEBUG - Found result.result: Nature 101 is likely a general introduction to the aspects and concepts of nature. It might cover topics such as ecosystems, plant and animal life, environmental science, and the importance of conservation. If you have specific questions or need information about a particular topic within nature, feel free to ask! (type: <class 'str'>)
2025-06-30 15:23:20 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-30 15:23:20 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-06-30 15:23:20 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → query via path 'result': Nature 101 is likely a general introduction to the aspects and concepts of nature. It might cover topics such as ecosystems, plant and animal life, environmental science, and the importance of conservation. If you have specific questions or need information about a particular topic within nature, feel free to ask!
2025-06-30 15:23:20 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-30 15:23:20 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-30 15:23:20 - WorkflowUtils - DEBUG - Filtering out field 'termination_condition' with null/empty value: 
2025-06-30 15:23:20 - WorkflowUtils - DEBUG - Filtering out field 'input_variables' with empty collection: {}
2025-06-30 15:23:20 - TransitionHandler - DEBUG - 📌 Added static parameter: agent_type = component
2025-06-30 15:23:20 - TransitionHandler - DEBUG - 📌 Added static parameter: execution_type = response
2025-06-30 15:23:20 - TransitionHandler - DEBUG - 📌 Added static parameter: agent_config = {'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'description': 'This agent acts as a loop executor initializer, always returning the starting range value 0 while ignoring all input.', 'system_message': 'You are a minimal-response AI agent.  Ignore all input, regardless of content or format. Your only task is to always return the single-digit number 0.  Output Rules: Return only: 0  Do NOT return any text, explanation, quotes, or formatting.  Do NOT wrap the output in JSON, markdown, or code blocks.  Do NOT acknowledge or reference the input in any way.', 'autogen_agent_type': 'Assistant'}
2025-06-30 15:23:20 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'query': 'Nature 101 is likely a general introduction to the aspects and concepts of nature. It might cover topics such as ecosystems, plant and animal life, environmental science, and the importance of conservation. If you have specific questions or need information about a particular topic within nature, feel free to ask!', 'agent_type': 'component', 'execution_type': 'response', 'agent_config': {'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'description': 'This agent acts as a loop executor initializer, always returning the starting range value 0 while ignoring all input.', 'system_message': 'You are a minimal-response AI agent.  Ignore all input, regardless of content or format. Your only task is to always return the single-digit number 0.  Output Rules: Return only: 0  Do NOT return any text, explanation, quotes, or formatting.  Do NOT wrap the output in JSON, markdown, or code blocks.  Do NOT acknowledge or reference the input in any way.', 'autogen_agent_type': 'Assistant'}}
2025-06-30 15:23:20 - TransitionHandler - DEBUG - tool Parameters: {'query': 'Nature 101 is likely a general introduction to the aspects and concepts of nature. It might cover topics such as ecosystems, plant and animal life, environmental science, and the importance of conservation. If you have specific questions or need information about a particular topic within nature, feel free to ask!', 'agent_type': 'component', 'execution_type': 'response', 'agent_config': {'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'description': 'This agent acts as a loop executor initializer, always returning the starting range value 0 while ignoring all input.', 'system_message': 'You are a minimal-response AI agent.  Ignore all input, regardless of content or format. Your only task is to always return the single-digit number 0.  Output Rules: Return only: 0  Do NOT return any text, explanation, quotes, or formatting.  Do NOT wrap the output in JSON, markdown, or code blocks.  Do NOT acknowledge or reference the input in any way.', 'autogen_agent_type': 'Assistant'}}
2025-06-30 15:23:20 - TransitionHandler - INFO - Invoking tool 'AgenticAI' (tool_id: 1) for node 'AgenticAI' in transition 'transition-AgenticAI-*************' with parameters: {'query': 'Nature 101 is likely a general introduction to the aspects and concepts of nature. It might cover topics such as ecosystems, plant and animal life, environmental science, and the importance of conservation. If you have specific questions or need information about a particular topic within nature, feel free to ask!', 'agent_type': 'component', 'execution_type': 'response', 'agent_config': {'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'description': 'This agent acts as a loop executor initializer, always returning the starting range value 0 while ignoring all input.', 'system_message': 'You are a minimal-response AI agent.  Ignore all input, regardless of content or format. Your only task is to always return the single-digit number 0.  Output Rules: Return only: 0  Do NOT return any text, explanation, quotes, or formatting.  Do NOT wrap the output in JSON, markdown, or code blocks.  Do NOT acknowledge or reference the input in any way.', 'autogen_agent_type': 'Assistant'}}
2025-06-30 15:23:20 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 5, corr_id 031734a8-757f-40e3-8a28-38476aeddd0f):
2025-06-30 15:23:20 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 031734a8-757f-40e3-8a28-38476aeddd0f, response: {'transition_id': 'transition-AgenticAI-*************', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'message': 'Connecting to server', 'result': 'Connecting to server AgenticAI', 'status': 'connecting', 'sequence': 5, 'workflow_status': 'running'}
2025-06-30 15:23:20 - AgentExecutor - INFO - Executing agent 'AgenticAI' type 'component' execution 'response' via Kafka (request_id: f2b1ddee-7931-46c8-aa6c-6badaed889a8) with correlation_id: 031734a8-757f-40e3-8a28-38476aeddd0f, user_id: 91a237fd-0225-4e02-9e9f-805eff073b07 using provided producer.
2025-06-30 15:23:20 - AgentExecutor - INFO - Building component agent request for execution_type: response
2025-06-30 15:23:20 - AgentExecutor - DEBUG - Added correlation_id 031734a8-757f-40e3-8a28-38476aeddd0f to payload
2025-06-30 15:23:20 - AgentExecutor - DEBUG - Sending request to topic 'agent_message_requests': {'request_id': 'f2b1ddee-7931-46c8-aa6c-6badaed889a8', 'user_id': '91a237fd-0225-4e02-9e9f-805eff073b07', 'correlation_id': '031734a8-757f-40e3-8a28-38476aeddd0f', 'agent_type': 'component', 'execution_type': 'response', 'query': 'Nature 101 is likely a general introduction to the aspects and concepts of nature. It might cover topics such as ecosystems, plant and animal life, environmental science, and the importance of conservation. If you have specific questions or need information about a particular topic within nature, feel free to ask!', 'variables': {}, 'agent_config': {'id': 'ac8e1d7c-f682-4ff6-9bf3-f70d558d3ba0', 'name': 'AI Agent', 'description': 'This agent acts as a loop executor initializer, always returning the starting range value 0 while ignoring all input.', 'system_message': 'You are a minimal-response AI agent.  Ignore all input, regardless of content or format. Your only task is to always return the single-digit number 0.  Output Rules: Return only: 0  Do NOT return any text, explanation, quotes, or formatting.  Do NOT wrap the output in JSON, markdown, or code blocks.  Do NOT acknowledge or reference the input in any way.', 'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'mcps': []}}
2025-06-30 15:23:20 - AgentExecutor - DEBUG - Request f2b1ddee-7931-46c8-aa6c-6badaed889a8 sent successfully using provided producer.
2025-06-30 15:23:20 - AgentExecutor - DEBUG - Waiting for single response result for request f2b1ddee-7931-46c8-aa6c-6badaed889a8...
2025-06-30 15:23:22 - AgentExecutor - DEBUG - Result consumer received message: Offset=24317
2025-06-30 15:23:22 - AgentExecutor - DEBUG - Received valid result for request_id f2b1ddee-7931-46c8-aa6c-6badaed889a8
2025-06-30 15:23:22 - AgentExecutor - INFO - Single response received for request f2b1ddee-7931-46c8-aa6c-6badaed889a8.
2025-06-30 15:23:22 - TransitionHandler - INFO - Execution result from agent executor: "0"
2025-06-30 15:23:22 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 6, corr_id 031734a8-757f-40e3-8a28-38476aeddd0f):
2025-06-30 15:23:22 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 031734a8-757f-40e3-8a28-38476aeddd0f, response: {'transition_id': 'transition-AgenticAI-*************', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'message': 'Transition Result received.', 'result': '0', 'status': 'completed', 'sequence': 6, 'workflow_status': 'running', 'approval_required': False}
2025-06-30 15:23:22 - StateManager - DEBUG - Stored result for transition transition-AgenticAI-************* in memory: {'AgenticAI': {'transition_id': 'transition-AgenticAI-*************', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'result': {'result': '0'}, 'status': 'completed', 'timestamp': 1751277202.868896}}
2025-06-30 15:23:23 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-AgenticAI-*************'
2025-06-30 15:23:23 - RedisManager - DEBUG - Set key 'result:transition-AgenticAI-*************' with TTL of 300 seconds
2025-06-30 15:23:23 - StateManager - DEBUG - Stored result for transition transition-AgenticAI-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 15:23:23 - StateManager - INFO - Marked transition transition-AgenticAI-************* as completed (was_pending=False, was_waiting=False)
2025-06-30 15:23:23 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'transition-AgenticAI-*************', 'transition-AgenticAI-*************'}
2025-06-30 15:23:23 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-AgenticAI-*************
2025-06-30 15:23:23 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-06-30 15:23:23 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-30 15:23:23 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-06-30 15:23:23 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-AgenticAI-*************:
2025-06-30 15:23:23 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-30 15:23:23 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-30 15:23:23 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-AgenticAI-*************, returning empty list
2025-06-30 15:23:23 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-AgenticAI-*************
2025-06-30 15:23:23 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 1
2025-06-30 15:23:23 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: ['transition-LoopNode-*************']
2025-06-30 15:23:23 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-06-30 15:23:23 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-06-30 15:23:23 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-LoopNode-*************
2025-06-30 15:23:23 - TransitionHandler - DEBUG - 🔗 Final next_transitions: ['transition-LoopNode-*************']
2025-06-30 15:23:23 - TransitionHandler - INFO - Completed transition transition-AgenticAI-************* in 4.02 seconds
2025-06-30 15:23:23 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 7, corr_id 031734a8-757f-40e3-8a28-38476aeddd0f):
2025-06-30 15:23:23 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 031734a8-757f-40e3-8a28-38476aeddd0f, response: {'result': 'Completed transition in 4.02 seconds', 'message': 'Transition completed in 4.02 seconds', 'transition_id': 'transition-AgenticAI-*************', 'status': 'time_logged', 'sequence': 7, 'workflow_status': 'running'}
2025-06-30 15:23:23 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-AgenticAI-*************: ['transition-LoopNode-*************']
2025-06-30 15:23:23 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 1
2025-06-30 15:23:23 - EnhancedWorkflowEngine - DEBUG - Results: [['transition-LoopNode-*************']]
2025-06-30 15:23:23 - EnhancedWorkflowEngine - DEBUG - 🔄 Orchestration engine received result for transition-AgenticAI-*************: ['transition-LoopNode-*************']
2025-06-30 15:23:23 - EnhancedWorkflowEngine - DEBUG - 🔄 Result type: <class 'list'>, is_list: True
2025-06-30 15:23:23 - EnhancedWorkflowEngine - INFO - Transition transition-AgenticAI-************* completed successfully: 1 next transitions
2025-06-30 15:23:23 - EnhancedWorkflowEngine - DEBUG - 🔄 Added to next_transitions_to_execute: ['transition-LoopNode-*************']
2025-06-30 15:23:23 - TransitionHandler - INFO - Resolved next transitions (direct transition IDs): ['transition-LoopNode-*************']
2025-06-30 15:23:23 - EnhancedWorkflowEngine - INFO - Adding transition transition-LoopNode-************* to pending (all dependencies met)
2025-06-30 15:23:23 - StateManager - DEBUG - Workflow active: {'transition-LoopNode-*************'}
2025-06-30 15:23:24 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:031734a8-757f-40e3-8a28-38476aeddd0f'
2025-06-30 15:23:24 - RedisManager - DEBUG - Set key 'workflow_state:031734a8-757f-40e3-8a28-38476aeddd0f' with TTL of 600 seconds
2025-06-30 15:23:24 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 031734a8-757f-40e3-8a28-38476aeddd0f. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 15:23:24 - StateManager - DEBUG - Checking waiting transitions: {'transition-CombineTextComponent-*************'}
2025-06-30 15:23:24 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-30 15:23:24 - StateManager - INFO - Cleared 1 pending transitions: {'transition-LoopNode-*************'}
2025-06-30 15:23:24 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-30 15:23:24 - StateManager - INFO - Terminated: False
2025-06-30 15:23:24 - StateManager - INFO - Pending transitions (0): []
2025-06-30 15:23:24 - StateManager - INFO - Waiting transitions (1): ['transition-CombineTextComponent-*************']
2025-06-30 15:23:24 - StateManager - INFO - Completed transitions (2): ['transition-AgenticAI-*************', 'transition-AgenticAI-*************']
2025-06-30 15:23:24 - StateManager - INFO - Results stored for 2 transitions
2025-06-30 15:23:24 - StateManager - DEBUG - Workflow active: {'transition-CombineTextComponent-*************'}
2025-06-30 15:23:24 - StateManager - INFO - Workflow status: active
2025-06-30 15:23:24 - StateManager - DEBUG - Workflow active: {'transition-CombineTextComponent-*************'}
2025-06-30 15:23:24 - StateManager - INFO - Workflow status: active
2025-06-30 15:23:24 - StateManager - INFO - Workflow paused: False
2025-06-30 15:23:24 - StateManager - INFO - ==============================
2025-06-30 15:23:24 - TransitionHandler - INFO - Starting parallel execution of transition: transition-LoopNode-*************
2025-06-30 15:23:24 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 8, corr_id 031734a8-757f-40e3-8a28-38476aeddd0f):
2025-06-30 15:23:24 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 031734a8-757f-40e3-8a28-38476aeddd0f, response: {'result': 'Starting execution of transition: transition-LoopNode-*************', 'message': 'Starting execution...', 'transition_id': 'transition-LoopNode-*************', 'status': 'started', 'sequence': 8, 'workflow_status': 'running'}
2025-06-30 15:23:24 - TransitionHandler - EXECUTE - Transition 'transition-LoopNode-*************' (type=standard, execution_type=loop)
2025-06-30 15:23:24 - LoopExecutor - DEBUG - 🔗 Orchestration engine set for loop executor integration
2025-06-30 15:23:24 - TransitionHandler - DEBUG - 🔗 Set orchestration engine for loop executor
2025-06-30 15:23:24 - TransitionHandler - INFO - Using KafkaToolExecutor for execution_type: loop
2025-06-30 15:23:24 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-LoopNode-*************
2025-06-30 15:23:24 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: loop
2025-06-30 15:23:24 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for loop node
2025-06-30 15:23:25 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-06-30 15:23:25 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-06-30 15:23:25 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-06-30 15:23:25 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-06-30 15:23:25 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 1)
2025-06-30 15:23:25 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '0'}
2025-06-30 15:23:25 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-30 15:23:25 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: 0
2025-06-30 15:23:25 - WorkflowUtils - DEBUG - Found result.result: 0 (type: <class 'str'>)
2025-06-30 15:23:25 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-30 15:23:25 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-06-30 15:23:25 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-30 15:23:25 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-30 15:23:25 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '0'}
2025-06-30 15:23:25 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-30 15:23:25 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: 0
2025-06-30 15:23:25 - WorkflowUtils - DEBUG - Found result.result: 0 (type: <class 'str'>)
2025-06-30 15:23:25 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-30 15:23:25 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-06-30 15:23:25 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → start via path 'result': 0
2025-06-30 15:23:25 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-30 15:23:25 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-30 15:23:25 - WorkflowUtils - DEBUG - Filtering out field 'iteration_list' with empty collection: []
2025-06-30 15:23:25 - WorkflowUtils - DEBUG - Filtering out field 'start' with null/empty value: None
2025-06-30 15:23:25 - WorkflowUtils - INFO - 🧹 Parameter filtering: 14 → 12 fields (2 null/empty fields removed)
2025-06-30 15:23:25 - TransitionHandler - DEBUG - 📌 Added static parameter: source_type = number_range
2025-06-30 15:23:25 - TransitionHandler - DEBUG - 📌 Added static parameter: batch_size = 1
2025-06-30 15:23:25 - TransitionHandler - DEBUG - 📌 Added static parameter: end = 6
2025-06-30 15:23:25 - TransitionHandler - DEBUG - 📌 Added static parameter: step = 1
2025-06-30 15:23:25 - TransitionHandler - DEBUG - 📌 Added static parameter: parallel_execution = False
2025-06-30 15:23:25 - TransitionHandler - DEBUG - 📌 Added static parameter: max_concurrent = 3
2025-06-30 15:23:25 - TransitionHandler - DEBUG - 📌 Added static parameter: preserve_order = True
2025-06-30 15:23:25 - TransitionHandler - DEBUG - 📌 Added static parameter: iteration_timeout = 60
2025-06-30 15:23:25 - TransitionHandler - DEBUG - 📌 Added static parameter: aggregation_type = collect_all
2025-06-30 15:23:25 - TransitionHandler - DEBUG - 📌 Added static parameter: include_metadata = False
2025-06-30 15:23:25 - TransitionHandler - DEBUG - 📌 Added static parameter: on_iteration_error = retry_once
2025-06-30 15:23:25 - TransitionHandler - DEBUG - 📌 Added static parameter: include_errors = True
2025-06-30 15:23:25 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'start': '0', 'source_type': 'number_range', 'batch_size': '1', 'end': '6', 'step': '1', 'parallel_execution': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': False, 'on_iteration_error': 'retry_once', 'include_errors': True}
2025-06-30 15:23:25 - TransitionHandler - DEBUG - tool Parameters: {'start': '0', 'source_type': 'number_range', 'batch_size': '1', 'end': '6', 'step': '1', 'parallel_execution': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': False, 'on_iteration_error': 'retry_once', 'include_errors': True}
2025-06-30 15:23:25 - TransitionHandler - INFO - Invoking tool 'LoopNode' (tool_id: 1) for node 'LoopNode' in transition 'transition-LoopNode-*************' with parameters: {'start': '0', 'source_type': 'number_range', 'batch_size': '1', 'end': '6', 'step': '1', 'parallel_execution': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': False, 'on_iteration_error': 'retry_once', 'include_errors': True}
2025-06-30 15:23:25 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 9, corr_id 031734a8-757f-40e3-8a28-38476aeddd0f):
2025-06-30 15:23:25 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 031734a8-757f-40e3-8a28-38476aeddd0f, response: {'transition_id': 'transition-LoopNode-*************', 'node_id': 'LoopNode', 'tool_name': 'LoopNode', 'message': 'Connecting to server', 'result': 'Connecting to server LoopNode', 'status': 'connecting', 'sequence': 9, 'workflow_status': 'running'}
2025-06-30 15:23:25 - TransitionHandler - DEBUG - 🔄 Resolving loop config parameters for transition: transition-LoopNode-*************
2025-06-30 15:23:25 - TransitionHandler - DEBUG - ✅ Loop config parameter resolution completed for transition: transition-LoopNode-*************
2025-06-30 15:23:25 - TransitionHandler - DEBUG - 🔍 Detected exit transition: transition-MergeDataComponent-************* (has final/aggregated indicators)
2025-06-30 15:23:25 - TransitionHandler - DEBUG - 🔍 Auto-detected loop body transition: transition-CombineTextComponent-************* (has current_item/iteration indicators)
2025-06-30 15:23:25 - TransitionHandler - INFO - 🔍 Auto-detected and added loop body transitions to config: ['transition-CombineTextComponent-*************']
2025-06-30 15:23:25 - LoopExecutor - INFO - 🚨 execute_tool called with transition_id: transition-LoopNode-*************
2025-06-30 15:23:25 - LoopExecutor - INFO - 🔄 Starting loop node execution for transition: transition-LoopNode-*************
2025-06-30 15:23:25 - LoopExecutor - DEBUG - 🔢 Detected number_range from 1 to 6 (step: 1), batch_size: 1
2025-06-30 15:23:25 - LoopExecutor - DEBUG - Parsed loop config: {'loop_type': 'context_independent', 'aggregation_config': {'type': 'list'}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': False}, 'iteration_source': {'type': 'range', 'data': {'start': 1, 'stop': 7, 'step': 1}, 'batch_size': 1, 'original': {'number_range': {'start': 1, 'end': 6}, 'step': 1}}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'concurrency': {'enabled': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'retry_config': {'max_retries': 1, 'retry_delay': 1.0}, 'iteration_behavior': 'independent', 'iteration_settings': {'parallel_execution': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'error_handling': {'on_iteration_error': 'retry_once', 'include_errors': True}, 'loop_body_transitions': ['transition-CombineTextComponent-*************'], 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-CombineTextComponent-1750920624318'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}}
2025-06-30 15:23:25 - LoopExecutor - DEBUG - Loop configuration validation passed
2025-06-30 15:23:25 - TransitionHandler - DEBUG - 📝 Registered loop executor for transition: transition-LoopNode-*************
2025-06-30 15:23:25 - LoopExecutor - DEBUG - 📝 Registered loop executor with transition handler for transition: transition-LoopNode-*************
2025-06-30 15:23:25 - LoopExecutor - INFO - Prepared 6 iterations from range source
2025-06-30 15:23:25 - LoopExecutor - INFO - 🔄 Initialized loop state for 6 iterations
2025-06-30 15:23:25 - LoopExecutor - INFO - 🚨 _execute_loop_workflow called
2025-06-30 15:23:25 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_state keys: ['loop_config', 'transition_id', 'iteration_data', 'loop_context', 'input_data']
2025-06-30 15:23:25 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_config keys: ['loop_type', 'aggregation_config', 'result_aggregation', 'iteration_source', 'exit_condition', 'concurrency', 'retry_config', 'iteration_behavior', 'iteration_settings', 'error_handling', 'loop_body_transitions', 'loop_body_configuration']
2025-06-30 15:23:25 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_body_config: {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-CombineTextComponent-1750920624318'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}
2025-06-30 15:23:25 - LoopExecutor - DEBUG - 🔍 DEBUG: entry_transitions: ['transition-CombineTextComponent-*************']
2025-06-30 15:23:25 - LoopExecutor - DEBUG - 🔍 DEBUG: exit_transitions: ['transition-CombineTextComponent-1750920624318']
2025-06-30 15:23:25 - LoopExecutor - INFO - 🔄 Starting iteration 1/6
2025-06-30 15:23:25 - LoopExecutor - INFO - 🚨 _execute_loop_body_chain called for iteration 0
2025-06-30 15:23:25 - LoopExecutor - DEBUG - 🔍 Current loop config keys: ['loop_type', 'aggregation_config', 'result_aggregation', 'iteration_source', 'exit_condition', 'concurrency', 'retry_config', 'iteration_behavior', 'iteration_settings', 'error_handling', 'loop_body_transitions', 'loop_body_configuration']
2025-06-30 15:23:25 - LoopExecutor - DEBUG - 🔍 Loop body config: {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-CombineTextComponent-1750920624318'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}
2025-06-30 15:23:25 - LoopExecutor - DEBUG - 🔍 Loop body transitions: ['transition-CombineTextComponent-*************']
2025-06-30 15:23:25 - LoopExecutor - ERROR - ❌ Loop body chain execution failed for iteration 0: 'WorkflowStateManager' object has no attribute 'store_result'
2025-06-30 15:23:25 - LoopExecutor - ERROR - ❌ Iteration 1 failed: 'WorkflowStateManager' object has no attribute 'store_result'
2025-06-30 15:23:25 - LoopExecutor - INFO - 🔄 Starting iteration 2/6
2025-06-30 15:23:25 - LoopExecutor - INFO - 🚨 _execute_loop_body_chain called for iteration 1
2025-06-30 15:23:25 - LoopExecutor - DEBUG - 🔍 Current loop config keys: ['loop_type', 'aggregation_config', 'result_aggregation', 'iteration_source', 'exit_condition', 'concurrency', 'retry_config', 'iteration_behavior', 'iteration_settings', 'error_handling', 'loop_body_transitions', 'loop_body_configuration']
2025-06-30 15:23:25 - LoopExecutor - DEBUG - 🔍 Loop body config: {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-CombineTextComponent-1750920624318'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}
2025-06-30 15:23:25 - LoopExecutor - DEBUG - 🔍 Loop body transitions: ['transition-CombineTextComponent-*************']
2025-06-30 15:23:25 - LoopExecutor - ERROR - ❌ Loop body chain execution failed for iteration 1: 'WorkflowStateManager' object has no attribute 'store_result'
2025-06-30 15:23:25 - LoopExecutor - ERROR - ❌ Iteration 2 failed: 'WorkflowStateManager' object has no attribute 'store_result'
2025-06-30 15:23:25 - LoopExecutor - INFO - 🔄 Starting iteration 3/6
2025-06-30 15:23:25 - LoopExecutor - INFO - 🚨 _execute_loop_body_chain called for iteration 2
2025-06-30 15:23:25 - LoopExecutor - DEBUG - 🔍 Current loop config keys: ['loop_type', 'aggregation_config', 'result_aggregation', 'iteration_source', 'exit_condition', 'concurrency', 'retry_config', 'iteration_behavior', 'iteration_settings', 'error_handling', 'loop_body_transitions', 'loop_body_configuration']
2025-06-30 15:23:25 - LoopExecutor - DEBUG - 🔍 Loop body config: {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-CombineTextComponent-1750920624318'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}
2025-06-30 15:23:25 - LoopExecutor - DEBUG - 🔍 Loop body transitions: ['transition-CombineTextComponent-*************']
2025-06-30 15:23:25 - LoopExecutor - ERROR - ❌ Loop body chain execution failed for iteration 2: 'WorkflowStateManager' object has no attribute 'store_result'
2025-06-30 15:23:25 - LoopExecutor - ERROR - ❌ Iteration 3 failed: 'WorkflowStateManager' object has no attribute 'store_result'
2025-06-30 15:23:25 - LoopExecutor - INFO - 🔄 Starting iteration 4/6
2025-06-30 15:23:25 - LoopExecutor - INFO - 🚨 _execute_loop_body_chain called for iteration 3
2025-06-30 15:23:25 - LoopExecutor - DEBUG - 🔍 Current loop config keys: ['loop_type', 'aggregation_config', 'result_aggregation', 'iteration_source', 'exit_condition', 'concurrency', 'retry_config', 'iteration_behavior', 'iteration_settings', 'error_handling', 'loop_body_transitions', 'loop_body_configuration']
2025-06-30 15:23:25 - LoopExecutor - DEBUG - 🔍 Loop body config: {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-CombineTextComponent-1750920624318'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}
2025-06-30 15:23:25 - LoopExecutor - DEBUG - 🔍 Loop body transitions: ['transition-CombineTextComponent-*************']
2025-06-30 15:23:25 - LoopExecutor - ERROR - ❌ Loop body chain execution failed for iteration 3: 'WorkflowStateManager' object has no attribute 'store_result'
2025-06-30 15:23:25 - LoopExecutor - ERROR - ❌ Iteration 4 failed: 'WorkflowStateManager' object has no attribute 'store_result'
2025-06-30 15:23:25 - LoopExecutor - INFO - 🔄 Starting iteration 5/6
2025-06-30 15:23:25 - LoopExecutor - INFO - 🚨 _execute_loop_body_chain called for iteration 4
2025-06-30 15:23:25 - LoopExecutor - DEBUG - 🔍 Current loop config keys: ['loop_type', 'aggregation_config', 'result_aggregation', 'iteration_source', 'exit_condition', 'concurrency', 'retry_config', 'iteration_behavior', 'iteration_settings', 'error_handling', 'loop_body_transitions', 'loop_body_configuration']
2025-06-30 15:23:25 - LoopExecutor - DEBUG - 🔍 Loop body config: {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-CombineTextComponent-1750920624318'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}
2025-06-30 15:23:25 - LoopExecutor - DEBUG - 🔍 Loop body transitions: ['transition-CombineTextComponent-*************']
2025-06-30 15:23:25 - LoopExecutor - ERROR - ❌ Loop body chain execution failed for iteration 4: 'WorkflowStateManager' object has no attribute 'store_result'
2025-06-30 15:23:25 - LoopExecutor - ERROR - ❌ Iteration 5 failed: 'WorkflowStateManager' object has no attribute 'store_result'
2025-06-30 15:23:25 - LoopExecutor - INFO - 🔄 Starting iteration 6/6
2025-06-30 15:23:25 - LoopExecutor - INFO - 🚨 _execute_loop_body_chain called for iteration 5
2025-06-30 15:23:25 - LoopExecutor - DEBUG - 🔍 Current loop config keys: ['loop_type', 'aggregation_config', 'result_aggregation', 'iteration_source', 'exit_condition', 'concurrency', 'retry_config', 'iteration_behavior', 'iteration_settings', 'error_handling', 'loop_body_transitions', 'loop_body_configuration']
2025-06-30 15:23:25 - LoopExecutor - DEBUG - 🔍 Loop body config: {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-CombineTextComponent-1750920624318'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}
2025-06-30 15:23:25 - LoopExecutor - DEBUG - 🔍 Loop body transitions: ['transition-CombineTextComponent-*************']
2025-06-30 15:23:25 - LoopExecutor - ERROR - ❌ Loop body chain execution failed for iteration 5: 'WorkflowStateManager' object has no attribute 'store_result'
2025-06-30 15:23:25 - LoopExecutor - ERROR - ❌ Iteration 6 failed: 'WorkflowStateManager' object has no attribute 'store_result'
2025-06-30 15:23:25 - LoopExecutor - INFO - 🔄 CALLING NEW AGGREGATION METHOD with 0 iteration results
2025-06-30 15:23:25 - LoopExecutor - DEBUG - 🔄 Aggregating 0 iteration results using aggregation_type: collect_all
2025-06-30 15:23:25 - LoopExecutor - DEBUG - 🔍 Extracted 0 data items from iterations
2025-06-30 15:23:25 - LoopExecutor - DEBUG - 🔍 Sample extracted data: None
2025-06-30 15:23:25 - LoopExecutor - INFO - 🔄 NEW AGGREGATION METHOD RETURNED: []
2025-06-30 15:23:25 - LoopExecutor - INFO - 📊 Loop execution completed with metadata: {'success': True, 'final_results': [], 'iteration_count': 0, 'total_iterations': 6}
2025-06-30 15:23:25 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 10, corr_id 031734a8-757f-40e3-8a28-38476aeddd0f):
2025-06-30 15:23:25 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 031734a8-757f-40e3-8a28-38476aeddd0f, response: {'status': 'loop_completed', 'metadata': {'success': True, 'final_results': [], 'iteration_count': 0, 'total_iterations': 6}, 'transition_id': 'transition-LoopNode-*************', 'sequence': 10, 'workflow_status': 'running'}
2025-06-30 15:23:25 - LoopExecutor - INFO - ✅ Loop node execution completed for transition: transition-LoopNode-*************
2025-06-30 15:23:25 - TransitionHandler - DEBUG - 🗑️ Unregistered loop executor for transition: transition-LoopNode-*************
2025-06-30 15:23:25 - LoopExecutor - DEBUG - 🗑️ Unregistered loop executor from transition handler for transition: transition-LoopNode-*************
2025-06-30 15:23:25 - TransitionHandler - INFO - Execution result from loop executor: {
  "final_results": []
}
2025-06-30 15:23:25 - TransitionHandler - INFO - Checking execution result for errors: {
  "final_results": []
}
2025-06-30 15:23:25 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 11, corr_id 031734a8-757f-40e3-8a28-38476aeddd0f):
2025-06-30 15:23:25 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 031734a8-757f-40e3-8a28-38476aeddd0f, response: {'transition_id': 'transition-LoopNode-*************', 'node_id': 'LoopNode', 'tool_name': 'LoopNode', 'message': 'Transition Result received.', 'result': {'final_results': []}, 'status': 'completed', 'sequence': 11, 'workflow_status': 'running', 'approval_required': False}
2025-06-30 15:23:25 - TransitionHandler - INFO - 🔄 Handling loop completion for transition: transition-LoopNode-*************
2025-06-30 15:23:25 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in memory: {'final_results': []}
2025-06-30 15:23:25 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-LoopNode-*************'
2025-06-30 15:23:26 - RedisManager - DEBUG - Set key 'result:transition-LoopNode-*************' with TTL of 300 seconds
2025-06-30 15:23:26 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 15:23:26 - StateManager - INFO - Marked transition transition-LoopNode-************* as completed (was_pending=False, was_waiting=False)
2025-06-30 15:23:26 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'transition-LoopNode-*************', 'transition-AgenticAI-*************', 'transition-AgenticAI-*************'}
2025-06-30 15:23:26 - TransitionHandler - DEBUG - 🔍 Detected exit transition: transition-MergeDataComponent-************* (has final/aggregated indicators)
2025-06-30 15:23:26 - TransitionHandler - DEBUG - 🔍 Auto-detected loop body transition: transition-CombineTextComponent-************* (has current_item/iteration indicators)
2025-06-30 15:23:26 - TransitionHandler - INFO - 🔍 Auto-detected loop body transitions: ['transition-CombineTextComponent-*************']
2025-06-30 15:23:26 - TransitionHandler - DEBUG - 🔍 Loop completion filtering - Loop config: {'iteration_behavior': 'independent', 'iteration_source': {'number_range': {'start': 1, 'end': 6}, 'step': 1}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'iteration_settings': {'parallel_execution': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': False}, 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-CombineTextComponent-1750920624318'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}, 'error_handling': {'on_iteration_error': 'retry_once', 'include_errors': True}}
2025-06-30 15:23:26 - TransitionHandler - DEBUG - 🔍 Loop body transitions to filter: ['transition-CombineTextComponent-*************']
2025-06-30 15:23:26 - TransitionHandler - DEBUG - 🔍 Output data configs: [{'to_transition_id': 'transition-MergeDataComponent-*************', 'target_node_id': 'Merge Data', 'data_type': 'string', 'output_handle_registry': {'handle_mappings': [{'handle_id': 'final_results', 'result_path': 'final_results', 'edge_id': 'reactflow__edge-LoopNode-*************final_results-MergeDataComponent-*************main_input'}]}}, {'to_transition_id': 'transition-CombineTextComponent-*************', 'target_node_id': 'Combine Text', 'data_type': 'string', 'output_handle_registry': {'handle_mappings': [{'handle_id': 'current_item', 'result_path': 'current_item', 'edge_id': 'reactflow__edge-LoopNode-*************current_item-CombineTextComponent-*************main_input'}]}}]
2025-06-30 15:23:26 - TransitionHandler - DEBUG - ✅ Adding exit transition 'transition-MergeDataComponent-*************' to next transitions
2025-06-30 15:23:26 - TransitionHandler - DEBUG - 🚫 Skipping loop body transition 'transition-CombineTextComponent-*************' - should have been executed internally by loop executor
2025-06-30 15:23:26 - TransitionHandler - INFO - ✅ Loop completion handled for transition-LoopNode-*************. Next transitions: ['transition-MergeDataComponent-*************']
2025-06-30 15:23:26 - TransitionHandler - INFO - Completed transition transition-LoopNode-************* in 1.57 seconds
2025-06-30 15:23:26 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 12, corr_id 031734a8-757f-40e3-8a28-38476aeddd0f):
2025-06-30 15:23:26 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 031734a8-757f-40e3-8a28-38476aeddd0f, response: {'result': 'Completed transition in 1.57 seconds', 'message': 'Transition completed in 1.57 seconds', 'transition_id': 'transition-LoopNode-*************', 'status': 'time_logged', 'sequence': 12, 'workflow_status': 'running'}
2025-06-30 15:23:26 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-LoopNode-*************: ['transition-MergeDataComponent-*************']
2025-06-30 15:23:26 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 1
2025-06-30 15:23:26 - EnhancedWorkflowEngine - DEBUG - Results: [['transition-MergeDataComponent-*************']]
2025-06-30 15:23:26 - EnhancedWorkflowEngine - DEBUG - 🔄 Orchestration engine received result for transition-LoopNode-*************: ['transition-MergeDataComponent-*************']
2025-06-30 15:23:26 - EnhancedWorkflowEngine - DEBUG - 🔄 Result type: <class 'list'>, is_list: True
2025-06-30 15:23:26 - EnhancedWorkflowEngine - INFO - Transition transition-LoopNode-************* completed successfully: 1 next transitions
2025-06-30 15:23:26 - EnhancedWorkflowEngine - DEBUG - 🔄 Added to next_transitions_to_execute: ['transition-MergeDataComponent-*************']
2025-06-30 15:23:26 - TransitionHandler - INFO - Resolved next transitions (direct transition IDs): ['transition-MergeDataComponent-*************']
2025-06-30 15:23:26 - EnhancedWorkflowEngine - INFO - Adding transition transition-MergeDataComponent-************* to pending (all dependencies met)
2025-06-30 15:23:26 - StateManager - DEBUG - Workflow active: {'transition-MergeDataComponent-*************'}
2025-06-30 15:23:26 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:031734a8-757f-40e3-8a28-38476aeddd0f'
2025-06-30 15:23:26 - RedisManager - DEBUG - Set key 'workflow_state:031734a8-757f-40e3-8a28-38476aeddd0f' with TTL of 600 seconds
2025-06-30 15:23:26 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 031734a8-757f-40e3-8a28-38476aeddd0f. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 15:23:26 - StateManager - DEBUG - Checking waiting transitions: {'transition-CombineTextComponent-*************'}
2025-06-30 15:23:26 - StateManager - DEBUG - Transition transition-CombineTextComponent-************* is now ready (dependencies met: ['transition-AgenticAI-*************', 'transition-AgenticAI-*************', 'transition-LoopNode-*************'])
2025-06-30 15:23:26 - StateManager - INFO - Moved transitions from waiting to pending: {'transition-CombineTextComponent-*************'}
2025-06-30 15:23:26 - StateManager - DEBUG - Updated waiting=set(), pending={'transition-CombineTextComponent-*************', 'transition-MergeDataComponent-*************'}
2025-06-30 15:23:26 - StateManager - INFO - Cleared 2 pending transitions: {'transition-CombineTextComponent-*************', 'transition-MergeDataComponent-*************'}
2025-06-30 15:23:26 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-30 15:23:26 - StateManager - INFO - Terminated: False
2025-06-30 15:23:26 - StateManager - INFO - Pending transitions (0): []
2025-06-30 15:23:26 - StateManager - INFO - Waiting transitions (0): []
2025-06-30 15:23:26 - StateManager - INFO - Completed transitions (3): ['transition-AgenticAI-*************', 'transition-AgenticAI-*************', 'transition-LoopNode-*************']
2025-06-30 15:23:26 - StateManager - INFO - Results stored for 3 transitions
2025-06-30 15:23:26 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-30 15:23:26 - StateManager - INFO - Workflow status: inactive
2025-06-30 15:23:26 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-30 15:23:26 - StateManager - INFO - Workflow status: inactive
2025-06-30 15:23:26 - StateManager - INFO - Workflow paused: False
2025-06-30 15:23:26 - StateManager - INFO - ==============================
2025-06-30 15:23:26 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-30 15:23:26 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 13, corr_id 031734a8-757f-40e3-8a28-38476aeddd0f):
2025-06-30 15:23:26 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 031734a8-757f-40e3-8a28-38476aeddd0f, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 13, 'workflow_status': 'running'}
2025-06-30 15:23:26 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-06-30 15:23:26 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-30 15:23:26 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-30 15:23:26 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-30 15:23:26 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-30 15:23:27 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-30 15:23:27 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 15:23:27 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-06-30 15:23:27 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-06-30 15:23:27 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-06-30 15:23:27 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-06-30 15:23:28 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 15:23:28 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-30 15:23:28 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-06-30 15:23:28 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-06-30 15:23:28 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-06-30 15:23:28 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-06-30 15:23:30 - StateManager - DEBUG - Retrieved result for transition transition-LoopNode-************* from Redis
2025-06-30 15:23:30 - StateManager - DEBUG - Extracted results for 1 tools in transition transition-LoopNode-*************
2025-06-30 15:23:30 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-30 15:23:30 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 1)
2025-06-30 15:23:30 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 2)
2025-06-30 15:23:30 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-30 15:23:30 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': 'Nature 101 is likely a general introduction to the aspects and concepts of nature. It might cover topics such as ecosystems, plant and animal life, environmental science, and the importance of conservation. If you have specific questions or need information about a particular topic within nature, feel free to ask!'}
2025-06-30 15:23:30 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-30 15:23:30 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: Nature 101 is likely a general introduction to the aspects and concepts of nature. It might cover topics such as ecosystems, plant and animal life, environmental science, and the importance of conservation. If you have specific questions or need information about a particular topic within nature, feel free to ask!
2025-06-30 15:23:30 - WorkflowUtils - DEBUG - Found result.result: Nature 101 is likely a general introduction to the aspects and concepts of nature. It might cover topics such as ecosystems, plant and animal life, environmental science, and the importance of conservation. If you have specific questions or need information about a particular topic within nature, feel free to ask! (type: <class 'str'>)
2025-06-30 15:23:30 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-30 15:23:30 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-06-30 15:23:30 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': 'Nature 101 is likely a general introduction to the aspects and concepts of nature. It might cover topics such as ecosystems, plant and animal life, environmental science, and the importance of conservation. If you have specific questions or need information about a particular topic within nature, feel free to ask!'}
2025-06-30 15:23:30 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-30 15:23:30 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: Nature 101 is likely a general introduction to the aspects and concepts of nature. It might cover topics such as ecosystems, plant and animal life, environmental science, and the importance of conservation. If you have specific questions or need information about a particular topic within nature, feel free to ask!
2025-06-30 15:23:30 - WorkflowUtils - DEBUG - Found result.result: Nature 101 is likely a general introduction to the aspects and concepts of nature. It might cover topics such as ecosystems, plant and animal life, environmental science, and the importance of conservation. If you have specific questions or need information about a particular topic within nature, feel free to ask! (type: <class 'str'>)
2025-06-30 15:23:30 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-30 15:23:30 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-06-30 15:23:30 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'final_results': []}
2025-06-30 15:23:30 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['final_results']
2025-06-30 15:23:30 - WorkflowUtils - DEBUG - Could not find result.result or result in source_results for handle current_item
2025-06-30 15:23:30 - WorkflowUtils - DEBUG - Path tracking - Source results structure
2025-06-30 15:23:30 - WorkflowUtils - INFO - 🔍 Handle mapping validation: partially_compatible (2/3 compatible)
2025-06-30 15:23:30 - TransitionHandler - INFO - 🔍 Handle validation: partially_compatible (2/3 compatible)
2025-06-30 15:23:30 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': 'Nature 101 is likely a general introduction to the aspects and concepts of nature. It might cover topics such as ecosystems, plant and animal life, environmental science, and the importance of conservation. If you have specific questions or need information about a particular topic within nature, feel free to ask!'}
2025-06-30 15:23:30 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-30 15:23:30 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: Nature 101 is likely a general introduction to the aspects and concepts of nature. It might cover topics such as ecosystems, plant and animal life, environmental science, and the importance of conservation. If you have specific questions or need information about a particular topic within nature, feel free to ask!
2025-06-30 15:23:30 - WorkflowUtils - DEBUG - Found result.result: Nature 101 is likely a general introduction to the aspects and concepts of nature. It might cover topics such as ecosystems, plant and animal life, environmental science, and the importance of conservation. If you have specific questions or need information about a particular topic within nature, feel free to ask! (type: <class 'str'>)
2025-06-30 15:23:30 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-30 15:23:30 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-06-30 15:23:30 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → input_2 via path 'result': Nature 101 is likely a general introduction to the aspects and concepts of nature. It might cover topics such as ecosystems, plant and animal life, environmental science, and the importance of conservation. If you have specific questions or need information about a particular topic within nature, feel free to ask!
2025-06-30 15:23:30 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': 'Nature 101 is likely a general introduction to the aspects and concepts of nature. It might cover topics such as ecosystems, plant and animal life, environmental science, and the importance of conservation. If you have specific questions or need information about a particular topic within nature, feel free to ask!'}
2025-06-30 15:23:30 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-30 15:23:30 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: Nature 101 is likely a general introduction to the aspects and concepts of nature. It might cover topics such as ecosystems, plant and animal life, environmental science, and the importance of conservation. If you have specific questions or need information about a particular topic within nature, feel free to ask!
2025-06-30 15:23:30 - WorkflowUtils - DEBUG - Found result.result: Nature 101 is likely a general introduction to the aspects and concepts of nature. It might cover topics such as ecosystems, plant and animal life, environmental science, and the importance of conservation. If you have specific questions or need information about a particular topic within nature, feel free to ask! (type: <class 'str'>)
2025-06-30 15:23:30 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-30 15:23:30 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-06-30 15:23:30 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → input_1 via path 'result': Nature 101 is likely a general introduction to the aspects and concepts of nature. It might cover topics such as ecosystems, plant and animal life, environmental science, and the importance of conservation. If you have specific questions or need information about a particular topic within nature, feel free to ask!
2025-06-30 15:23:30 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'final_results': []}
2025-06-30 15:23:30 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['final_results']
2025-06-30 15:23:30 - WorkflowUtils - DEBUG - Could not find result.result or result in source_results for handle current_item
2025-06-30 15:23:30 - WorkflowUtils - DEBUG - Path tracking - Source results structure
2025-06-30 15:23:30 - WorkflowUtils - WARNING - ❌ Handle mapping failed: current_item → main_input (no data found)
2025-06-30 15:23:30 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 2/3 successful
2025-06-30 15:23:30 - WorkflowUtils - WARNING - ⚠️ 1 universal handle mappings failed - this may cause tool execution errors
2025-06-30 15:23:30 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 2/3 successful
2025-06-30 15:23:30 - TransitionHandler - WARNING - ❌ Failed mapping: current_item → main_input (Error: No data found for handle current_item)
2025-06-30 15:23:30 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-30 15:23:30 - WorkflowUtils - DEBUG - Filtering out field 'input_1' with null/empty value: None
2025-06-30 15:23:30 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: None
2025-06-30 15:23:30 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-06-30 15:23:30 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-06-30 15:23:30 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-06-30 15:23:30 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-06-30 15:23:30 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-06-30 15:23:30 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-06-30 15:23:30 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-06-30 15:23:30 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-06-30 15:23:30 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 2 fields (11 null/empty fields removed)
2025-06-30 15:23:30 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-30 15:23:30 - TransitionHandler - DEBUG - 📌 Added static parameter: separator = \n
2025-06-30 15:23:30 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'input_2': 'Nature 101 is likely a general introduction to the aspects and concepts of nature. It might cover topics such as ecosystems, plant and animal life, environmental science, and the importance of conservation. If you have specific questions or need information about a particular topic within nature, feel free to ask!', 'input_1': 'Nature 101 is likely a general introduction to the aspects and concepts of nature. It might cover topics such as ecosystems, plant and animal life, environmental science, and the importance of conservation. If you have specific questions or need information about a particular topic within nature, feel free to ask!', 'num_additional_inputs': '1', 'separator': '\\n'}
2025-06-30 15:23:30 - TransitionHandler - DEBUG - tool Parameters: {'input_2': 'Nature 101 is likely a general introduction to the aspects and concepts of nature. It might cover topics such as ecosystems, plant and animal life, environmental science, and the importance of conservation. If you have specific questions or need information about a particular topic within nature, feel free to ask!', 'input_1': 'Nature 101 is likely a general introduction to the aspects and concepts of nature. It might cover topics such as ecosystems, plant and animal life, environmental science, and the importance of conservation. If you have specific questions or need information about a particular topic within nature, feel free to ask!', 'num_additional_inputs': '1', 'separator': '\\n'}
2025-06-30 15:23:30 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'input_2': 'Nature 101 is likely a general introduction to the aspects and concepts of nature. It might cover topics such as ecosystems, plant and animal life, environmental science, and the importance of conservation. If you have specific questions or need information about a particular topic within nature, feel free to ask!', 'input_1': 'Nature 101 is likely a general introduction to the aspects and concepts of nature. It might cover topics such as ecosystems, plant and animal life, environmental science, and the importance of conservation. If you have specific questions or need information about a particular topic within nature, feel free to ask!', 'num_additional_inputs': '1', 'separator': '\\n'}
2025-06-30 15:23:30 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 14, corr_id 031734a8-757f-40e3-8a28-38476aeddd0f):
2025-06-30 15:23:30 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 031734a8-757f-40e3-8a28-38476aeddd0f, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 14, 'workflow_status': 'running'}
2025-06-30 15:23:30 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: aebde075-613d-44ef-a711-16659b0fb10f) using provided producer.
2025-06-30 15:23:30 - NodeExecutor - DEBUG - Added correlation_id 031734a8-757f-40e3-8a28-38476aeddd0f to payload
2025-06-30 15:23:30 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-06-30 15:23:30 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'input_2': 'Nature 101 is likely a general introduction to the aspects and concepts of nature. It might cover topics such as ecosystems, plant and animal life, environmental science, and the importance of conservation. If you have specific questions or need information about a particular topic within nature, feel free to ask!', 'input_1': 'Nature 101 is likely a general introduction to the aspects and concepts of nature. It might cover topics such as ecosystems, plant and animal life, environmental science, and the importance of conservation. If you have specific questions or need information about a particular topic within nature, feel free to ask!', 'num_additional_inputs': '1', 'separator': '\\n'}, 'request_id': 'aebde075-613d-44ef-a711-16659b0fb10f', 'correlation_id': '031734a8-757f-40e3-8a28-38476aeddd0f', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-06-30 15:23:30 - NodeExecutor - DEBUG - Request aebde075-613d-44ef-a711-16659b0fb10f sent successfully using provided producer.
2025-06-30 15:23:30 - NodeExecutor - DEBUG - Waiting indefinitely for result for request aebde075-613d-44ef-a711-16659b0fb10f...
2025-06-30 15:23:30 - TransitionHandler - INFO - Starting parallel execution of transition: transition-MergeDataComponent-*************
2025-06-30 15:23:30 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 15, corr_id 031734a8-757f-40e3-8a28-38476aeddd0f):
2025-06-30 15:23:30 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 031734a8-757f-40e3-8a28-38476aeddd0f, response: {'result': 'Starting execution of transition: transition-MergeDataComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-MergeDataComponent-*************', 'status': 'started', 'sequence': 15, 'workflow_status': 'running'}
2025-06-30 15:23:30 - TransitionHandler - EXECUTE - Transition 'transition-MergeDataComponent-*************' (type=standard, execution_type=Components)
2025-06-30 15:23:30 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-30 15:23:30 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-MergeDataComponent-*************
2025-06-30 15:23:30 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-30 15:23:30 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-30 15:23:31 - StateManager - DEBUG - Retrieved result for transition transition-LoopNode-************* from Redis
2025-06-30 15:23:31 - StateManager - DEBUG - Extracted results for 1 tools in transition transition-LoopNode-*************
2025-06-30 15:23:31 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-30 15:23:31 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-30 15:23:31 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_results': {'final_results': []}
2025-06-30 15:23:31 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['final_results']
2025-06-30 15:23:31 - WorkflowUtils - DEBUG - Path tracking - Found handle 'final_results' directly in source_results
2025-06-30 15:23:31 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-30 15:23:31 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-30 15:23:31 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_results': {'final_results': []}
2025-06-30 15:23:31 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['final_results']
2025-06-30 15:23:31 - WorkflowUtils - DEBUG - Path tracking - Found handle 'final_results' directly in source_results
2025-06-30 15:23:31 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_results → main_input via path 'final_results': []
2025-06-30 15:23:31 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-30 15:23:31 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-30 15:23:31 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-30 15:23:31 - WorkflowUtils - DEBUG - Filtering out field 'output_key_1' with null/empty value: 
2025-06-30 15:23:31 - WorkflowUtils - DEBUG - Filtering out field 'output_key_2' with null/empty value: 
2025-06-30 15:23:31 - WorkflowUtils - DEBUG - Filtering out field 'output_key_3' with null/empty value: 
2025-06-30 15:23:31 - WorkflowUtils - DEBUG - Filtering out field 'output_key_4' with null/empty value: 
2025-06-30 15:23:31 - WorkflowUtils - DEBUG - Filtering out field 'output_key_5' with null/empty value: 
2025-06-30 15:23:31 - WorkflowUtils - DEBUG - Filtering out field 'output_key_6' with null/empty value: 
2025-06-30 15:23:31 - WorkflowUtils - DEBUG - Filtering out field 'output_key_7' with null/empty value: 
2025-06-30 15:23:31 - WorkflowUtils - DEBUG - Filtering out field 'output_key_8' with null/empty value: 
2025-06-30 15:23:31 - WorkflowUtils - DEBUG - Filtering out field 'output_key_9' with null/empty value: 
2025-06-30 15:23:31 - WorkflowUtils - DEBUG - Filtering out field 'output_key_10' with null/empty value: 
2025-06-30 15:23:31 - WorkflowUtils - DEBUG - Filtering out field 'output_key_11' with null/empty value: 
2025-06-30 15:23:31 - WorkflowUtils - DEBUG - Filtering out field 'input_1' with empty collection: {}
2025-06-30 15:23:31 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with empty collection: {}
2025-06-30 15:23:31 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with empty collection: {}
2025-06-30 15:23:31 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with empty collection: {}
2025-06-30 15:23:31 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with empty collection: {}
2025-06-30 15:23:31 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with empty collection: {}
2025-06-30 15:23:31 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with empty collection: {}
2025-06-30 15:23:31 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with empty collection: {}
2025-06-30 15:23:31 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with empty collection: {}
2025-06-30 15:23:31 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with empty collection: {}
2025-06-30 15:23:31 - WorkflowUtils - INFO - 🧹 Parameter filtering: 24 → 2 fields (22 null/empty fields removed)
2025-06-30 15:23:31 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-30 15:23:31 - TransitionHandler - DEBUG - 📌 Added static parameter: merge_strategy = Aggregate
2025-06-30 15:23:31 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'main_input': [], 'num_additional_inputs': '1', 'merge_strategy': 'Aggregate'}
2025-06-30 15:23:31 - TransitionHandler - DEBUG - tool Parameters: {'main_input': [], 'num_additional_inputs': '1', 'merge_strategy': 'Aggregate'}
2025-06-30 15:23:31 - TransitionHandler - INFO - Invoking tool 'MergeDataComponent' (tool_id: 1) for node 'MergeDataComponent' in transition 'transition-MergeDataComponent-*************' with parameters: {'main_input': [], 'num_additional_inputs': '1', 'merge_strategy': 'Aggregate'}
2025-06-30 15:23:31 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 16, corr_id 031734a8-757f-40e3-8a28-38476aeddd0f):
2025-06-30 15:23:31 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 031734a8-757f-40e3-8a28-38476aeddd0f, response: {'transition_id': 'transition-MergeDataComponent-*************', 'node_id': 'MergeDataComponent', 'tool_name': 'MergeDataComponent', 'message': 'Connecting to server', 'result': 'Connecting to server MergeDataComponent', 'status': 'connecting', 'sequence': 16, 'workflow_status': 'running'}
2025-06-30 15:23:31 - NodeExecutor - INFO - Executing tool 'MergeDataComponent' via Kafka (request_id: 7a3e99a0-1301-4061-be3b-bf8a60f19eb0) using provided producer.
2025-06-30 15:23:31 - NodeExecutor - DEBUG - Added correlation_id 031734a8-757f-40e3-8a28-38476aeddd0f to payload
2025-06-30 15:23:31 - NodeExecutor - DEBUG - Added transition_id transition-MergeDataComponent-************* to payload
2025-06-30 15:23:31 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'MergeDataComponent', 'tool_parameters': {'main_input': [], 'num_additional_inputs': '1', 'merge_strategy': 'Aggregate'}, 'request_id': '7a3e99a0-1301-4061-be3b-bf8a60f19eb0', 'correlation_id': '031734a8-757f-40e3-8a28-38476aeddd0f', 'transition_id': 'transition-MergeDataComponent-*************'}
2025-06-30 15:23:31 - NodeExecutor - DEBUG - Request 7a3e99a0-1301-4061-be3b-bf8a60f19eb0 sent successfully using provided producer.
2025-06-30 15:23:31 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 7a3e99a0-1301-4061-be3b-bf8a60f19eb0...
2025-06-30 15:23:32 - NodeExecutor - DEBUG - Result consumer received message: Offset=954
2025-06-30 15:23:32 - NodeExecutor - WARNING - Received error response for request_id aebde075-613d-44ef-a711-16659b0fb10f: Error combining text for request_id aebde075-613d-44ef-a711-16659b0fb10f: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-06-30 15:23:32 - NodeExecutor - DEBUG - Result consumer received message: Offset=955
2025-06-30 15:23:32 - NodeExecutor - DEBUG - Received valid result for request_id 7a3e99a0-1301-4061-be3b-bf8a60f19eb0
2025-06-30 15:23:32 - NodeExecutor - ERROR - Error during node execution aebde075-613d-44ef-a711-16659b0fb10f: Node execution failed: Error combining text for request_id aebde075-613d-44ef-a711-16659b0fb10f: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id aebde075-613d-44ef-a711-16659b0fb10f: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-06-30 15:23:32 - TransitionHandler - ERROR - Tool execution failed for tool 'CombineTextComponent' (tool_id: 1) in node 'CombineTextComponent' of transition 'transition-CombineTextComponent-*************': Node execution failed: Error combining text for request_id aebde075-613d-44ef-a711-16659b0fb10f: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 492, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 361, in execute_tool
    raise e
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id aebde075-613d-44ef-a711-16659b0fb10f: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"

2025-06-30 15:23:32 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 17, corr_id 031734a8-757f-40e3-8a28-38476aeddd0f):
2025-06-30 15:23:32 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 031734a8-757f-40e3-8a28-38476aeddd0f, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition faced an error during execution.', 'result': '[ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id aebde075-613d-44ef-a711-16659b0fb10f: "Required field \'input_data\', \'input_datta\', or \'text_inputs\' not found in parameters"', 'status': 'failed', 'sequence': 17, 'workflow_status': 'running'}
2025-06-30 15:23:32 - TransitionHandler - ERROR - Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id aebde075-613d-44ef-a711-16659b0fb10f: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-06-30 15:23:32 - NodeExecutor - INFO - Result received for request 7a3e99a0-1301-4061-be3b-bf8a60f19eb0.
2025-06-30 15:23:32 - TransitionHandler - INFO - Execution result from Components executor: []
2025-06-30 15:23:32 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 18, corr_id 031734a8-757f-40e3-8a28-38476aeddd0f):
2025-06-30 15:23:32 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 031734a8-757f-40e3-8a28-38476aeddd0f, response: {'transition_id': 'transition-MergeDataComponent-*************', 'node_id': 'MergeDataComponent', 'tool_name': 'MergeDataComponent', 'message': 'Transition Result received.', 'result': [], 'status': 'completed', 'sequence': 18, 'workflow_status': 'running', 'approval_required': False}
2025-06-30 15:23:32 - StateManager - DEBUG - Stored result for transition transition-MergeDataComponent-************* in memory: {'MergeDataComponent': {'transition_id': 'transition-MergeDataComponent-*************', 'node_id': 'MergeDataComponent', 'tool_name': 'MergeDataComponent', 'result': {'result': []}, 'status': 'completed', 'timestamp': 1751277212.305074}}
2025-06-30 15:23:32 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-MergeDataComponent-*************'
2025-06-30 15:23:33 - RedisManager - DEBUG - Set key 'result:transition-MergeDataComponent-*************' with TTL of 300 seconds
2025-06-30 15:23:33 - StateManager - DEBUG - Stored result for transition transition-MergeDataComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 15:23:33 - StateManager - INFO - Marked transition transition-MergeDataComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-30 15:23:33 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-LoopNode-*************', 'transition-MergeDataComponent-*************', 'transition-AgenticAI-*************', 'transition-AgenticAI-*************'}
2025-06-30 15:23:33 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-MergeDataComponent-*************
2025-06-30 15:23:33 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'list'>
2025-06-30 15:23:33 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-30 15:23:33 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'list'>
2025-06-30 15:23:33 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-MergeDataComponent-*************:
2025-06-30 15:23:33 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-30 15:23:33 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-30 15:23:33 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-MergeDataComponent-*************, returning empty list
2025-06-30 15:23:33 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-MergeDataComponent-*************
2025-06-30 15:23:33 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 0
2025-06-30 15:23:33 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: []
2025-06-30 15:23:33 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-06-30 15:23:33 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-06-30 15:23:33 - TransitionHandler - DEBUG - 🔗 Final next_transitions: []
2025-06-30 15:23:33 - TransitionHandler - INFO - Completed transition transition-MergeDataComponent-************* in 3.03 seconds
2025-06-30 15:23:33 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 19, corr_id 031734a8-757f-40e3-8a28-38476aeddd0f):
2025-06-30 15:23:33 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 031734a8-757f-40e3-8a28-38476aeddd0f, response: {'result': 'Completed transition in 3.03 seconds', 'message': 'Transition completed in 3.03 seconds', 'transition_id': 'transition-MergeDataComponent-*************', 'status': 'time_logged', 'sequence': 19, 'workflow_status': 'running'}
2025-06-30 15:23:33 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-MergeDataComponent-*************: []
2025-06-30 15:23:33 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 0
2025-06-30 15:23:33 - EnhancedWorkflowEngine - DEBUG - Results: [Exception('Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id aebde075-613d-44ef-a711-16659b0fb10f: "Required field \'input_data\', \'input_datta\', or \'text_inputs\' not found in parameters"'), []]
2025-06-30 15:23:33 - EnhancedWorkflowEngine - DEBUG - 🔄 Orchestration engine received result for transition-CombineTextComponent-*************: Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id aebde075-613d-44ef-a711-16659b0fb10f: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-06-30 15:23:33 - EnhancedWorkflowEngine - DEBUG - 🔄 Result type: <class 'Exception'>, is_list: False
2025-06-30 15:23:33 - EnhancedWorkflowEngine - ERROR - Error in execution of transition transition-CombineTextComponent-*************: Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id aebde075-613d-44ef-a711-16659b0fb10f: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-06-30 15:23:33 - EnhancedWorkflowEngine - ERROR - Traceback for transition transition-CombineTextComponent-*************: NoneType: None

2025-06-30 15:23:33 - EnhancedWorkflowEngine - ERROR - An unexpected error occurred during parallel execution of standard transitions: Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id aebde075-613d-44ef-a711-16659b0fb10f: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-06-30 15:23:33 - EnhancedWorkflowEngine - ERROR - Traceback for unexpected error: Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 492, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 361, in execute_tool
    raise e
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id aebde075-613d-44ef-a711-16659b0fb10f: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 204, in _execute_transition_with_tracking
    result = await self._execute_standard_or_reflection_transition(transition)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 703, in _execute_standard_or_reflection_transition
    raise Exception(f"Tool execution error: {error_message}")
Exception: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id aebde075-613d-44ef-a711-16659b0fb10f: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/executor_core.py", line 267, in execute
    raise result
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 230, in _execute_transition_with_tracking
    raise Exception(f"Exception in transition {transition_id}: {str(e)}")
Exception: Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id aebde075-613d-44ef-a711-16659b0fb10f: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"

2025-06-30 15:23:33 - EnhancedWorkflowEngine - ERROR - An unexpected error occurred during workflow execution: Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id aebde075-613d-44ef-a711-16659b0fb10f: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-06-30 15:23:33 - EnhancedWorkflowEngine - ERROR - Traceback for unexpected error: Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 492, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 361, in execute_tool
    raise e
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id aebde075-613d-44ef-a711-16659b0fb10f: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 204, in _execute_transition_with_tracking
    result = await self._execute_standard_or_reflection_transition(transition)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 703, in _execute_standard_or_reflection_transition
    raise Exception(f"Tool execution error: {error_message}")
Exception: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id aebde075-613d-44ef-a711-16659b0fb10f: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/executor_core.py", line 285, in execute
    raise e
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/executor_core.py", line 267, in execute
    raise result
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 230, in _execute_transition_with_tracking
    raise Exception(f"Exception in transition {transition_id}: {str(e)}")
Exception: Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id aebde075-613d-44ef-a711-16659b0fb10f: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"

2025-06-30 15:23:33 - KafkaWorkflowConsumer - ERROR - Exception in workflow execution: Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id aebde075-613d-44ef-a711-16659b0fb10f: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-06-30 15:23:33 - KafkaWorkflowConsumer - INFO - Workflow '0c19c070-905e-46ef-9f57-62eb427bf396' final status: failed, result: Exception in workflow '0c19c070-905e-46ef-9f57-62eb427bf396': Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id aebde075-613d-44ef-a711-16659b0fb10f: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-06-30 15:23:33 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 031734a8-757f-40e3-8a28-38476aeddd0f, response: {'status': 'failed', 'result': 'Exception in workflow \'0c19c070-905e-46ef-9f57-62eb427bf396\': Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id aebde075-613d-44ef-a711-16659b0fb10f: "Required field \'input_data\', \'input_datta\', or \'text_inputs\' not found in parameters"', 'workflow_status': 'failed', 'error': 'Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id aebde075-613d-44ef-a711-16659b0fb10f: "Required field \'input_data\', \'input_datta\', or \'text_inputs\' not found in parameters"', 'error_type': 'Exception'}
2025-06-30 15:23:33 - KafkaWorkflowConsumer - DEBUG - Stopped workflow with correlation_id: 031734a8-757f-40e3-8a28-38476aeddd0f 
2025-06-30 15:24:27 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-30 15:24:27 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 15:24:27 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 15:24:27 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-30 15:24:32 - KafkaWorkflowConsumer - INFO - Consumer task cancelled.
2025-06-30 15:24:33 - MCPToolExecutor - INFO - Stopping KafkaToolExecutor internal consumer components...
2025-06-30 15:24:33 - MCPToolExecutor - DEBUG - Cancelling background consumer task...
2025-06-30 15:24:33 - MCPToolExecutor - INFO - Result consumer loop cancelled.
2025-06-30 15:24:33 - MCPToolExecutor - DEBUG - Stopping internal Kafka consumer...
2025-06-30 15:24:33 - MCPToolExecutor - INFO - Internal Kafka consumer stopped.
2025-06-30 15:24:33 - MCPToolExecutor - INFO - KafkaToolExecutor internal consumer stopped.
2025-06-30 15:24:33 - NodeExecutor - INFO - Stopping NodeExecutor internal consumer components...
2025-06-30 15:24:33 - NodeExecutor - DEBUG - Cancelling background consumer task...
2025-06-30 15:24:33 - NodeExecutor - INFO - Result consumer loop cancelled.
2025-06-30 15:24:33 - NodeExecutor - DEBUG - Stopping internal Kafka consumer...
2025-06-30 15:24:34 - NodeExecutor - INFO - Internal Kafka consumer stopped.
2025-06-30 15:24:34 - NodeExecutor - INFO - NodeExecutor internal consumer stopped.
2025-06-30 15:24:34 - AgentExecutor - INFO - Stopping AgentExecutor internal consumer components...
2025-06-30 15:24:34 - AgentExecutor - DEBUG - Cancelling background consumer task...
2025-06-30 15:24:34 - AgentExecutor - INFO - Result consumer loop cancelled.
2025-06-30 15:24:34 - AgentExecutor - DEBUG - Stopping internal Kafka consumer...
2025-06-30 15:24:34 - AgentExecutor - INFO - Internal Kafka consumer stopped.
2025-06-30 15:24:34 - AgentExecutor - INFO - AgentExecutor internal consumer stopped.
2025-06-30 15:24:34 - KafkaWorkflowConsumer - INFO - Closing database connections...
2025-06-30 15:24:34 - RedisManager - INFO - Redis connection closed.
2025-06-30 15:24:34 - RedisManager - INFO - Redis connection closed.
2025-06-30 15:24:34 - PostgresManager - INFO - PostgreSQL connection pool closed.
2025-06-30 15:24:34 - RedisEventListener - INFO - Redis event listener thread stopped
2025-06-30 15:24:34 - RedisEventListener - INFO - Redis event listener stopped
2025-06-30 15:24:34 - KafkaWorkflowConsumer - INFO - Database connections closed
2025-06-30 15:24:34 - KafkaWorkflowConsumer - INFO - Consumer and producer stopped gracefully.
