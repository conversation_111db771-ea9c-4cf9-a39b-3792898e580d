2025-06-30 15:36:36 - Main - INFO - Starting Server
2025-06-30 15:36:36 - Main - INFO - Connection at: **************:9092
2025-06-30 15:36:36 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-06-30 15:36:36 - Node<PERSON>xecutor - INFO - NodeExecutor initialized.
2025-06-30 15:36:36 - AgentExecutor - INFO - AgentExecutor initialized.
2025-06-30 15:36:36 - KafkaWorkflowConsumer - INFO - Initializing database connections...
2025-06-30 15:36:36 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-30 15:36:38 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-30 15:36:38 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-30 15:36:40 - <PERSON><PERSON><PERSON>anager - INFO - Successfully connected to Redis on DB index: 6!
2025-06-30 15:36:42 - PostgresManager - INFO - PostgreSQL connection pool created
2025-06-30 15:36:42 - PostgresManager - INFO - PostgreSQL connection pool is available
2025-06-30 15:36:44 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-30 15:36:45 - RedisEventListener - INFO - Creating new RedisEventListener instance
2025-06-30 15:36:45 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-30 15:36:46 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-30 15:36:46 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-30 15:36:48 - RedisManager - INFO - Successfully connected to Redis on DB index: 6!
2025-06-30 15:36:48 - RedisEventListener - INFO - Starting Redis event listener thread
2025-06-30 15:36:48 - RedisEventListener - INFO - Redis event listener started
2025-06-30 15:36:48 - KafkaWorkflowConsumer - INFO - Database connections initialized successfully
2025-06-30 15:36:48 - StateManager - DEBUG - Using provided database connections
2025-06-30 15:36:48 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-30 15:36:48 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-30 15:36:48 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-30 15:36:48 - RedisEventListener - INFO - Configured Redis results DB for keyspace notifications including expirations
2025-06-30 15:36:49 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-30 15:36:49 - StateManager - INFO - WorkflowStateManager initialized
2025-06-30 15:36:49 - KafkaWorkflowConsumer - INFO - WorkflowStateManager reference set in RedisEventListener for archival operations
2025-06-30 15:36:49 - KafkaWorkflowConsumer - INFO - KafkaWorkflowConsumer initialized successfully
2025-06-30 15:36:49 - RedisEventListener - INFO - Configured Redis state DB for keyspace notifications including expirations
2025-06-30 15:36:49 - RedisEventListener - INFO - Created dedicated Redis clients for pubsub with decode_responses=False
2025-06-30 15:36:52 - RedisEventListener - INFO - Redis results client decode_responses: True
2025-06-30 15:36:52 - RedisEventListener - INFO - Redis state client decode_responses: True
2025-06-30 15:36:52 - RedisEventListener - INFO - Subscribed to keyspace events for Redis DB 5 and 6
2025-06-30 15:36:56 - MCPToolExecutor - INFO - Starting KafkaToolExecutor internal consumer...
2025-06-30 15:37:02 - MCPToolExecutor - INFO - Internal consumer started. Listening for results on: 'mcp_results', Group: 'tool-executor-consumer'
2025-06-30 15:37:02 - MCPToolExecutor - INFO - Background result consumer loop started.
2025-06-30 15:37:02 - NodeExecutor - INFO - Starting NodeExecutor internal consumer...
2025-06-30 15:37:09 - NodeExecutor - INFO - Internal consumer started. Listening for results on: 'node_results', Group: 'node-executor-consumer'
2025-06-30 15:37:09 - NodeExecutor - INFO - Background result consumer loop started.
2025-06-30 15:37:09 - AgentExecutor - INFO - Starting AgentExecutor internal consumer...
2025-06-30 15:37:15 - AgentExecutor - INFO - Internal consumer started. Listening for results on: 'agent_chat_responses', Group: 'agent-executor-consumer'
2025-06-30 15:37:15 - AgentExecutor - INFO - Background result consumer loop started.
2025-06-30 15:37:41 - KafkaWorkflowConsumer - INFO - Consumer task cancelled.
2025-06-30 15:37:42 - MCPToolExecutor - INFO - Stopping KafkaToolExecutor internal consumer components...
2025-06-30 15:37:42 - MCPToolExecutor - DEBUG - Cancelling background consumer task...
2025-06-30 15:37:42 - MCPToolExecutor - INFO - Result consumer loop cancelled.
2025-06-30 15:37:42 - MCPToolExecutor - DEBUG - Stopping internal Kafka consumer...
2025-06-30 15:37:42 - MCPToolExecutor - INFO - Internal Kafka consumer stopped.
2025-06-30 15:37:42 - MCPToolExecutor - INFO - KafkaToolExecutor internal consumer stopped.
2025-06-30 15:37:42 - NodeExecutor - INFO - Stopping NodeExecutor internal consumer components...
2025-06-30 15:37:42 - NodeExecutor - DEBUG - Cancelling background consumer task...
2025-06-30 15:37:42 - NodeExecutor - INFO - Result consumer loop cancelled.
2025-06-30 15:37:42 - NodeExecutor - DEBUG - Stopping internal Kafka consumer...
2025-06-30 15:37:43 - NodeExecutor - INFO - Internal Kafka consumer stopped.
2025-06-30 15:37:43 - NodeExecutor - INFO - NodeExecutor internal consumer stopped.
2025-06-30 15:37:43 - AgentExecutor - INFO - Stopping AgentExecutor internal consumer components...
2025-06-30 15:37:43 - AgentExecutor - DEBUG - Cancelling background consumer task...
2025-06-30 15:37:43 - AgentExecutor - INFO - Result consumer loop cancelled.
2025-06-30 15:37:43 - AgentExecutor - DEBUG - Stopping internal Kafka consumer...
2025-06-30 15:37:43 - AgentExecutor - INFO - Internal Kafka consumer stopped.
2025-06-30 15:37:43 - AgentExecutor - INFO - AgentExecutor internal consumer stopped.
2025-06-30 15:37:43 - KafkaWorkflowConsumer - INFO - Closing database connections...
2025-06-30 15:37:43 - RedisManager - INFO - Redis connection closed.
2025-06-30 15:37:43 - RedisManager - INFO - Redis connection closed.
2025-06-30 15:37:43 - PostgresManager - INFO - PostgreSQL connection pool closed.
2025-06-30 15:37:43 - RedisEventListener - INFO - Redis event listener thread stopped
2025-06-30 15:37:43 - RedisEventListener - INFO - Redis event listener stopped
2025-06-30 15:37:43 - KafkaWorkflowConsumer - INFO - Database connections closed
2025-06-30 15:37:43 - KafkaWorkflowConsumer - INFO - Consumer and producer stopped gracefully.
