2025-06-30 14:57:57 - Main - INFO - Starting Server
2025-06-30 14:57:57 - Main - INFO - Connection at: **************:9092
2025-06-30 14:57:57 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-06-30 14:57:57 - NodeExecutor - INFO - NodeExecutor initialized.
2025-06-30 14:57:57 - AgentExecutor - INFO - AgentExecutor initialized.
2025-06-30 14:57:57 - KafkaWorkflowConsumer - INFO - Initializing database connections...
2025-06-30 14:57:57 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-30 14:57:59 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-30 14:57:59 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-30 14:58:00 - Red<PERSON><PERSON>anager - INFO - Successfully connected to Redis on DB index: 6!
2025-06-30 14:58:02 - PostgresManager - INFO - PostgreSQL connection pool created
2025-06-30 14:58:02 - PostgresManager - INFO - PostgreSQL connection pool is available
2025-06-30 14:58:05 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-30 14:58:06 - RedisEventListener - INFO - Creating new RedisEventListener instance
2025-06-30 14:58:06 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-30 14:58:07 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-30 14:58:07 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-30 14:58:09 - RedisManager - INFO - Successfully connected to Redis on DB index: 6!
2025-06-30 14:58:09 - RedisEventListener - INFO - Starting Redis event listener thread
2025-06-30 14:58:09 - RedisEventListener - INFO - Redis event listener started
2025-06-30 14:58:09 - KafkaWorkflowConsumer - INFO - Database connections initialized successfully
2025-06-30 14:58:09 - StateManager - DEBUG - Using provided database connections
2025-06-30 14:58:09 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-30 14:58:09 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-30 14:58:09 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-30 14:58:10 - RedisEventListener - INFO - Configured Redis results DB for keyspace notifications including expirations
2025-06-30 14:58:10 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-30 14:58:10 - StateManager - INFO - WorkflowStateManager initialized
2025-06-30 14:58:10 - KafkaWorkflowConsumer - INFO - WorkflowStateManager reference set in RedisEventListener for archival operations
2025-06-30 14:58:10 - KafkaWorkflowConsumer - INFO - KafkaWorkflowConsumer initialized successfully
2025-06-30 14:58:10 - RedisEventListener - INFO - Configured Redis state DB for keyspace notifications including expirations
2025-06-30 14:58:10 - RedisEventListener - INFO - Created dedicated Redis clients for pubsub with decode_responses=False
2025-06-30 14:58:13 - RedisEventListener - INFO - Redis results client decode_responses: True
2025-06-30 14:58:13 - RedisEventListener - INFO - Redis state client decode_responses: True
2025-06-30 14:58:13 - RedisEventListener - INFO - Subscribed to keyspace events for Redis DB 5 and 6
2025-06-30 14:58:18 - MCPToolExecutor - INFO - Starting KafkaToolExecutor internal consumer...
2025-06-30 14:58:24 - MCPToolExecutor - INFO - Internal consumer started. Listening for results on: 'mcp_results', Group: 'tool-executor-consumer'
2025-06-30 14:58:24 - MCPToolExecutor - INFO - Background result consumer loop started.
2025-06-30 14:58:24 - NodeExecutor - INFO - Starting NodeExecutor internal consumer...
2025-06-30 14:58:30 - NodeExecutor - INFO - Internal consumer started. Listening for results on: 'node_results', Group: 'node-executor-consumer'
2025-06-30 14:58:30 - NodeExecutor - INFO - Background result consumer loop started.
2025-06-30 14:58:30 - AgentExecutor - INFO - Starting AgentExecutor internal consumer...
2025-06-30 14:58:36 - AgentExecutor - INFO - Internal consumer started. Listening for results on: 'agent_chat_responses', Group: 'agent-executor-consumer'
2025-06-30 14:58:36 - AgentExecutor - INFO - Background result consumer loop started.
2025-06-30 14:58:36 - KafkaWorkflowConsumer - INFO - Received: topic=workflow-requests, partition=0, offset=1066
2025-06-30 14:58:36 - KafkaWorkflowConsumer - DEBUG - message json: {'task_id': 1751275701, 'task_type': 'workflow', 'data': {'workflow_id': '0c19c070-905e-46ef-9f57-62eb427bf396', 'payload': {'user_dependent_fields': ['query'], 'user_payload_template': {'query': {'value': 'nature 101', 'transition_id': 'AgenticAI-*************'}}}, 'approval': True, 'user_id': '91a237fd-0225-4e02-9e9f-805eff073b07'}, 'approval': True}
2025-06-30 14:58:36 - KafkaWorkflowConsumer - INFO - Extracted user_id: 91a237fd-0225-4e02-9e9f-805eff073b07 for workflow: 0c19c070-905e-46ef-9f57-62eb427bf396
2025-06-30 14:58:36 - WorkflowService - DEBUG - Sending GET request to: https://app-dev.rapidinnovation.dev/api/v1/workflows/orchestration/0c19c070-905e-46ef-9f57-62eb427bf396
2025-06-30 14:58:40 - WorkflowService - DEBUG - Received response with status code: 200
2025-06-30 14:58:40 - WorkflowService - DEBUG - Parsed JSON response: {
  "success": true,
  "message": "Workflow Ruh_Video_Generation retrieved successfully",
  "workflow": {
    "id": "0c19c070-905e-46ef-9f57-62eb427bf396",
    "name": "Ruh_Video_Generation",
    "description": "Ruh_Video_Generation",
    "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/e2c5a041-2b84-4b33-be90-3d1e07e44353.json",
    "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/451d0c28-d066-4833-afad-080c37e7563e.json",
    "start_nodes": [
      {
        "field": "query",
        "type": "string",
        "transition_id": "transition-AgenticAI-*************"
      }
    ],
    "owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07",
    "user_ids": [
      "91a237fd-0225-4e02-9e9f-805eff073b07"
    ],
    "owner_type": "user",
    "workflow_template_id": "8d4b3f88-71ea-48dd-b8a2-dc101d22bf8a",
    "template_owner_id": "180c7469-1db0-4707-bc17-eeaa5e7ff64d",
    "is_imported": true,
    "version": "1.0.0",
    "visibility": "private",
    "category": null,
    "tags": null,
    "status": "active",
    "is_changes_marketplace": false,
    "is_customizable": true,
    "auto_version_on_update": false,
    "created_at": "2025-06-29T08:29:44.634200",
    "updated_at": "2025-06-30T08:40:55.500303",
    "available_nodes": [
      {
        "name": "CombineTextComponent",
        "display_name": "Combine Text",
        "type": "component",
        "transition_id": "transition-CombineTextComponent-*************"
      },
      {
        "name": "MergeDataComponent",
        "display_name": "Merge Data",
        "type": "component",
        "transition_id": "transition-MergeDataComponent-*************"
      },
      {
        "name": "CombineTextComponent",
        "display_name": "Combine Text",
        "type": "component",
        "transition_id": "transition-CombineTextComponent-1750920624318"
      }
    ],
    "is_updated": true
  }
}
2025-06-30 14:58:40 - KafkaWorkflowConsumer - DEBUG - Workflow loaded for 0c19c070-905e-46ef-9f57-62eb427bf396 - server_script_path is optional
2025-06-30 14:58:40 - WorkflowUtils - INFO - WorkflowUtils initialized
2025-06-30 14:58:40 - StateManager - DEBUG - Using global database connections from initializer
2025-06-30 14:58:40 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-30 14:58:40 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-30 14:58:40 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-30 14:58:41 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-30 14:58:41 - StateManager - INFO - WorkflowStateManager initialized
2025-06-30 14:58:41 - WorkflowUtils - INFO - Workflow JSON is valid against the enhanced schema.
2025-06-30 14:58:41 - StateManager - DEBUG - Using provided database connections
2025-06-30 14:58:41 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-30 14:58:41 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-30 14:58:41 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-30 14:58:42 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-30 14:58:42 - StateManager - INFO - WorkflowStateManager initialized
2025-06-30 14:58:42 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-*************: ['transition-AgenticAI-*************']
2025-06-30 14:58:42 - StateManager - DEBUG - Extracted dependencies for transition transition-LoopNode-*************: ['transition-AgenticAI-*************']
2025-06-30 14:58:42 - StateManager - DEBUG - Extracted dependencies for transition transition-MergeDataComponent-*************: ['transition-LoopNode-*************']
2025-06-30 14:58:42 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-*************: ['transition-AgenticAI-*************', 'transition-AgenticAI-*************', 'transition-LoopNode-*************']
2025-06-30 14:58:42 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1750505490787: ['transition-CombineTextComponent-*************']
2025-06-30 14:58:42 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1751001474606: ['transition-AgenticAI-1750505490787']
2025-06-30 14:58:42 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1751001471087: ['transition-AgenticAI-1750505490787']
2025-06-30 14:58:42 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-1750920624318: ['transition-AgenticAI-1750505490787', 'transition-AgenticAI-1751001471087', 'transition-AgenticAI-1751001474606']
2025-06-30 14:58:42 - StateManager - INFO - Built dependency map for 9 transitions
2025-06-30 14:58:42 - StateManager - DEBUG - Transition transition-AgenticAI-************* depends on: ['transition-AgenticAI-*************']
2025-06-30 14:58:42 - StateManager - DEBUG - Transition transition-LoopNode-************* depends on: ['transition-AgenticAI-*************']
2025-06-30 14:58:42 - StateManager - DEBUG - Transition transition-MergeDataComponent-************* depends on: ['transition-LoopNode-*************']
2025-06-30 14:58:42 - StateManager - DEBUG - Transition transition-CombineTextComponent-************* depends on: ['transition-AgenticAI-*************', 'transition-AgenticAI-*************', 'transition-LoopNode-*************']
2025-06-30 14:58:42 - StateManager - DEBUG - Transition transition-AgenticAI-1750505490787 depends on: ['transition-CombineTextComponent-*************']
2025-06-30 14:58:42 - StateManager - DEBUG - Transition transition-AgenticAI-1751001474606 depends on: ['transition-AgenticAI-1750505490787']
2025-06-30 14:58:42 - StateManager - DEBUG - Transition transition-AgenticAI-1751001471087 depends on: ['transition-AgenticAI-1750505490787']
2025-06-30 14:58:42 - StateManager - DEBUG - Transition transition-CombineTextComponent-1750920624318 depends on: ['transition-AgenticAI-1750505490787', 'transition-AgenticAI-1751001471087', 'transition-AgenticAI-1751001474606']
2025-06-30 14:58:42 - MCPToolExecutor - DEBUG - Set correlation ID to: 895aaa41-37c0-4d36-805b-c66511b28040
2025-06-30 14:58:42 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 895aaa41-37c0-4d36-805b-c66511b28040 in tool_executor
2025-06-30 14:58:42 - MCPToolExecutor - DEBUG - Set user ID to: 91a237fd-0225-4e02-9e9f-805eff073b07
2025-06-30 14:58:42 - EnhancedWorkflowEngine - DEBUG - Set user_id 91a237fd-0225-4e02-9e9f-805eff073b07 in tool_executor
2025-06-30 14:58:42 - NodeExecutor - DEBUG - Set correlation ID to: 895aaa41-37c0-4d36-805b-c66511b28040
2025-06-30 14:58:42 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 895aaa41-37c0-4d36-805b-c66511b28040 in node_executor
2025-06-30 14:58:42 - AgentExecutor - DEBUG - Set correlation ID to: 895aaa41-37c0-4d36-805b-c66511b28040
2025-06-30 14:58:42 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 895aaa41-37c0-4d36-805b-c66511b28040 in agent_executor
2025-06-30 14:58:42 - AgentExecutor - DEBUG - Set user ID to: 91a237fd-0225-4e02-9e9f-805eff073b07
2025-06-30 14:58:42 - EnhancedWorkflowEngine - DEBUG - Set user_id 91a237fd-0225-4e02-9e9f-805eff073b07 in agent_executor
2025-06-30 14:58:42 - TransitionHandler - INFO - TransitionHandler initialized
2025-06-30 14:58:42 - EnhancedWorkflowEngine - INFO - EnhancedWorkflowEngine initialized with workflow ID: 895aaa41-37c0-4d36-805b-c66511b28040
2025-06-30 14:58:42 - KafkaWorkflowConsumer - INFO - Workflow execution started in background for task-request, corr_id: 895aaa41-37c0-4d36-805b-c66511b28040
2025-06-30 14:58:42 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 895aaa41-37c0-4d36-805b-c66511b28040, response: {'status': 'Workflow Initialized', 'result': 'Workflow Initialized', 'workflow_status': 'running'}
2025-06-30 14:58:42 - StateManager - INFO - Workflow initialized with initial transition: transition-AgenticAI-*************
2025-06-30 14:58:42 - StateManager - DEBUG - State: pending={'transition-AgenticAI-*************'}, waiting=set(), completed=set()
2025-06-30 14:58:42 - EnhancedWorkflowEngine - INFO - Initializing workflow with single initial transition: transition-AgenticAI-*************
2025-06-30 14:58:42 - StateManager - DEBUG - Workflow active: {'transition-AgenticAI-*************'}
2025-06-30 14:58:43 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:895aaa41-37c0-4d36-805b-c66511b28040'
2025-06-30 14:58:43 - RedisManager - DEBUG - Set key 'workflow_state:895aaa41-37c0-4d36-805b-c66511b28040' with TTL of 600 seconds
2025-06-30 14:58:43 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 895aaa41-37c0-4d36-805b-c66511b28040. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:58:43 - StateManager - DEBUG - Checking waiting transitions: set()
2025-06-30 14:58:43 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-30 14:58:43 - StateManager - INFO - Cleared 1 pending transitions: {'transition-AgenticAI-*************'}
2025-06-30 14:58:43 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-30 14:58:43 - StateManager - INFO - Terminated: False
2025-06-30 14:58:43 - StateManager - INFO - Pending transitions (0): []
2025-06-30 14:58:43 - StateManager - INFO - Waiting transitions (0): []
2025-06-30 14:58:43 - StateManager - INFO - Completed transitions (0): []
2025-06-30 14:58:43 - StateManager - INFO - Results stored for 0 transitions
2025-06-30 14:58:43 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-30 14:58:43 - StateManager - INFO - Workflow status: inactive
2025-06-30 14:58:43 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-30 14:58:43 - StateManager - INFO - Workflow status: inactive
2025-06-30 14:58:43 - StateManager - INFO - Workflow paused: False
2025-06-30 14:58:43 - StateManager - INFO - ==============================
2025-06-30 14:58:43 - TransitionHandler - INFO - Starting parallel execution of transition: transition-AgenticAI-*************
2025-06-30 14:58:43 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 0, corr_id 895aaa41-37c0-4d36-805b-c66511b28040):
2025-06-30 14:58:43 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 895aaa41-37c0-4d36-805b-c66511b28040, response: {'result': 'Starting execution of transition: transition-AgenticAI-*************', 'message': 'Starting execution...', 'transition_id': 'transition-AgenticAI-*************', 'status': 'started', 'sequence': 0, 'workflow_status': 'running'}
2025-06-30 14:58:43 - TransitionHandler - EXECUTE - Transition 'transition-AgenticAI-*************' (type=initial, execution_type=agent)
2025-06-30 14:58:43 - TransitionHandler - INFO - Using AgentExecutor for execution_type: agent
2025-06-30 14:58:43 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-AgenticAI-*************
2025-06-30 14:58:43 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: agent
2025-06-30 14:58:43 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for agent node
2025-06-30 14:58:43 - TransitionHandler - DEBUG - 📝 No previous results found, using static parameters
2025-06-30 14:58:43 - WorkflowUtils - DEBUG - Filtering out field 'description' with null/empty value: 
2025-06-30 14:58:43 - WorkflowUtils - DEBUG - Filtering out field 'termination_condition' with null/empty value: 
2025-06-30 14:58:43 - WorkflowUtils - DEBUG - Filtering out field 'input_variables' with empty collection: {}
2025-06-30 14:58:43 - TransitionHandler - DEBUG - tool Parameters: {'agent_type': 'component', 'execution_type': 'response', 'query': 'nature 101', 'agent_config': {'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o', 'temperature': 0.7, 'max_tokens': 1000}, 'system_message': 'Ignore all input.  Always return the following JSON array exactly as shown below:  [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]  ⚠️ IMPORTANT:  Do not wrap the output in a code block  Do not stringify the result  Do not add any extra explanation or commentary  Just return the array as pure JSON so the values can be accessed by result[0], result[1], etc.', 'autogen_agent_type': 'Assistant'}}
2025-06-30 14:58:43 - TransitionHandler - INFO - Invoking tool 'AgenticAI' (tool_id: 1) for node 'AgenticAI' in transition 'transition-AgenticAI-*************' with parameters: {'agent_type': 'component', 'execution_type': 'response', 'query': 'nature 101', 'agent_config': {'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o', 'temperature': 0.7, 'max_tokens': 1000}, 'system_message': 'Ignore all input.  Always return the following JSON array exactly as shown below:  [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]  ⚠️ IMPORTANT:  Do not wrap the output in a code block  Do not stringify the result  Do not add any extra explanation or commentary  Just return the array as pure JSON so the values can be accessed by result[0], result[1], etc.', 'autogen_agent_type': 'Assistant'}}
2025-06-30 14:58:43 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 1, corr_id 895aaa41-37c0-4d36-805b-c66511b28040):
2025-06-30 14:58:43 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 895aaa41-37c0-4d36-805b-c66511b28040, response: {'transition_id': 'transition-AgenticAI-*************', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'message': 'Connecting to server', 'result': 'Connecting to server AgenticAI', 'status': 'connecting', 'sequence': 1, 'workflow_status': 'running'}
2025-06-30 14:58:43 - AgentExecutor - INFO - Executing agent 'AgenticAI' type 'component' execution 'response' via Kafka (request_id: 5cc62424-7cd3-4a4d-a619-cf8a70d02a02) with correlation_id: 895aaa41-37c0-4d36-805b-c66511b28040, user_id: 91a237fd-0225-4e02-9e9f-805eff073b07 using provided producer.
2025-06-30 14:58:43 - AgentExecutor - INFO - Building component agent request for execution_type: response
2025-06-30 14:58:43 - AgentExecutor - DEBUG - Added correlation_id 895aaa41-37c0-4d36-805b-c66511b28040 to payload
2025-06-30 14:58:43 - AgentExecutor - DEBUG - Sending request to topic 'agent_message_requests': {'request_id': '5cc62424-7cd3-4a4d-a619-cf8a70d02a02', 'user_id': '91a237fd-0225-4e02-9e9f-805eff073b07', 'correlation_id': '895aaa41-37c0-4d36-805b-c66511b28040', 'agent_type': 'component', 'execution_type': 'response', 'query': 'nature 101', 'variables': {}, 'agent_config': {'id': '8e8418b3-a47c-4d29-9977-4fa136cb11aa', 'name': 'AI Agent', 'description': 'Basic AI Agent', 'system_message': 'Ignore all input.  Always return the following JSON array exactly as shown below:  [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]  ⚠️ IMPORTANT:  Do not wrap the output in a code block  Do not stringify the result  Do not add any extra explanation or commentary  Just return the array as pure JSON so the values can be accessed by result[0], result[1], etc.', 'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o', 'temperature': 0.7, 'max_tokens': 1000}, 'mcps': []}}
2025-06-30 14:58:43 - AgentExecutor - DEBUG - Request 5cc62424-7cd3-4a4d-a619-cf8a70d02a02 sent successfully using provided producer.
2025-06-30 14:58:43 - AgentExecutor - DEBUG - Waiting for single response result for request 5cc62424-7cd3-4a4d-a619-cf8a70d02a02...
2025-06-30 14:58:43 - KafkaWorkflowConsumer - INFO - Committed offset after starting engine for task-request: 1066, corr_id: 895aaa41-37c0-4d36-805b-c66511b28040
2025-06-30 14:58:44 - AgentExecutor - DEBUG - Result consumer received message: Offset=24307
2025-06-30 14:58:44 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: 18249d5a-8a79-40a8-a68a-70f3a416f459
2025-06-30 14:58:44 - AgentExecutor - DEBUG - Result consumer received message: Offset=24308
2025-06-30 14:58:44 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: e7511719-3166-4afa-ac67-4cbb3ec52c65
2025-06-30 14:58:44 - AgentExecutor - DEBUG - Result consumer received message: Offset=24309
2025-06-30 14:58:44 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: 56cb889a-8581-492f-b0b8-046fd50cda03
2025-06-30 14:58:44 - AgentExecutor - DEBUG - Result consumer received message: Offset=24310
2025-06-30 14:58:44 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: 9ccc6bfa-03f6-4f45-b868-7233d86c8712
2025-06-30 14:58:44 - AgentExecutor - DEBUG - Result consumer received message: Offset=24311
2025-06-30 14:58:44 - AgentExecutor - WARNING - Received result for unknown or timed-out request_id: d7cb3f15-76bd-4a6e-876a-93e469680eba
2025-06-30 14:58:48 - AgentExecutor - DEBUG - Result consumer received message: Offset=24312
2025-06-30 14:58:48 - AgentExecutor - DEBUG - Received valid result for request_id 5cc62424-7cd3-4a4d-a619-cf8a70d02a02
2025-06-30 14:58:48 - AgentExecutor - INFO - Single response received for request 5cc62424-7cd3-4a4d-a619-cf8a70d02a02.
2025-06-30 14:58:48 - TransitionHandler - INFO - Execution result from agent executor: "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you\u2019re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!"
2025-06-30 14:58:48 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 2, corr_id 895aaa41-37c0-4d36-805b-c66511b28040):
2025-06-30 14:58:48 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 895aaa41-37c0-4d36-805b-c66511b28040, response: {'transition_id': 'transition-AgenticAI-*************', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'message': 'Transition Result received.', 'result': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'status': 'completed', 'sequence': 2, 'workflow_status': 'running', 'approval_required': False}
2025-06-30 14:58:48 - StateManager - DEBUG - Stored result for transition transition-AgenticAI-************* in memory: {'AgenticAI': {'transition_id': 'transition-AgenticAI-*************', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'result': {'result': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!"}, 'status': 'completed', 'timestamp': 1751275728.4167528}}
2025-06-30 14:58:49 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-AgenticAI-*************'
2025-06-30 14:58:49 - RedisManager - DEBUG - Set key 'result:transition-AgenticAI-*************' with TTL of 300 seconds
2025-06-30 14:58:49 - StateManager - DEBUG - Stored result for transition transition-AgenticAI-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:58:49 - StateManager - INFO - Marked transition transition-AgenticAI-************* as completed (was_pending=False, was_waiting=False)
2025-06-30 14:58:49 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-AgenticAI-*************'}
2025-06-30 14:58:49 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-AgenticAI-*************
2025-06-30 14:58:49 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-06-30 14:58:49 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-30 14:58:49 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-06-30 14:58:49 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-AgenticAI-*************:
2025-06-30 14:58:49 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-30 14:58:49 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-30 14:58:49 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-AgenticAI-*************, returning empty list
2025-06-30 14:58:49 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-AgenticAI-*************
2025-06-30 14:58:49 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 3
2025-06-30 14:58:49 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: ['transition-AgenticAI-*************', 'transition-CombineTextComponent-*************', 'transition-CombineTextComponent-*************']
2025-06-30 14:58:49 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-06-30 14:58:49 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-06-30 14:58:49 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-AgenticAI-*************
2025-06-30 14:58:49 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-CombineTextComponent-*************
2025-06-30 14:58:49 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-CombineTextComponent-*************
2025-06-30 14:58:49 - TransitionHandler - DEBUG - 🔗 Final next_transitions: ['transition-CombineTextComponent-*************', 'transition-AgenticAI-*************']
2025-06-30 14:58:49 - TransitionHandler - INFO - Completed transition transition-AgenticAI-************* in 5.88 seconds
2025-06-30 14:58:49 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 3, corr_id 895aaa41-37c0-4d36-805b-c66511b28040):
2025-06-30 14:58:49 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 895aaa41-37c0-4d36-805b-c66511b28040, response: {'result': 'Completed transition in 5.88 seconds', 'message': 'Transition completed in 5.88 seconds', 'transition_id': 'transition-AgenticAI-*************', 'status': 'time_logged', 'sequence': 3, 'workflow_status': 'running'}
2025-06-30 14:58:49 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-AgenticAI-*************: ['transition-CombineTextComponent-*************', 'transition-AgenticAI-*************']
2025-06-30 14:58:49 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 2
2025-06-30 14:58:49 - EnhancedWorkflowEngine - DEBUG - Results: [['transition-CombineTextComponent-*************', 'transition-AgenticAI-*************']]
2025-06-30 14:58:49 - EnhancedWorkflowEngine - DEBUG - 🔄 Orchestration engine received result for transition-AgenticAI-*************: ['transition-CombineTextComponent-*************', 'transition-AgenticAI-*************']
2025-06-30 14:58:49 - EnhancedWorkflowEngine - DEBUG - 🔄 Result type: <class 'list'>, is_list: True
2025-06-30 14:58:49 - EnhancedWorkflowEngine - INFO - Transition transition-AgenticAI-************* completed successfully: 2 next transitions
2025-06-30 14:58:49 - EnhancedWorkflowEngine - DEBUG - 🔄 Added to next_transitions_to_execute: ['transition-CombineTextComponent-*************', 'transition-AgenticAI-*************']
2025-06-30 14:58:49 - TransitionHandler - INFO - Resolved next transitions (direct transition IDs): ['transition-CombineTextComponent-*************', 'transition-AgenticAI-*************']
2025-06-30 14:58:49 - EnhancedWorkflowEngine - INFO - Adding transition transition-CombineTextComponent-************* to waiting (dependencies not yet met)
2025-06-30 14:58:49 - EnhancedWorkflowEngine - INFO - Adding transition transition-AgenticAI-************* to pending (all dependencies met)
2025-06-30 14:58:49 - StateManager - DEBUG - Workflow active: {'transition-AgenticAI-*************'}
2025-06-30 14:58:50 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:895aaa41-37c0-4d36-805b-c66511b28040'
2025-06-30 14:58:50 - RedisManager - DEBUG - Set key 'workflow_state:895aaa41-37c0-4d36-805b-c66511b28040' with TTL of 600 seconds
2025-06-30 14:58:50 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 895aaa41-37c0-4d36-805b-c66511b28040. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:58:50 - StateManager - DEBUG - Checking waiting transitions: {'transition-CombineTextComponent-*************'}
2025-06-30 14:58:50 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-30 14:58:50 - StateManager - INFO - Cleared 1 pending transitions: {'transition-AgenticAI-*************'}
2025-06-30 14:58:50 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-30 14:58:50 - StateManager - INFO - Terminated: False
2025-06-30 14:58:50 - StateManager - INFO - Pending transitions (0): []
2025-06-30 14:58:50 - StateManager - INFO - Waiting transitions (1): ['transition-CombineTextComponent-*************']
2025-06-30 14:58:50 - StateManager - INFO - Completed transitions (1): ['transition-AgenticAI-*************']
2025-06-30 14:58:50 - StateManager - INFO - Results stored for 1 transitions
2025-06-30 14:58:50 - StateManager - DEBUG - Workflow active: {'transition-CombineTextComponent-*************'}
2025-06-30 14:58:50 - StateManager - INFO - Workflow status: active
2025-06-30 14:58:50 - StateManager - DEBUG - Workflow active: {'transition-CombineTextComponent-*************'}
2025-06-30 14:58:50 - StateManager - INFO - Workflow status: active
2025-06-30 14:58:50 - StateManager - INFO - Workflow paused: False
2025-06-30 14:58:50 - StateManager - INFO - ==============================
2025-06-30 14:58:50 - TransitionHandler - INFO - Starting parallel execution of transition: transition-AgenticAI-*************
2025-06-30 14:58:50 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 4, corr_id 895aaa41-37c0-4d36-805b-c66511b28040):
2025-06-30 14:58:50 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 895aaa41-37c0-4d36-805b-c66511b28040, response: {'result': 'Starting execution of transition: transition-AgenticAI-*************', 'message': 'Starting execution...', 'transition_id': 'transition-AgenticAI-*************', 'status': 'started', 'sequence': 4, 'workflow_status': 'running'}
2025-06-30 14:58:50 - TransitionHandler - EXECUTE - Transition 'transition-AgenticAI-*************' (type=standard, execution_type=agent)
2025-06-30 14:58:50 - TransitionHandler - INFO - Using AgentExecutor for execution_type: agent
2025-06-30 14:58:50 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-AgenticAI-*************
2025-06-30 14:58:50 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: agent
2025-06-30 14:58:50 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for agent node
2025-06-30 14:58:51 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-06-30 14:58:51 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-06-30 14:58:51 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-06-30 14:58:51 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-06-30 14:58:51 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 1)
2025-06-30 14:58:51 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!"}
2025-06-30 14:58:51 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-30 14:58:51 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!
2025-06-30 14:58:51 - WorkflowUtils - DEBUG - Found result.result: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you! (type: <class 'str'>)
2025-06-30 14:58:51 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-30 14:58:51 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-06-30 14:58:51 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-30 14:58:51 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-30 14:58:51 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!"}
2025-06-30 14:58:51 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-30 14:58:51 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!
2025-06-30 14:58:51 - WorkflowUtils - DEBUG - Found result.result: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you! (type: <class 'str'>)
2025-06-30 14:58:51 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-30 14:58:51 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-06-30 14:58:51 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → query via path 'result': I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!
2025-06-30 14:58:51 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-30 14:58:51 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-30 14:58:51 - WorkflowUtils - DEBUG - Filtering out field 'termination_condition' with null/empty value: 
2025-06-30 14:58:51 - WorkflowUtils - DEBUG - Filtering out field 'input_variables' with empty collection: {}
2025-06-30 14:58:51 - TransitionHandler - DEBUG - 📌 Added static parameter: agent_type = component
2025-06-30 14:58:51 - TransitionHandler - DEBUG - 📌 Added static parameter: execution_type = response
2025-06-30 14:58:51 - TransitionHandler - DEBUG - 📌 Added static parameter: agent_config = {'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'description': 'This agent acts as a loop executor initializer, always returning the starting range value 0 while ignoring all input.', 'system_message': 'You are a minimal-response AI agent.  Ignore all input, regardless of content or format. Your only task is to always return the single-digit number 0.  Output Rules: Return only: 0  Do NOT return any text, explanation, quotes, or formatting.  Do NOT wrap the output in JSON, markdown, or code blocks.  Do NOT acknowledge or reference the input in any way.', 'autogen_agent_type': 'Assistant'}
2025-06-30 14:58:51 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'query': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'agent_type': 'component', 'execution_type': 'response', 'agent_config': {'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'description': 'This agent acts as a loop executor initializer, always returning the starting range value 0 while ignoring all input.', 'system_message': 'You are a minimal-response AI agent.  Ignore all input, regardless of content or format. Your only task is to always return the single-digit number 0.  Output Rules: Return only: 0  Do NOT return any text, explanation, quotes, or formatting.  Do NOT wrap the output in JSON, markdown, or code blocks.  Do NOT acknowledge or reference the input in any way.', 'autogen_agent_type': 'Assistant'}}
2025-06-30 14:58:51 - TransitionHandler - DEBUG - tool Parameters: {'query': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'agent_type': 'component', 'execution_type': 'response', 'agent_config': {'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'description': 'This agent acts as a loop executor initializer, always returning the starting range value 0 while ignoring all input.', 'system_message': 'You are a minimal-response AI agent.  Ignore all input, regardless of content or format. Your only task is to always return the single-digit number 0.  Output Rules: Return only: 0  Do NOT return any text, explanation, quotes, or formatting.  Do NOT wrap the output in JSON, markdown, or code blocks.  Do NOT acknowledge or reference the input in any way.', 'autogen_agent_type': 'Assistant'}}
2025-06-30 14:58:51 - TransitionHandler - INFO - Invoking tool 'AgenticAI' (tool_id: 1) for node 'AgenticAI' in transition 'transition-AgenticAI-*************' with parameters: {'query': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'agent_type': 'component', 'execution_type': 'response', 'agent_config': {'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'description': 'This agent acts as a loop executor initializer, always returning the starting range value 0 while ignoring all input.', 'system_message': 'You are a minimal-response AI agent.  Ignore all input, regardless of content or format. Your only task is to always return the single-digit number 0.  Output Rules: Return only: 0  Do NOT return any text, explanation, quotes, or formatting.  Do NOT wrap the output in JSON, markdown, or code blocks.  Do NOT acknowledge or reference the input in any way.', 'autogen_agent_type': 'Assistant'}}
2025-06-30 14:58:51 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 5, corr_id 895aaa41-37c0-4d36-805b-c66511b28040):
2025-06-30 14:58:51 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 895aaa41-37c0-4d36-805b-c66511b28040, response: {'transition_id': 'transition-AgenticAI-*************', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'message': 'Connecting to server', 'result': 'Connecting to server AgenticAI', 'status': 'connecting', 'sequence': 5, 'workflow_status': 'running'}
2025-06-30 14:58:51 - AgentExecutor - INFO - Executing agent 'AgenticAI' type 'component' execution 'response' via Kafka (request_id: 01007968-db7f-4087-afee-1ed62e4c808d) with correlation_id: 895aaa41-37c0-4d36-805b-c66511b28040, user_id: 91a237fd-0225-4e02-9e9f-805eff073b07 using provided producer.
2025-06-30 14:58:51 - AgentExecutor - INFO - Building component agent request for execution_type: response
2025-06-30 14:58:51 - AgentExecutor - DEBUG - Added correlation_id 895aaa41-37c0-4d36-805b-c66511b28040 to payload
2025-06-30 14:58:51 - AgentExecutor - DEBUG - Sending request to topic 'agent_message_requests': {'request_id': '01007968-db7f-4087-afee-1ed62e4c808d', 'user_id': '91a237fd-0225-4e02-9e9f-805eff073b07', 'correlation_id': '895aaa41-37c0-4d36-805b-c66511b28040', 'agent_type': 'component', 'execution_type': 'response', 'query': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'variables': {}, 'agent_config': {'id': '0839b663-c93c-43c1-b4e3-08aed7385786', 'name': 'AI Agent', 'description': 'This agent acts as a loop executor initializer, always returning the starting range value 0 while ignoring all input.', 'system_message': 'You are a minimal-response AI agent.  Ignore all input, regardless of content or format. Your only task is to always return the single-digit number 0.  Output Rules: Return only: 0  Do NOT return any text, explanation, quotes, or formatting.  Do NOT wrap the output in JSON, markdown, or code blocks.  Do NOT acknowledge or reference the input in any way.', 'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'mcps': []}}
2025-06-30 14:58:51 - AgentExecutor - DEBUG - Request 01007968-db7f-4087-afee-1ed62e4c808d sent successfully using provided producer.
2025-06-30 14:58:51 - AgentExecutor - DEBUG - Waiting for single response result for request 01007968-db7f-4087-afee-1ed62e4c808d...
2025-06-30 14:58:53 - AgentExecutor - DEBUG - Result consumer received message: Offset=24313
2025-06-30 14:58:53 - AgentExecutor - DEBUG - Received valid result for request_id 01007968-db7f-4087-afee-1ed62e4c808d
2025-06-30 14:58:53 - AgentExecutor - INFO - Single response received for request 01007968-db7f-4087-afee-1ed62e4c808d.
2025-06-30 14:58:53 - TransitionHandler - INFO - Execution result from agent executor: "0"
2025-06-30 14:58:53 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 6, corr_id 895aaa41-37c0-4d36-805b-c66511b28040):
2025-06-30 14:58:53 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 895aaa41-37c0-4d36-805b-c66511b28040, response: {'transition_id': 'transition-AgenticAI-*************', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'message': 'Transition Result received.', 'result': '0', 'status': 'completed', 'sequence': 6, 'workflow_status': 'running', 'approval_required': False}
2025-06-30 14:58:53 - StateManager - DEBUG - Stored result for transition transition-AgenticAI-************* in memory: {'AgenticAI': {'transition_id': 'transition-AgenticAI-*************', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'result': {'result': '0'}, 'status': 'completed', 'timestamp': 1751275733.874919}}
2025-06-30 14:58:54 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-AgenticAI-*************'
2025-06-30 14:58:54 - RedisManager - DEBUG - Set key 'result:transition-AgenticAI-*************' with TTL of 300 seconds
2025-06-30 14:58:54 - StateManager - DEBUG - Stored result for transition transition-AgenticAI-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:58:54 - StateManager - INFO - Marked transition transition-AgenticAI-************* as completed (was_pending=False, was_waiting=False)
2025-06-30 14:58:54 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'transition-AgenticAI-*************', 'transition-AgenticAI-*************'}
2025-06-30 14:58:54 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-AgenticAI-*************
2025-06-30 14:58:54 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-06-30 14:58:54 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-30 14:58:54 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-06-30 14:58:54 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-AgenticAI-*************:
2025-06-30 14:58:54 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-30 14:58:54 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-30 14:58:54 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-AgenticAI-*************, returning empty list
2025-06-30 14:58:54 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-AgenticAI-*************
2025-06-30 14:58:54 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 1
2025-06-30 14:58:54 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: ['transition-LoopNode-*************']
2025-06-30 14:58:54 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-06-30 14:58:54 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-06-30 14:58:54 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-LoopNode-*************
2025-06-30 14:58:54 - TransitionHandler - DEBUG - 🔗 Final next_transitions: ['transition-LoopNode-*************']
2025-06-30 14:58:54 - TransitionHandler - INFO - Completed transition transition-AgenticAI-************* in 4.39 seconds
2025-06-30 14:58:54 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 7, corr_id 895aaa41-37c0-4d36-805b-c66511b28040):
2025-06-30 14:58:54 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 895aaa41-37c0-4d36-805b-c66511b28040, response: {'result': 'Completed transition in 4.39 seconds', 'message': 'Transition completed in 4.39 seconds', 'transition_id': 'transition-AgenticAI-*************', 'status': 'time_logged', 'sequence': 7, 'workflow_status': 'running'}
2025-06-30 14:58:54 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-AgenticAI-*************: ['transition-LoopNode-*************']
2025-06-30 14:58:54 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 1
2025-06-30 14:58:54 - EnhancedWorkflowEngine - DEBUG - Results: [['transition-LoopNode-*************']]
2025-06-30 14:58:54 - EnhancedWorkflowEngine - DEBUG - 🔄 Orchestration engine received result for transition-AgenticAI-*************: ['transition-LoopNode-*************']
2025-06-30 14:58:54 - EnhancedWorkflowEngine - DEBUG - 🔄 Result type: <class 'list'>, is_list: True
2025-06-30 14:58:54 - EnhancedWorkflowEngine - INFO - Transition transition-AgenticAI-************* completed successfully: 1 next transitions
2025-06-30 14:58:54 - EnhancedWorkflowEngine - DEBUG - 🔄 Added to next_transitions_to_execute: ['transition-LoopNode-*************']
2025-06-30 14:58:54 - TransitionHandler - INFO - Resolved next transitions (direct transition IDs): ['transition-LoopNode-*************']
2025-06-30 14:58:54 - EnhancedWorkflowEngine - INFO - Adding transition transition-LoopNode-************* to pending (all dependencies met)
2025-06-30 14:58:54 - StateManager - DEBUG - Workflow active: {'transition-LoopNode-*************'}
2025-06-30 14:58:55 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:895aaa41-37c0-4d36-805b-c66511b28040'
2025-06-30 14:58:55 - RedisManager - DEBUG - Set key 'workflow_state:895aaa41-37c0-4d36-805b-c66511b28040' with TTL of 600 seconds
2025-06-30 14:58:55 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 895aaa41-37c0-4d36-805b-c66511b28040. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:58:55 - StateManager - DEBUG - Checking waiting transitions: {'transition-CombineTextComponent-*************'}
2025-06-30 14:58:55 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-30 14:58:55 - StateManager - INFO - Cleared 1 pending transitions: {'transition-LoopNode-*************'}
2025-06-30 14:58:55 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-30 14:58:55 - StateManager - INFO - Terminated: False
2025-06-30 14:58:55 - StateManager - INFO - Pending transitions (0): []
2025-06-30 14:58:55 - StateManager - INFO - Waiting transitions (1): ['transition-CombineTextComponent-*************']
2025-06-30 14:58:55 - StateManager - INFO - Completed transitions (2): ['transition-AgenticAI-*************', 'transition-AgenticAI-*************']
2025-06-30 14:58:55 - StateManager - INFO - Results stored for 2 transitions
2025-06-30 14:58:55 - StateManager - DEBUG - Workflow active: {'transition-CombineTextComponent-*************'}
2025-06-30 14:58:55 - StateManager - INFO - Workflow status: active
2025-06-30 14:58:55 - StateManager - DEBUG - Workflow active: {'transition-CombineTextComponent-*************'}
2025-06-30 14:58:55 - StateManager - INFO - Workflow status: active
2025-06-30 14:58:55 - StateManager - INFO - Workflow paused: False
2025-06-30 14:58:55 - StateManager - INFO - ==============================
2025-06-30 14:58:55 - TransitionHandler - INFO - Starting parallel execution of transition: transition-LoopNode-*************
2025-06-30 14:58:55 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 8, corr_id 895aaa41-37c0-4d36-805b-c66511b28040):
2025-06-30 14:58:55 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 895aaa41-37c0-4d36-805b-c66511b28040, response: {'result': 'Starting execution of transition: transition-LoopNode-*************', 'message': 'Starting execution...', 'transition_id': 'transition-LoopNode-*************', 'status': 'started', 'sequence': 8, 'workflow_status': 'running'}
2025-06-30 14:58:55 - TransitionHandler - EXECUTE - Transition 'transition-LoopNode-*************' (type=standard, execution_type=loop)
2025-06-30 14:58:55 - LoopExecutor - DEBUG - 🔗 Orchestration engine set for loop executor integration
2025-06-30 14:58:55 - TransitionHandler - DEBUG - 🔗 Set orchestration engine for loop executor
2025-06-30 14:58:55 - TransitionHandler - INFO - Using KafkaToolExecutor for execution_type: loop
2025-06-30 14:58:55 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-LoopNode-*************
2025-06-30 14:58:55 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: loop
2025-06-30 14:58:55 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for loop node
2025-06-30 14:58:56 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-06-30 14:58:56 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-06-30 14:58:56 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-06-30 14:58:56 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-06-30 14:58:56 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 1)
2025-06-30 14:58:56 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '0'}
2025-06-30 14:58:56 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-30 14:58:56 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: 0
2025-06-30 14:58:56 - WorkflowUtils - DEBUG - Found result.result: 0 (type: <class 'str'>)
2025-06-30 14:58:56 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-30 14:58:56 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-06-30 14:58:56 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-30 14:58:56 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-30 14:58:56 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '0'}
2025-06-30 14:58:56 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-30 14:58:56 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: 0
2025-06-30 14:58:56 - WorkflowUtils - DEBUG - Found result.result: 0 (type: <class 'str'>)
2025-06-30 14:58:56 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-30 14:58:56 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-06-30 14:58:56 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → start via path 'result': 0
2025-06-30 14:58:56 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-30 14:58:56 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-30 14:58:56 - WorkflowUtils - DEBUG - Filtering out field 'iteration_list' with empty collection: []
2025-06-30 14:58:56 - WorkflowUtils - DEBUG - Filtering out field 'start' with null/empty value: None
2025-06-30 14:58:56 - WorkflowUtils - INFO - 🧹 Parameter filtering: 14 → 12 fields (2 null/empty fields removed)
2025-06-30 14:58:56 - TransitionHandler - DEBUG - 📌 Added static parameter: source_type = number_range
2025-06-30 14:58:56 - TransitionHandler - DEBUG - 📌 Added static parameter: batch_size = 1
2025-06-30 14:58:56 - TransitionHandler - DEBUG - 📌 Added static parameter: end = 6
2025-06-30 14:58:56 - TransitionHandler - DEBUG - 📌 Added static parameter: step = 1
2025-06-30 14:58:56 - TransitionHandler - DEBUG - 📌 Added static parameter: parallel_execution = False
2025-06-30 14:58:56 - TransitionHandler - DEBUG - 📌 Added static parameter: max_concurrent = 3
2025-06-30 14:58:56 - TransitionHandler - DEBUG - 📌 Added static parameter: preserve_order = True
2025-06-30 14:58:56 - TransitionHandler - DEBUG - 📌 Added static parameter: iteration_timeout = 60
2025-06-30 14:58:56 - TransitionHandler - DEBUG - 📌 Added static parameter: aggregation_type = collect_all
2025-06-30 14:58:56 - TransitionHandler - DEBUG - 📌 Added static parameter: include_metadata = False
2025-06-30 14:58:56 - TransitionHandler - DEBUG - 📌 Added static parameter: on_iteration_error = retry_once
2025-06-30 14:58:56 - TransitionHandler - DEBUG - 📌 Added static parameter: include_errors = True
2025-06-30 14:58:56 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'start': '0', 'source_type': 'number_range', 'batch_size': '1', 'end': '6', 'step': '1', 'parallel_execution': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': False, 'on_iteration_error': 'retry_once', 'include_errors': True}
2025-06-30 14:58:56 - TransitionHandler - DEBUG - tool Parameters: {'start': '0', 'source_type': 'number_range', 'batch_size': '1', 'end': '6', 'step': '1', 'parallel_execution': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': False, 'on_iteration_error': 'retry_once', 'include_errors': True}
2025-06-30 14:58:56 - TransitionHandler - INFO - Invoking tool 'LoopNode' (tool_id: 1) for node 'LoopNode' in transition 'transition-LoopNode-*************' with parameters: {'start': '0', 'source_type': 'number_range', 'batch_size': '1', 'end': '6', 'step': '1', 'parallel_execution': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': False, 'on_iteration_error': 'retry_once', 'include_errors': True}
2025-06-30 14:58:56 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 9, corr_id 895aaa41-37c0-4d36-805b-c66511b28040):
2025-06-30 14:58:56 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 895aaa41-37c0-4d36-805b-c66511b28040, response: {'transition_id': 'transition-LoopNode-*************', 'node_id': 'LoopNode', 'tool_name': 'LoopNode', 'message': 'Connecting to server', 'result': 'Connecting to server LoopNode', 'status': 'connecting', 'sequence': 9, 'workflow_status': 'running'}
2025-06-30 14:58:56 - TransitionHandler - DEBUG - 🔄 Resolving loop config parameters for transition: transition-LoopNode-*************
2025-06-30 14:58:56 - TransitionHandler - DEBUG - ✅ Loop config parameter resolution completed for transition: transition-LoopNode-*************
2025-06-30 14:58:56 - TransitionHandler - DEBUG - 🔍 Detected exit transition: transition-MergeDataComponent-************* (has final/aggregated indicators)
2025-06-30 14:58:56 - TransitionHandler - DEBUG - 🔍 Auto-detected loop body transition: transition-CombineTextComponent-************* (has current_item/iteration indicators)
2025-06-30 14:58:56 - TransitionHandler - INFO - 🔍 Auto-detected and added loop body transitions to config: ['transition-CombineTextComponent-*************']
2025-06-30 14:58:56 - LoopExecutor - INFO - 🚨 execute_tool called with transition_id: transition-LoopNode-*************
2025-06-30 14:58:56 - LoopExecutor - INFO - 🔄 Starting loop node execution for transition: transition-LoopNode-*************
2025-06-30 14:58:56 - LoopExecutor - DEBUG - 🔢 Detected number_range from 1 to 6 (step: 1), batch_size: 1
2025-06-30 14:58:56 - LoopExecutor - DEBUG - Parsed loop config: {'loop_type': 'context_independent', 'aggregation_config': {'type': 'list'}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': False}, 'iteration_source': {'type': 'range', 'data': {'start': 1, 'stop': 7, 'step': 1}, 'batch_size': 1, 'original': {'number_range': {'start': 1, 'end': 6}, 'step': 1}}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'concurrency': {'enabled': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'retry_config': {'max_retries': 1, 'retry_delay': 1.0}, 'iteration_behavior': 'independent', 'iteration_settings': {'parallel_execution': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'error_handling': {'on_iteration_error': 'retry_once', 'include_errors': True}, 'loop_body_transitions': ['transition-CombineTextComponent-*************'], 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-CombineTextComponent-1750920624318'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}}
2025-06-30 14:58:56 - LoopExecutor - DEBUG - Loop configuration validation passed
2025-06-30 14:58:56 - TransitionHandler - DEBUG - 📝 Registered loop executor for transition: transition-LoopNode-*************
2025-06-30 14:58:56 - LoopExecutor - DEBUG - 📝 Registered loop executor with transition handler for transition: transition-LoopNode-*************
2025-06-30 14:58:56 - LoopExecutor - INFO - Prepared 6 iterations from range source
2025-06-30 14:58:56 - LoopExecutor - INFO - 🔄 Initialized loop state for 6 iterations
2025-06-30 14:58:56 - LoopExecutor - INFO - 🚨 _execute_loop_workflow called
2025-06-30 14:58:56 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_state keys: ['loop_config', 'transition_id', 'iteration_data', 'loop_context', 'input_data']
2025-06-30 14:58:56 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_config keys: ['loop_type', 'aggregation_config', 'result_aggregation', 'iteration_source', 'exit_condition', 'concurrency', 'retry_config', 'iteration_behavior', 'iteration_settings', 'error_handling', 'loop_body_transitions', 'loop_body_configuration']
2025-06-30 14:58:56 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_body_config: {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-CombineTextComponent-1750920624318'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}
2025-06-30 14:58:56 - LoopExecutor - DEBUG - 🔍 DEBUG: entry_transitions: ['transition-CombineTextComponent-*************']
2025-06-30 14:58:56 - LoopExecutor - DEBUG - 🔍 DEBUG: exit_transitions: ['transition-CombineTextComponent-1750920624318']
2025-06-30 14:58:56 - LoopExecutor - INFO - 🔄 Starting iteration 1/6
2025-06-30 14:58:56 - LoopExecutor - DEBUG - 🔄 Executing iteration 1 chain directly
2025-06-30 14:58:56 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in memory: 1
2025-06-30 14:58:56 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_0
2025-06-30 14:58:57 - RedisManager - DEBUG - Set key 'result:loop_iteration_0' with TTL of 900 seconds
2025-06-30 14:58:57 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:58:57 - StateManager - INFO - Marked transition loop_iteration_0 as completed (was_pending=False, was_waiting=False)
2025-06-30 14:58:57 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'loop_iteration_0', 'transition-AgenticAI-*************', 'transition-AgenticAI-*************'}
2025-06-30 14:58:57 - StateManager - DEBUG - Stored result for transition current_iteration in memory: 1
2025-06-30 14:58:57 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-06-30 14:58:57 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-06-30 14:58:57 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:58:57 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-06-30 14:58:57 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'loop_iteration_0', 'current_iteration', 'transition-AgenticAI-*************', 'transition-AgenticAI-*************'}
2025-06-30 14:58:57 - StateManager - DEBUG - Stored result for transition loop_iteration_transition-LoopNode-*************_0 in memory: {'current_item': 1, 'iteration_index': 0, 'iteration_metadata': {'timestamp': 312826.085483166, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-06-30 14:58:58 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_transition-LoopNode-*************_0
2025-06-30 14:58:58 - RedisManager - DEBUG - Set key 'result:loop_iteration_transition-LoopNode-*************_0' with TTL of 900 seconds
2025-06-30 14:58:58 - StateManager - DEBUG - Stored result for transition loop_iteration_transition-LoopNode-*************_0 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:58:58 - StateManager - INFO - Marked transition loop_iteration_transition-LoopNode-*************_0 as completed (was_pending=False, was_waiting=False)
2025-06-30 14:58:58 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'current_iteration', 'loop_iteration_transition-LoopNode-*************_0', 'loop_iteration_0', 'transition-AgenticAI-*************', 'transition-AgenticAI-*************'}
2025-06-30 14:58:58 - StateManager - DEBUG - Stored result for transition backup_transition-LoopNode-*************_iteration_0 in memory: {'current_item': 1, 'iteration_index': 0, 'iteration_metadata': {'timestamp': 312826.085483166, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-06-30 14:58:59 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:backup_transition-LoopNode-*************_iteration_0'
2025-06-30 14:58:59 - RedisManager - DEBUG - Set key 'result:backup_transition-LoopNode-*************_iteration_0' with TTL of 300 seconds
2025-06-30 14:58:59 - StateManager - DEBUG - Stored result for transition backup_transition-LoopNode-*************_iteration_0 in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:58:59 - StateManager - INFO - Marked transition backup_transition-LoopNode-*************_iteration_0 as completed (was_pending=False, was_waiting=False)
2025-06-30 14:58:59 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'current_iteration', 'loop_iteration_transition-LoopNode-*************_0', 'loop_iteration_0', 'transition-AgenticAI-*************', 'backup_transition-LoopNode-*************_iteration_0', 'transition-AgenticAI-*************'}
2025-06-30 14:58:59 - LoopExecutor - DEBUG - 💾 Temporarily stored current iteration data under loop transition ID: transition-LoopNode-*************
2025-06-30 14:58:59 - LoopExecutor - DEBUG - 💾 Also stored with backup keys: loop_iteration_transition-LoopNode-*************_0, backup_transition-LoopNode-*************_iteration_0
2025-06-30 14:58:59 - LoopExecutor - DEBUG - 💾 Current iteration data: {'current_item': 1, 'iteration_index': 0, 'iteration_metadata': {'timestamp': 312826.085483166, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-06-30 14:58:59 - LoopExecutor - DEBUG - 💾 Injected iteration payload into state: loop_iteration_0
2025-06-30 14:58:59 - LoopExecutor - DEBUG - 🚀 Executing transition: transition-CombineTextComponent-*************
2025-06-30 14:58:59 - LoopExecutor - DEBUG - 🔧 About to execute transition transition-CombineTextComponent-*************
2025-06-30 14:58:59 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-30 14:58:59 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 10, corr_id 895aaa41-37c0-4d36-805b-c66511b28040):
2025-06-30 14:58:59 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 895aaa41-37c0-4d36-805b-c66511b28040, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 10, 'workflow_status': 'running'}
2025-06-30 14:58:59 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-06-30 14:58:59 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-30 14:58:59 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-30 14:58:59 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-30 14:58:59 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-30 14:59:00 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-06-30 14:59:00 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-06-30 14:59:00 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-06-30 14:59:00 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-06-30 14:59:01 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-06-30 14:59:01 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-06-30 14:59:01 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-06-30 14:59:01 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-06-30 14:59:01 - StateManager - DEBUG - No result found in Redis for transition transition-LoopNode-*************. Trying PostgreSQL next.
2025-06-30 14:59:04 - PostgresManager - DEBUG - No result found for transition transition-LoopNode-************* in correlation 895aaa41-37c0-4d36-805b-c66511b28040
2025-06-30 14:59:04 - StateManager - DEBUG - No result found in PostgreSQL for transition transition-LoopNode-*************. Trying in-memory next.
2025-06-30 14:59:04 - StateManager - INFO - Using in-memory result for transition transition-LoopNode-*************.
2025-06-30 14:59:04 - StateManager - DEBUG - Extracted results for 3 tools in transition transition-LoopNode-*************
2025-06-30 14:59:04 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-30 14:59:04 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 1)
2025-06-30 14:59:04 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 2)
2025-06-30 14:59:04 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-30 14:59:04 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!"}
2025-06-30 14:59:04 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-30 14:59:04 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!
2025-06-30 14:59:04 - WorkflowUtils - DEBUG - Found result.result: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you! (type: <class 'str'>)
2025-06-30 14:59:04 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-30 14:59:04 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-06-30 14:59:04 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!"}
2025-06-30 14:59:04 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-30 14:59:04 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!
2025-06-30 14:59:04 - WorkflowUtils - DEBUG - Found result.result: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you! (type: <class 'str'>)
2025-06-30 14:59:04 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-30 14:59:04 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-06-30 14:59:04 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 1, 'iteration_index': 0, 'iteration_metadata': {'timestamp': 312826.085483166, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-06-30 14:59:04 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-30 14:59:04 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-30 14:59:04 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (3/3 compatible)
2025-06-30 14:59:04 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (3/3 compatible)
2025-06-30 14:59:04 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!"}
2025-06-30 14:59:04 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-30 14:59:04 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!
2025-06-30 14:59:04 - WorkflowUtils - DEBUG - Found result.result: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you! (type: <class 'str'>)
2025-06-30 14:59:04 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-30 14:59:04 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-06-30 14:59:04 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → input_2 via path 'result': I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!
2025-06-30 14:59:04 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!"}
2025-06-30 14:59:04 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-30 14:59:04 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!
2025-06-30 14:59:04 - WorkflowUtils - DEBUG - Found result.result: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you! (type: <class 'str'>)
2025-06-30 14:59:04 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-30 14:59:04 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-06-30 14:59:04 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → input_1 via path 'result': I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!
2025-06-30 14:59:04 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 1, 'iteration_index': 0, 'iteration_metadata': {'timestamp': 312826.085483166, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-06-30 14:59:04 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-30 14:59:04 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-30 14:59:04 - WorkflowUtils - DEBUG - ✅ Handle mapping success: current_item → main_input via path 'current_item': 1
2025-06-30 14:59:04 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 3/3 successful
2025-06-30 14:59:04 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 3/3 successful
2025-06-30 14:59:04 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-30 14:59:04 - WorkflowUtils - DEBUG - Filtering out field 'input_1' with null/empty value: None
2025-06-30 14:59:04 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: None
2025-06-30 14:59:04 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-06-30 14:59:04 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-06-30 14:59:04 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-06-30 14:59:04 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-06-30 14:59:04 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-06-30 14:59:04 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-06-30 14:59:04 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-06-30 14:59:04 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-06-30 14:59:04 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 2 fields (11 null/empty fields removed)
2025-06-30 14:59:04 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-30 14:59:04 - TransitionHandler - DEBUG - 📌 Added static parameter: separator = \n
2025-06-30 14:59:04 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'input_2': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'input_1': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'main_input': 1, 'num_additional_inputs': '1', 'separator': '\\n'}
2025-06-30 14:59:04 - TransitionHandler - DEBUG - tool Parameters: {'input_2': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'input_1': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'main_input': 1, 'num_additional_inputs': '1', 'separator': '\\n'}
2025-06-30 14:59:04 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'input_2': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'input_1': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'main_input': 1, 'num_additional_inputs': '1', 'separator': '\\n'}
2025-06-30 14:59:04 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 11, corr_id 895aaa41-37c0-4d36-805b-c66511b28040):
2025-06-30 14:59:04 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 895aaa41-37c0-4d36-805b-c66511b28040, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 11, 'workflow_status': 'running'}
2025-06-30 14:59:04 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 114ba947-28ee-41bc-a7ad-7ef6612ae19f) using provided producer.
2025-06-30 14:59:04 - NodeExecutor - DEBUG - Added correlation_id 895aaa41-37c0-4d36-805b-c66511b28040 to payload
2025-06-30 14:59:04 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-06-30 14:59:04 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'input_2': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'input_1': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'main_input': 1, 'num_additional_inputs': '1', 'separator': '\\n'}, 'request_id': '114ba947-28ee-41bc-a7ad-7ef6612ae19f', 'correlation_id': '895aaa41-37c0-4d36-805b-c66511b28040', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-06-30 14:59:04 - NodeExecutor - DEBUG - Request 114ba947-28ee-41bc-a7ad-7ef6612ae19f sent successfully using provided producer.
2025-06-30 14:59:04 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 114ba947-28ee-41bc-a7ad-7ef6612ae19f...
2025-06-30 14:59:05 - NodeExecutor - DEBUG - Result consumer received message: Offset=944
2025-06-30 14:59:05 - NodeExecutor - WARNING - Received error response for request_id 114ba947-28ee-41bc-a7ad-7ef6612ae19f: Error combining text for request_id 114ba947-28ee-41bc-a7ad-7ef6612ae19f: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-06-30 14:59:05 - NodeExecutor - ERROR - Error during node execution 114ba947-28ee-41bc-a7ad-7ef6612ae19f: Node execution failed: Error combining text for request_id 114ba947-28ee-41bc-a7ad-7ef6612ae19f: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id 114ba947-28ee-41bc-a7ad-7ef6612ae19f: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-06-30 14:59:05 - TransitionHandler - ERROR - Tool execution failed for tool 'CombineTextComponent' (tool_id: 1) in node 'CombineTextComponent' of transition 'transition-CombineTextComponent-*************': Node execution failed: Error combining text for request_id 114ba947-28ee-41bc-a7ad-7ef6612ae19f: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 492, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 361, in execute_tool
    raise e
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id 114ba947-28ee-41bc-a7ad-7ef6612ae19f: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"

2025-06-30 14:59:05 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 12, corr_id 895aaa41-37c0-4d36-805b-c66511b28040):
2025-06-30 14:59:05 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 895aaa41-37c0-4d36-805b-c66511b28040, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition faced an error during execution.', 'result': '[ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 114ba947-28ee-41bc-a7ad-7ef6612ae19f: "Required field \'input_data\', \'input_datta\', or \'text_inputs\' not found in parameters"', 'status': 'failed', 'sequence': 12, 'workflow_status': 'running'}
2025-06-30 14:59:05 - TransitionHandler - ERROR - Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 114ba947-28ee-41bc-a7ad-7ef6612ae19f: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-06-30 14:59:05 - LoopExecutor - ERROR - ❌ Iteration chain execution failed: Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 114ba947-28ee-41bc-a7ad-7ef6612ae19f: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-06-30 14:59:05 - LoopExecutor - ERROR - ❌ Iteration 1 failed: Iteration chain execution failed: Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 114ba947-28ee-41bc-a7ad-7ef6612ae19f: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-06-30 14:59:05 - LoopExecutor - INFO - 🔄 Starting iteration 2/6
2025-06-30 14:59:05 - LoopExecutor - DEBUG - 🔄 Executing iteration 2 chain directly
2025-06-30 14:59:05 - StateManager - DEBUG - Stored result for transition loop_iteration_1 in memory: 2
2025-06-30 14:59:05 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_1
2025-06-30 14:59:06 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-30 14:59:06 - RedisManager - DEBUG - Set key 'result:loop_iteration_1' with TTL of 900 seconds
2025-06-30 14:59:06 - StateManager - DEBUG - Stored result for transition loop_iteration_1 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:59:06 - StateManager - INFO - Marked transition loop_iteration_1 as completed (was_pending=False, was_waiting=False)
2025-06-30 14:59:06 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'current_iteration', 'loop_iteration_transition-LoopNode-*************_0', 'loop_iteration_0', 'transition-AgenticAI-*************', 'loop_iteration_1', 'backup_transition-LoopNode-*************_iteration_0', 'transition-AgenticAI-*************'}
2025-06-30 14:59:06 - StateManager - DEBUG - Stored result for transition current_iteration in memory: 2
2025-06-30 14:59:06 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 14:59:06 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-06-30 14:59:06 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 14:59:06 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-30 14:59:07 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-06-30 14:59:07 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:59:07 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-06-30 14:59:07 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'current_iteration', 'loop_iteration_transition-LoopNode-*************_0', 'loop_iteration_0', 'transition-AgenticAI-*************', 'loop_iteration_1', 'backup_transition-LoopNode-*************_iteration_0', 'transition-AgenticAI-*************'}
2025-06-30 14:59:07 - StateManager - DEBUG - Stored result for transition loop_iteration_transition-LoopNode-*************_1 in memory: {'current_item': 2, 'iteration_index': 1, 'iteration_metadata': {'timestamp': 312835.248714916, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-06-30 14:59:07 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_transition-LoopNode-*************_1
2025-06-30 14:59:07 - RedisManager - DEBUG - Set key 'result:loop_iteration_transition-LoopNode-*************_1' with TTL of 900 seconds
2025-06-30 14:59:07 - StateManager - DEBUG - Stored result for transition loop_iteration_transition-LoopNode-*************_1 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:59:07 - StateManager - INFO - Marked transition loop_iteration_transition-LoopNode-*************_1 as completed (was_pending=False, was_waiting=False)
2025-06-30 14:59:07 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'current_iteration', 'loop_iteration_transition-LoopNode-*************_0', 'loop_iteration_0', 'transition-AgenticAI-*************', 'loop_iteration_1', 'backup_transition-LoopNode-*************_iteration_0', 'loop_iteration_transition-LoopNode-*************_1', 'transition-AgenticAI-*************'}
2025-06-30 14:59:07 - StateManager - DEBUG - Stored result for transition backup_transition-LoopNode-*************_iteration_1 in memory: {'current_item': 2, 'iteration_index': 1, 'iteration_metadata': {'timestamp': 312835.248714916, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-06-30 14:59:08 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:backup_transition-LoopNode-*************_iteration_1'
2025-06-30 14:59:08 - RedisManager - DEBUG - Set key 'result:backup_transition-LoopNode-*************_iteration_1' with TTL of 300 seconds
2025-06-30 14:59:08 - StateManager - DEBUG - Stored result for transition backup_transition-LoopNode-*************_iteration_1 in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:59:08 - StateManager - INFO - Marked transition backup_transition-LoopNode-*************_iteration_1 as completed (was_pending=False, was_waiting=False)
2025-06-30 14:59:08 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'current_iteration', 'backup_transition-LoopNode-*************_iteration_1', 'loop_iteration_transition-LoopNode-*************_0', 'loop_iteration_0', 'transition-AgenticAI-*************', 'loop_iteration_1', 'backup_transition-LoopNode-*************_iteration_0', 'loop_iteration_transition-LoopNode-*************_1', 'transition-AgenticAI-*************'}
2025-06-30 14:59:08 - LoopExecutor - DEBUG - 💾 Temporarily stored current iteration data under loop transition ID: transition-LoopNode-*************
2025-06-30 14:59:08 - LoopExecutor - DEBUG - 💾 Also stored with backup keys: loop_iteration_transition-LoopNode-*************_1, backup_transition-LoopNode-*************_iteration_1
2025-06-30 14:59:08 - LoopExecutor - DEBUG - 💾 Current iteration data: {'current_item': 2, 'iteration_index': 1, 'iteration_metadata': {'timestamp': 312835.248714916, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-06-30 14:59:08 - LoopExecutor - DEBUG - 💾 Injected iteration payload into state: loop_iteration_1
2025-06-30 14:59:08 - LoopExecutor - DEBUG - 🚀 Executing transition: transition-CombineTextComponent-*************
2025-06-30 14:59:08 - LoopExecutor - DEBUG - 🔧 About to execute transition transition-CombineTextComponent-*************
2025-06-30 14:59:08 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-30 14:59:08 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 13, corr_id 895aaa41-37c0-4d36-805b-c66511b28040):
2025-06-30 14:59:08 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 895aaa41-37c0-4d36-805b-c66511b28040, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 13, 'workflow_status': 'running'}
2025-06-30 14:59:08 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-06-30 14:59:08 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-30 14:59:08 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-30 14:59:08 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-30 14:59:08 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-30 14:59:09 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-06-30 14:59:09 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-06-30 14:59:09 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-06-30 14:59:09 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-06-30 14:59:10 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-06-30 14:59:10 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-06-30 14:59:10 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-06-30 14:59:10 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-06-30 14:59:11 - StateManager - DEBUG - No result found in Redis for transition transition-LoopNode-*************. Trying PostgreSQL next.
2025-06-30 14:59:13 - PostgresManager - DEBUG - No result found for transition transition-LoopNode-************* in correlation 895aaa41-37c0-4d36-805b-c66511b28040
2025-06-30 14:59:13 - StateManager - DEBUG - No result found in PostgreSQL for transition transition-LoopNode-*************. Trying in-memory next.
2025-06-30 14:59:13 - StateManager - INFO - Using in-memory result for transition transition-LoopNode-*************.
2025-06-30 14:59:13 - StateManager - DEBUG - Extracted results for 3 tools in transition transition-LoopNode-*************
2025-06-30 14:59:13 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-30 14:59:13 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 1)
2025-06-30 14:59:13 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 2)
2025-06-30 14:59:13 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-30 14:59:13 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!"}
2025-06-30 14:59:13 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-30 14:59:13 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!
2025-06-30 14:59:13 - WorkflowUtils - DEBUG - Found result.result: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you! (type: <class 'str'>)
2025-06-30 14:59:13 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-30 14:59:13 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-06-30 14:59:13 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!"}
2025-06-30 14:59:13 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-30 14:59:13 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!
2025-06-30 14:59:13 - WorkflowUtils - DEBUG - Found result.result: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you! (type: <class 'str'>)
2025-06-30 14:59:13 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-30 14:59:13 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-06-30 14:59:13 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 2, 'iteration_index': 1, 'iteration_metadata': {'timestamp': 312835.248714916, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-06-30 14:59:13 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-30 14:59:13 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-30 14:59:13 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (3/3 compatible)
2025-06-30 14:59:13 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (3/3 compatible)
2025-06-30 14:59:13 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!"}
2025-06-30 14:59:13 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-30 14:59:13 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!
2025-06-30 14:59:13 - WorkflowUtils - DEBUG - Found result.result: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you! (type: <class 'str'>)
2025-06-30 14:59:13 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-30 14:59:13 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-06-30 14:59:13 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → input_2 via path 'result': I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!
2025-06-30 14:59:13 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!"}
2025-06-30 14:59:13 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-30 14:59:13 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!
2025-06-30 14:59:13 - WorkflowUtils - DEBUG - Found result.result: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you! (type: <class 'str'>)
2025-06-30 14:59:13 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-30 14:59:13 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-06-30 14:59:13 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → input_1 via path 'result': I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!
2025-06-30 14:59:13 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 2, 'iteration_index': 1, 'iteration_metadata': {'timestamp': 312835.248714916, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-06-30 14:59:13 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-30 14:59:13 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-30 14:59:13 - WorkflowUtils - DEBUG - ✅ Handle mapping success: current_item → main_input via path 'current_item': 2
2025-06-30 14:59:13 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 3/3 successful
2025-06-30 14:59:13 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 3/3 successful
2025-06-30 14:59:13 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-30 14:59:13 - WorkflowUtils - DEBUG - Filtering out field 'input_1' with null/empty value: None
2025-06-30 14:59:13 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: None
2025-06-30 14:59:13 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-06-30 14:59:13 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-06-30 14:59:13 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-06-30 14:59:13 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-06-30 14:59:13 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-06-30 14:59:13 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-06-30 14:59:13 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-06-30 14:59:13 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-06-30 14:59:13 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 2 fields (11 null/empty fields removed)
2025-06-30 14:59:13 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-30 14:59:13 - TransitionHandler - DEBUG - 📌 Added static parameter: separator = \n
2025-06-30 14:59:13 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'input_2': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'input_1': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'main_input': 2, 'num_additional_inputs': '1', 'separator': '\\n'}
2025-06-30 14:59:13 - TransitionHandler - DEBUG - tool Parameters: {'input_2': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'input_1': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'main_input': 2, 'num_additional_inputs': '1', 'separator': '\\n'}
2025-06-30 14:59:13 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'input_2': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'input_1': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'main_input': 2, 'num_additional_inputs': '1', 'separator': '\\n'}
2025-06-30 14:59:13 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 14, corr_id 895aaa41-37c0-4d36-805b-c66511b28040):
2025-06-30 14:59:13 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 895aaa41-37c0-4d36-805b-c66511b28040, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 14, 'workflow_status': 'running'}
2025-06-30 14:59:13 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 4eaabcea-f9cb-405e-8674-72a676ad59c0) using provided producer.
2025-06-30 14:59:13 - NodeExecutor - DEBUG - Added correlation_id 895aaa41-37c0-4d36-805b-c66511b28040 to payload
2025-06-30 14:59:13 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-06-30 14:59:13 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'input_2': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'input_1': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'main_input': 2, 'num_additional_inputs': '1', 'separator': '\\n'}, 'request_id': '4eaabcea-f9cb-405e-8674-72a676ad59c0', 'correlation_id': '895aaa41-37c0-4d36-805b-c66511b28040', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-06-30 14:59:13 - NodeExecutor - DEBUG - Request 4eaabcea-f9cb-405e-8674-72a676ad59c0 sent successfully using provided producer.
2025-06-30 14:59:13 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 4eaabcea-f9cb-405e-8674-72a676ad59c0...
2025-06-30 14:59:14 - NodeExecutor - DEBUG - Result consumer received message: Offset=945
2025-06-30 14:59:14 - NodeExecutor - WARNING - Received error response for request_id 4eaabcea-f9cb-405e-8674-72a676ad59c0: Error combining text for request_id 4eaabcea-f9cb-405e-8674-72a676ad59c0: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-06-30 14:59:14 - NodeExecutor - ERROR - Error during node execution 4eaabcea-f9cb-405e-8674-72a676ad59c0: Node execution failed: Error combining text for request_id 4eaabcea-f9cb-405e-8674-72a676ad59c0: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id 4eaabcea-f9cb-405e-8674-72a676ad59c0: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-06-30 14:59:14 - TransitionHandler - ERROR - Tool execution failed for tool 'CombineTextComponent' (tool_id: 1) in node 'CombineTextComponent' of transition 'transition-CombineTextComponent-*************': Node execution failed: Error combining text for request_id 4eaabcea-f9cb-405e-8674-72a676ad59c0: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 492, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 361, in execute_tool
    raise e
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id 4eaabcea-f9cb-405e-8674-72a676ad59c0: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"

2025-06-30 14:59:14 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 15, corr_id 895aaa41-37c0-4d36-805b-c66511b28040):
2025-06-30 14:59:14 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 895aaa41-37c0-4d36-805b-c66511b28040, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition faced an error during execution.', 'result': '[ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 4eaabcea-f9cb-405e-8674-72a676ad59c0: "Required field \'input_data\', \'input_datta\', or \'text_inputs\' not found in parameters"', 'status': 'failed', 'sequence': 15, 'workflow_status': 'running'}
2025-06-30 14:59:14 - TransitionHandler - ERROR - Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 4eaabcea-f9cb-405e-8674-72a676ad59c0: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-06-30 14:59:14 - LoopExecutor - ERROR - ❌ Iteration chain execution failed: Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 4eaabcea-f9cb-405e-8674-72a676ad59c0: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-06-30 14:59:14 - LoopExecutor - ERROR - ❌ Iteration 2 failed: Iteration chain execution failed: Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 4eaabcea-f9cb-405e-8674-72a676ad59c0: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-06-30 14:59:14 - LoopExecutor - INFO - 🔄 Starting iteration 3/6
2025-06-30 14:59:14 - LoopExecutor - DEBUG - 🔄 Executing iteration 3 chain directly
2025-06-30 14:59:14 - StateManager - DEBUG - Stored result for transition loop_iteration_2 in memory: 3
2025-06-30 14:59:14 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_2
2025-06-30 14:59:14 - RedisManager - DEBUG - Set key 'result:loop_iteration_2' with TTL of 900 seconds
2025-06-30 14:59:14 - StateManager - DEBUG - Stored result for transition loop_iteration_2 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:59:14 - StateManager - INFO - Marked transition loop_iteration_2 as completed (was_pending=False, was_waiting=False)
2025-06-30 14:59:14 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'current_iteration', 'backup_transition-LoopNode-*************_iteration_1', 'loop_iteration_transition-LoopNode-*************_0', 'loop_iteration_0', 'loop_iteration_2', 'transition-AgenticAI-*************', 'loop_iteration_1', 'backup_transition-LoopNode-*************_iteration_0', 'loop_iteration_transition-LoopNode-*************_1', 'transition-AgenticAI-*************'}
2025-06-30 14:59:14 - StateManager - DEBUG - Stored result for transition current_iteration in memory: 3
2025-06-30 14:59:15 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-06-30 14:59:15 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-06-30 14:59:15 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:59:15 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-06-30 14:59:15 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'current_iteration', 'backup_transition-LoopNode-*************_iteration_1', 'loop_iteration_transition-LoopNode-*************_0', 'loop_iteration_0', 'loop_iteration_2', 'transition-AgenticAI-*************', 'loop_iteration_1', 'backup_transition-LoopNode-*************_iteration_0', 'loop_iteration_transition-LoopNode-*************_1', 'transition-AgenticAI-*************'}
2025-06-30 14:59:15 - StateManager - DEBUG - Stored result for transition loop_iteration_transition-LoopNode-*************_2 in memory: {'current_item': 3, 'iteration_index': 2, 'iteration_metadata': {'timestamp': 312843.910623666, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-06-30 14:59:16 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_transition-LoopNode-*************_2
2025-06-30 14:59:16 - RedisManager - DEBUG - Set key 'result:loop_iteration_transition-LoopNode-*************_2' with TTL of 900 seconds
2025-06-30 14:59:16 - StateManager - DEBUG - Stored result for transition loop_iteration_transition-LoopNode-*************_2 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:59:16 - StateManager - INFO - Marked transition loop_iteration_transition-LoopNode-*************_2 as completed (was_pending=False, was_waiting=False)
2025-06-30 14:59:16 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'current_iteration', 'loop_iteration_transition-LoopNode-*************_2', 'backup_transition-LoopNode-*************_iteration_1', 'loop_iteration_transition-LoopNode-*************_0', 'loop_iteration_0', 'loop_iteration_2', 'transition-AgenticAI-*************', 'loop_iteration_1', 'backup_transition-LoopNode-*************_iteration_0', 'loop_iteration_transition-LoopNode-*************_1', 'transition-AgenticAI-*************'}
2025-06-30 14:59:16 - StateManager - DEBUG - Stored result for transition backup_transition-LoopNode-*************_iteration_2 in memory: {'current_item': 3, 'iteration_index': 2, 'iteration_metadata': {'timestamp': 312843.910623666, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-06-30 14:59:17 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:backup_transition-LoopNode-*************_iteration_2'
2025-06-30 14:59:17 - RedisManager - DEBUG - Set key 'result:backup_transition-LoopNode-*************_iteration_2' with TTL of 300 seconds
2025-06-30 14:59:17 - StateManager - DEBUG - Stored result for transition backup_transition-LoopNode-*************_iteration_2 in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:59:17 - StateManager - INFO - Marked transition backup_transition-LoopNode-*************_iteration_2 as completed (was_pending=False, was_waiting=False)
2025-06-30 14:59:17 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'current_iteration', 'backup_transition-LoopNode-*************_iteration_2', 'loop_iteration_transition-LoopNode-*************_2', 'backup_transition-LoopNode-*************_iteration_1', 'loop_iteration_transition-LoopNode-*************_0', 'loop_iteration_0', 'loop_iteration_2', 'transition-AgenticAI-*************', 'loop_iteration_1', 'backup_transition-LoopNode-*************_iteration_0', 'loop_iteration_transition-LoopNode-*************_1', 'transition-AgenticAI-*************'}
2025-06-30 14:59:17 - LoopExecutor - DEBUG - 💾 Temporarily stored current iteration data under loop transition ID: transition-LoopNode-*************
2025-06-30 14:59:17 - LoopExecutor - DEBUG - 💾 Also stored with backup keys: loop_iteration_transition-LoopNode-*************_2, backup_transition-LoopNode-*************_iteration_2
2025-06-30 14:59:17 - LoopExecutor - DEBUG - 💾 Current iteration data: {'current_item': 3, 'iteration_index': 2, 'iteration_metadata': {'timestamp': 312843.910623666, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-06-30 14:59:17 - LoopExecutor - DEBUG - 💾 Injected iteration payload into state: loop_iteration_2
2025-06-30 14:59:17 - LoopExecutor - DEBUG - 🚀 Executing transition: transition-CombineTextComponent-*************
2025-06-30 14:59:17 - LoopExecutor - DEBUG - 🔧 About to execute transition transition-CombineTextComponent-*************
2025-06-30 14:59:17 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-30 14:59:17 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 16, corr_id 895aaa41-37c0-4d36-805b-c66511b28040):
2025-06-30 14:59:17 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 895aaa41-37c0-4d36-805b-c66511b28040, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 16, 'workflow_status': 'running'}
2025-06-30 14:59:17 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-06-30 14:59:17 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-30 14:59:17 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-30 14:59:17 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-30 14:59:17 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-30 14:59:18 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-06-30 14:59:18 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-06-30 14:59:18 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-06-30 14:59:18 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-06-30 14:59:18 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-06-30 14:59:18 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-06-30 14:59:18 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-06-30 14:59:18 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-06-30 14:59:19 - StateManager - DEBUG - No result found in Redis for transition transition-LoopNode-*************. Trying PostgreSQL next.
2025-06-30 14:59:22 - PostgresManager - DEBUG - No result found for transition transition-LoopNode-************* in correlation 895aaa41-37c0-4d36-805b-c66511b28040
2025-06-30 14:59:22 - StateManager - DEBUG - No result found in PostgreSQL for transition transition-LoopNode-*************. Trying in-memory next.
2025-06-30 14:59:22 - StateManager - INFO - Using in-memory result for transition transition-LoopNode-*************.
2025-06-30 14:59:22 - StateManager - DEBUG - Extracted results for 3 tools in transition transition-LoopNode-*************
2025-06-30 14:59:22 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-30 14:59:22 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 1)
2025-06-30 14:59:22 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 2)
2025-06-30 14:59:22 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-30 14:59:22 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!"}
2025-06-30 14:59:22 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-30 14:59:22 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!
2025-06-30 14:59:22 - WorkflowUtils - DEBUG - Found result.result: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you! (type: <class 'str'>)
2025-06-30 14:59:22 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-30 14:59:22 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-06-30 14:59:22 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!"}
2025-06-30 14:59:22 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-30 14:59:22 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!
2025-06-30 14:59:22 - WorkflowUtils - DEBUG - Found result.result: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you! (type: <class 'str'>)
2025-06-30 14:59:22 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-30 14:59:22 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-06-30 14:59:22 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 3, 'iteration_index': 2, 'iteration_metadata': {'timestamp': 312843.910623666, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-06-30 14:59:22 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-30 14:59:22 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-30 14:59:22 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (3/3 compatible)
2025-06-30 14:59:22 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (3/3 compatible)
2025-06-30 14:59:22 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!"}
2025-06-30 14:59:22 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-30 14:59:22 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!
2025-06-30 14:59:22 - WorkflowUtils - DEBUG - Found result.result: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you! (type: <class 'str'>)
2025-06-30 14:59:22 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-30 14:59:22 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-06-30 14:59:22 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → input_2 via path 'result': I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!
2025-06-30 14:59:22 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!"}
2025-06-30 14:59:22 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-30 14:59:22 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!
2025-06-30 14:59:22 - WorkflowUtils - DEBUG - Found result.result: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you! (type: <class 'str'>)
2025-06-30 14:59:22 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-30 14:59:22 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-06-30 14:59:22 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → input_1 via path 'result': I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!
2025-06-30 14:59:22 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 3, 'iteration_index': 2, 'iteration_metadata': {'timestamp': 312843.910623666, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-06-30 14:59:22 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-30 14:59:22 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-30 14:59:22 - WorkflowUtils - DEBUG - ✅ Handle mapping success: current_item → main_input via path 'current_item': 3
2025-06-30 14:59:22 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 3/3 successful
2025-06-30 14:59:22 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 3/3 successful
2025-06-30 14:59:22 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-30 14:59:22 - WorkflowUtils - DEBUG - Filtering out field 'input_1' with null/empty value: None
2025-06-30 14:59:22 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: None
2025-06-30 14:59:22 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-06-30 14:59:22 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-06-30 14:59:22 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-06-30 14:59:22 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-06-30 14:59:22 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-06-30 14:59:22 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-06-30 14:59:22 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-06-30 14:59:22 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-06-30 14:59:22 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 2 fields (11 null/empty fields removed)
2025-06-30 14:59:22 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-30 14:59:22 - TransitionHandler - DEBUG - 📌 Added static parameter: separator = \n
2025-06-30 14:59:22 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'input_2': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'input_1': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'main_input': 3, 'num_additional_inputs': '1', 'separator': '\\n'}
2025-06-30 14:59:22 - TransitionHandler - DEBUG - tool Parameters: {'input_2': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'input_1': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'main_input': 3, 'num_additional_inputs': '1', 'separator': '\\n'}
2025-06-30 14:59:22 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'input_2': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'input_1': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'main_input': 3, 'num_additional_inputs': '1', 'separator': '\\n'}
2025-06-30 14:59:22 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 17, corr_id 895aaa41-37c0-4d36-805b-c66511b28040):
2025-06-30 14:59:22 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 895aaa41-37c0-4d36-805b-c66511b28040, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 17, 'workflow_status': 'running'}
2025-06-30 14:59:22 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 13b0dcb6-8d22-4774-9d2f-3336eaad390a) using provided producer.
2025-06-30 14:59:22 - NodeExecutor - DEBUG - Added correlation_id 895aaa41-37c0-4d36-805b-c66511b28040 to payload
2025-06-30 14:59:22 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-06-30 14:59:22 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'input_2': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'input_1': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'main_input': 3, 'num_additional_inputs': '1', 'separator': '\\n'}, 'request_id': '13b0dcb6-8d22-4774-9d2f-3336eaad390a', 'correlation_id': '895aaa41-37c0-4d36-805b-c66511b28040', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-06-30 14:59:22 - NodeExecutor - DEBUG - Request 13b0dcb6-8d22-4774-9d2f-3336eaad390a sent successfully using provided producer.
2025-06-30 14:59:22 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 13b0dcb6-8d22-4774-9d2f-3336eaad390a...
2025-06-30 14:59:23 - NodeExecutor - DEBUG - Result consumer received message: Offset=946
2025-06-30 14:59:23 - NodeExecutor - WARNING - Received error response for request_id 13b0dcb6-8d22-4774-9d2f-3336eaad390a: Error combining text for request_id 13b0dcb6-8d22-4774-9d2f-3336eaad390a: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-06-30 14:59:23 - NodeExecutor - ERROR - Error during node execution 13b0dcb6-8d22-4774-9d2f-3336eaad390a: Node execution failed: Error combining text for request_id 13b0dcb6-8d22-4774-9d2f-3336eaad390a: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id 13b0dcb6-8d22-4774-9d2f-3336eaad390a: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-06-30 14:59:23 - TransitionHandler - ERROR - Tool execution failed for tool 'CombineTextComponent' (tool_id: 1) in node 'CombineTextComponent' of transition 'transition-CombineTextComponent-*************': Node execution failed: Error combining text for request_id 13b0dcb6-8d22-4774-9d2f-3336eaad390a: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 492, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 361, in execute_tool
    raise e
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id 13b0dcb6-8d22-4774-9d2f-3336eaad390a: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"

2025-06-30 14:59:23 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 18, corr_id 895aaa41-37c0-4d36-805b-c66511b28040):
2025-06-30 14:59:23 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 895aaa41-37c0-4d36-805b-c66511b28040, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition faced an error during execution.', 'result': '[ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 13b0dcb6-8d22-4774-9d2f-3336eaad390a: "Required field \'input_data\', \'input_datta\', or \'text_inputs\' not found in parameters"', 'status': 'failed', 'sequence': 18, 'workflow_status': 'running'}
2025-06-30 14:59:23 - TransitionHandler - ERROR - Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 13b0dcb6-8d22-4774-9d2f-3336eaad390a: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-06-30 14:59:23 - LoopExecutor - ERROR - ❌ Iteration chain execution failed: Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 13b0dcb6-8d22-4774-9d2f-3336eaad390a: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-06-30 14:59:23 - LoopExecutor - ERROR - ❌ Iteration 3 failed: Iteration chain execution failed: Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 13b0dcb6-8d22-4774-9d2f-3336eaad390a: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-06-30 14:59:23 - LoopExecutor - INFO - 🔄 Starting iteration 4/6
2025-06-30 14:59:23 - LoopExecutor - DEBUG - 🔄 Executing iteration 4 chain directly
2025-06-30 14:59:23 - StateManager - DEBUG - Stored result for transition loop_iteration_3 in memory: 4
2025-06-30 14:59:23 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_3
2025-06-30 14:59:24 - RedisManager - DEBUG - Set key 'result:loop_iteration_3' with TTL of 900 seconds
2025-06-30 14:59:24 - StateManager - DEBUG - Stored result for transition loop_iteration_3 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:59:24 - StateManager - INFO - Marked transition loop_iteration_3 as completed (was_pending=False, was_waiting=False)
2025-06-30 14:59:24 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'current_iteration', 'backup_transition-LoopNode-*************_iteration_2', 'loop_iteration_transition-LoopNode-*************_2', 'backup_transition-LoopNode-*************_iteration_1', 'loop_iteration_3', 'loop_iteration_transition-LoopNode-*************_0', 'loop_iteration_0', 'loop_iteration_2', 'transition-AgenticAI-*************', 'loop_iteration_1', 'backup_transition-LoopNode-*************_iteration_0', 'loop_iteration_transition-LoopNode-*************_1', 'transition-AgenticAI-*************'}
2025-06-30 14:59:24 - StateManager - DEBUG - Stored result for transition current_iteration in memory: 4
2025-06-30 14:59:24 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-06-30 14:59:24 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-06-30 14:59:24 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:59:24 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-06-30 14:59:24 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'current_iteration', 'backup_transition-LoopNode-*************_iteration_2', 'loop_iteration_transition-LoopNode-*************_2', 'backup_transition-LoopNode-*************_iteration_1', 'loop_iteration_3', 'loop_iteration_transition-LoopNode-*************_0', 'loop_iteration_0', 'loop_iteration_2', 'transition-AgenticAI-*************', 'loop_iteration_1', 'backup_transition-LoopNode-*************_iteration_0', 'loop_iteration_transition-LoopNode-*************_1', 'transition-AgenticAI-*************'}
2025-06-30 14:59:24 - StateManager - DEBUG - Stored result for transition loop_iteration_transition-LoopNode-*************_3 in memory: {'current_item': 4, 'iteration_index': 3, 'iteration_metadata': {'timestamp': 312853.049715291, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-06-30 14:59:25 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_transition-LoopNode-*************_3
2025-06-30 14:59:25 - RedisManager - DEBUG - Set key 'result:loop_iteration_transition-LoopNode-*************_3' with TTL of 900 seconds
2025-06-30 14:59:25 - StateManager - DEBUG - Stored result for transition loop_iteration_transition-LoopNode-*************_3 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:59:25 - StateManager - INFO - Marked transition loop_iteration_transition-LoopNode-*************_3 as completed (was_pending=False, was_waiting=False)
2025-06-30 14:59:25 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'current_iteration', 'backup_transition-LoopNode-*************_iteration_2', 'loop_iteration_transition-LoopNode-*************_2', 'backup_transition-LoopNode-*************_iteration_1', 'loop_iteration_3', 'loop_iteration_transition-LoopNode-*************_0', 'loop_iteration_0', 'loop_iteration_2', 'transition-AgenticAI-*************', 'loop_iteration_transition-LoopNode-*************_3', 'loop_iteration_1', 'backup_transition-LoopNode-*************_iteration_0', 'loop_iteration_transition-LoopNode-*************_1', 'transition-AgenticAI-*************'}
2025-06-30 14:59:25 - StateManager - DEBUG - Stored result for transition backup_transition-LoopNode-*************_iteration_3 in memory: {'current_item': 4, 'iteration_index': 3, 'iteration_metadata': {'timestamp': 312853.049715291, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-06-30 14:59:26 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:backup_transition-LoopNode-*************_iteration_3'
2025-06-30 14:59:26 - RedisManager - DEBUG - Set key 'result:backup_transition-LoopNode-*************_iteration_3' with TTL of 300 seconds
2025-06-30 14:59:26 - StateManager - DEBUG - Stored result for transition backup_transition-LoopNode-*************_iteration_3 in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:59:26 - StateManager - INFO - Marked transition backup_transition-LoopNode-*************_iteration_3 as completed (was_pending=False, was_waiting=False)
2025-06-30 14:59:26 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'current_iteration', 'backup_transition-LoopNode-*************_iteration_2', 'loop_iteration_transition-LoopNode-*************_2', 'backup_transition-LoopNode-*************_iteration_1', 'loop_iteration_3', 'loop_iteration_transition-LoopNode-*************_0', 'loop_iteration_0', 'loop_iteration_2', 'transition-AgenticAI-*************', 'loop_iteration_transition-LoopNode-*************_3', 'backup_transition-LoopNode-*************_iteration_3', 'loop_iteration_1', 'backup_transition-LoopNode-*************_iteration_0', 'loop_iteration_transition-LoopNode-*************_1', 'transition-AgenticAI-*************'}
2025-06-30 14:59:26 - LoopExecutor - DEBUG - 💾 Temporarily stored current iteration data under loop transition ID: transition-LoopNode-*************
2025-06-30 14:59:26 - LoopExecutor - DEBUG - 💾 Also stored with backup keys: loop_iteration_transition-LoopNode-*************_3, backup_transition-LoopNode-*************_iteration_3
2025-06-30 14:59:26 - LoopExecutor - DEBUG - 💾 Current iteration data: {'current_item': 4, 'iteration_index': 3, 'iteration_metadata': {'timestamp': 312853.049715291, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-06-30 14:59:26 - LoopExecutor - DEBUG - 💾 Injected iteration payload into state: loop_iteration_3
2025-06-30 14:59:26 - LoopExecutor - DEBUG - 🚀 Executing transition: transition-CombineTextComponent-*************
2025-06-30 14:59:26 - LoopExecutor - DEBUG - 🔧 About to execute transition transition-CombineTextComponent-*************
2025-06-30 14:59:26 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-30 14:59:26 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 19, corr_id 895aaa41-37c0-4d36-805b-c66511b28040):
2025-06-30 14:59:26 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 895aaa41-37c0-4d36-805b-c66511b28040, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 19, 'workflow_status': 'running'}
2025-06-30 14:59:26 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-06-30 14:59:26 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-30 14:59:26 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-30 14:59:26 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-30 14:59:26 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-30 14:59:27 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-06-30 14:59:27 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-06-30 14:59:27 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-06-30 14:59:27 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-06-30 14:59:28 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-06-30 14:59:28 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-06-30 14:59:28 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-06-30 14:59:28 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-06-30 14:59:28 - StateManager - DEBUG - No result found in Redis for transition transition-LoopNode-*************. Trying PostgreSQL next.
2025-06-30 14:59:31 - PostgresManager - DEBUG - No result found for transition transition-LoopNode-************* in correlation 895aaa41-37c0-4d36-805b-c66511b28040
2025-06-30 14:59:31 - StateManager - DEBUG - No result found in PostgreSQL for transition transition-LoopNode-*************. Trying in-memory next.
2025-06-30 14:59:31 - StateManager - INFO - Using in-memory result for transition transition-LoopNode-*************.
2025-06-30 14:59:31 - StateManager - DEBUG - Extracted results for 3 tools in transition transition-LoopNode-*************
2025-06-30 14:59:31 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-30 14:59:31 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 1)
2025-06-30 14:59:31 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 2)
2025-06-30 14:59:31 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-30 14:59:31 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!"}
2025-06-30 14:59:31 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-30 14:59:31 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!
2025-06-30 14:59:31 - WorkflowUtils - DEBUG - Found result.result: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you! (type: <class 'str'>)
2025-06-30 14:59:31 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-30 14:59:31 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-06-30 14:59:31 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!"}
2025-06-30 14:59:31 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-30 14:59:31 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!
2025-06-30 14:59:31 - WorkflowUtils - DEBUG - Found result.result: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you! (type: <class 'str'>)
2025-06-30 14:59:31 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-30 14:59:31 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-06-30 14:59:31 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 4, 'iteration_index': 3, 'iteration_metadata': {'timestamp': 312853.049715291, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-06-30 14:59:31 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-30 14:59:31 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-30 14:59:31 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (3/3 compatible)
2025-06-30 14:59:31 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (3/3 compatible)
2025-06-30 14:59:31 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!"}
2025-06-30 14:59:31 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-30 14:59:31 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!
2025-06-30 14:59:31 - WorkflowUtils - DEBUG - Found result.result: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you! (type: <class 'str'>)
2025-06-30 14:59:31 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-30 14:59:31 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-06-30 14:59:31 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → input_2 via path 'result': I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!
2025-06-30 14:59:31 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!"}
2025-06-30 14:59:31 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-30 14:59:31 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!
2025-06-30 14:59:31 - WorkflowUtils - DEBUG - Found result.result: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you! (type: <class 'str'>)
2025-06-30 14:59:31 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-30 14:59:31 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-06-30 14:59:31 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → input_1 via path 'result': I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!
2025-06-30 14:59:31 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 4, 'iteration_index': 3, 'iteration_metadata': {'timestamp': 312853.049715291, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-06-30 14:59:31 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-30 14:59:31 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-30 14:59:31 - WorkflowUtils - DEBUG - ✅ Handle mapping success: current_item → main_input via path 'current_item': 4
2025-06-30 14:59:31 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 3/3 successful
2025-06-30 14:59:31 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 3/3 successful
2025-06-30 14:59:31 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-30 14:59:31 - WorkflowUtils - DEBUG - Filtering out field 'input_1' with null/empty value: None
2025-06-30 14:59:31 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: None
2025-06-30 14:59:31 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-06-30 14:59:31 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-06-30 14:59:31 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-06-30 14:59:31 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-06-30 14:59:31 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-06-30 14:59:31 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-06-30 14:59:31 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-06-30 14:59:31 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-06-30 14:59:31 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 2 fields (11 null/empty fields removed)
2025-06-30 14:59:31 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-30 14:59:31 - TransitionHandler - DEBUG - 📌 Added static parameter: separator = \n
2025-06-30 14:59:31 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'input_2': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'input_1': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'main_input': 4, 'num_additional_inputs': '1', 'separator': '\\n'}
2025-06-30 14:59:31 - TransitionHandler - DEBUG - tool Parameters: {'input_2': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'input_1': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'main_input': 4, 'num_additional_inputs': '1', 'separator': '\\n'}
2025-06-30 14:59:31 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'input_2': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'input_1': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'main_input': 4, 'num_additional_inputs': '1', 'separator': '\\n'}
2025-06-30 14:59:31 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 20, corr_id 895aaa41-37c0-4d36-805b-c66511b28040):
2025-06-30 14:59:31 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 895aaa41-37c0-4d36-805b-c66511b28040, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 20, 'workflow_status': 'running'}
2025-06-30 14:59:31 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: f17a442c-86fd-45eb-94d4-cc82d0ab2f96) using provided producer.
2025-06-30 14:59:31 - NodeExecutor - DEBUG - Added correlation_id 895aaa41-37c0-4d36-805b-c66511b28040 to payload
2025-06-30 14:59:31 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-06-30 14:59:31 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'input_2': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'input_1': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'main_input': 4, 'num_additional_inputs': '1', 'separator': '\\n'}, 'request_id': 'f17a442c-86fd-45eb-94d4-cc82d0ab2f96', 'correlation_id': '895aaa41-37c0-4d36-805b-c66511b28040', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-06-30 14:59:31 - NodeExecutor - DEBUG - Request f17a442c-86fd-45eb-94d4-cc82d0ab2f96 sent successfully using provided producer.
2025-06-30 14:59:31 - NodeExecutor - DEBUG - Waiting indefinitely for result for request f17a442c-86fd-45eb-94d4-cc82d0ab2f96...
2025-06-30 14:59:31 - NodeExecutor - DEBUG - Result consumer received message: Offset=947
2025-06-30 14:59:31 - NodeExecutor - WARNING - Received error response for request_id f17a442c-86fd-45eb-94d4-cc82d0ab2f96: Error combining text for request_id f17a442c-86fd-45eb-94d4-cc82d0ab2f96: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-06-30 14:59:31 - NodeExecutor - ERROR - Error during node execution f17a442c-86fd-45eb-94d4-cc82d0ab2f96: Node execution failed: Error combining text for request_id f17a442c-86fd-45eb-94d4-cc82d0ab2f96: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id f17a442c-86fd-45eb-94d4-cc82d0ab2f96: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-06-30 14:59:31 - TransitionHandler - ERROR - Tool execution failed for tool 'CombineTextComponent' (tool_id: 1) in node 'CombineTextComponent' of transition 'transition-CombineTextComponent-*************': Node execution failed: Error combining text for request_id f17a442c-86fd-45eb-94d4-cc82d0ab2f96: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 492, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 361, in execute_tool
    raise e
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id f17a442c-86fd-45eb-94d4-cc82d0ab2f96: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"

2025-06-30 14:59:31 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 21, corr_id 895aaa41-37c0-4d36-805b-c66511b28040):
2025-06-30 14:59:31 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 895aaa41-37c0-4d36-805b-c66511b28040, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition faced an error during execution.', 'result': '[ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id f17a442c-86fd-45eb-94d4-cc82d0ab2f96: "Required field \'input_data\', \'input_datta\', or \'text_inputs\' not found in parameters"', 'status': 'failed', 'sequence': 21, 'workflow_status': 'running'}
2025-06-30 14:59:31 - TransitionHandler - ERROR - Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id f17a442c-86fd-45eb-94d4-cc82d0ab2f96: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-06-30 14:59:31 - LoopExecutor - ERROR - ❌ Iteration chain execution failed: Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id f17a442c-86fd-45eb-94d4-cc82d0ab2f96: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-06-30 14:59:31 - LoopExecutor - ERROR - ❌ Iteration 4 failed: Iteration chain execution failed: Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id f17a442c-86fd-45eb-94d4-cc82d0ab2f96: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-06-30 14:59:31 - LoopExecutor - INFO - 🔄 Starting iteration 5/6
2025-06-30 14:59:31 - LoopExecutor - DEBUG - 🔄 Executing iteration 5 chain directly
2025-06-30 14:59:31 - StateManager - DEBUG - Stored result for transition loop_iteration_4 in memory: 5
2025-06-30 14:59:32 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_4
2025-06-30 14:59:32 - RedisManager - DEBUG - Set key 'result:loop_iteration_4' with TTL of 900 seconds
2025-06-30 14:59:32 - StateManager - DEBUG - Stored result for transition loop_iteration_4 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:59:32 - StateManager - INFO - Marked transition loop_iteration_4 as completed (was_pending=False, was_waiting=False)
2025-06-30 14:59:32 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'current_iteration', 'backup_transition-LoopNode-*************_iteration_2', 'loop_iteration_transition-LoopNode-*************_2', 'backup_transition-LoopNode-*************_iteration_1', 'loop_iteration_3', 'loop_iteration_4', 'loop_iteration_transition-LoopNode-*************_0', 'loop_iteration_0', 'loop_iteration_2', 'transition-AgenticAI-*************', 'loop_iteration_transition-LoopNode-*************_3', 'backup_transition-LoopNode-*************_iteration_3', 'loop_iteration_1', 'backup_transition-LoopNode-*************_iteration_0', 'loop_iteration_transition-LoopNode-*************_1', 'transition-AgenticAI-*************'}
2025-06-30 14:59:32 - StateManager - DEBUG - Stored result for transition current_iteration in memory: 5
2025-06-30 14:59:33 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-06-30 14:59:33 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-06-30 14:59:33 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:59:33 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-06-30 14:59:33 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'current_iteration', 'backup_transition-LoopNode-*************_iteration_2', 'loop_iteration_transition-LoopNode-*************_2', 'backup_transition-LoopNode-*************_iteration_1', 'loop_iteration_3', 'loop_iteration_4', 'loop_iteration_transition-LoopNode-*************_0', 'loop_iteration_0', 'loop_iteration_2', 'transition-AgenticAI-*************', 'loop_iteration_transition-LoopNode-*************_3', 'backup_transition-LoopNode-*************_iteration_3', 'loop_iteration_1', 'backup_transition-LoopNode-*************_iteration_0', 'loop_iteration_transition-LoopNode-*************_1', 'transition-AgenticAI-*************'}
2025-06-30 14:59:33 - StateManager - DEBUG - Stored result for transition loop_iteration_transition-LoopNode-*************_4 in memory: {'current_item': 5, 'iteration_index': 4, 'iteration_metadata': {'timestamp': 312861.771238375, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-06-30 14:59:33 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_transition-LoopNode-*************_4
2025-06-30 14:59:34 - RedisManager - DEBUG - Set key 'result:loop_iteration_transition-LoopNode-*************_4' with TTL of 900 seconds
2025-06-30 14:59:34 - StateManager - DEBUG - Stored result for transition loop_iteration_transition-LoopNode-*************_4 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:59:34 - StateManager - INFO - Marked transition loop_iteration_transition-LoopNode-*************_4 as completed (was_pending=False, was_waiting=False)
2025-06-30 14:59:34 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'current_iteration', 'backup_transition-LoopNode-*************_iteration_2', 'loop_iteration_transition-LoopNode-*************_2', 'loop_iteration_transition-LoopNode-*************_4', 'backup_transition-LoopNode-*************_iteration_1', 'loop_iteration_3', 'loop_iteration_4', 'loop_iteration_transition-LoopNode-*************_0', 'loop_iteration_0', 'loop_iteration_2', 'transition-AgenticAI-*************', 'loop_iteration_transition-LoopNode-*************_3', 'backup_transition-LoopNode-*************_iteration_3', 'loop_iteration_1', 'backup_transition-LoopNode-*************_iteration_0', 'loop_iteration_transition-LoopNode-*************_1', 'transition-AgenticAI-*************'}
2025-06-30 14:59:34 - StateManager - DEBUG - Stored result for transition backup_transition-LoopNode-*************_iteration_4 in memory: {'current_item': 5, 'iteration_index': 4, 'iteration_metadata': {'timestamp': 312861.771238375, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-06-30 14:59:35 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:backup_transition-LoopNode-*************_iteration_4'
2025-06-30 14:59:35 - RedisManager - DEBUG - Set key 'result:backup_transition-LoopNode-*************_iteration_4' with TTL of 300 seconds
2025-06-30 14:59:35 - StateManager - DEBUG - Stored result for transition backup_transition-LoopNode-*************_iteration_4 in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:59:35 - StateManager - INFO - Marked transition backup_transition-LoopNode-*************_iteration_4 as completed (was_pending=False, was_waiting=False)
2025-06-30 14:59:35 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'current_iteration', 'backup_transition-LoopNode-*************_iteration_2', 'loop_iteration_transition-LoopNode-*************_2', 'loop_iteration_transition-LoopNode-*************_4', 'backup_transition-LoopNode-*************_iteration_1', 'loop_iteration_3', 'loop_iteration_4', 'loop_iteration_transition-LoopNode-*************_0', 'loop_iteration_0', 'loop_iteration_2', 'transition-AgenticAI-*************', 'loop_iteration_transition-LoopNode-*************_3', 'backup_transition-LoopNode-*************_iteration_3', 'loop_iteration_1', 'backup_transition-LoopNode-*************_iteration_4', 'backup_transition-LoopNode-*************_iteration_0', 'loop_iteration_transition-LoopNode-*************_1', 'transition-AgenticAI-*************'}
2025-06-30 14:59:35 - LoopExecutor - DEBUG - 💾 Temporarily stored current iteration data under loop transition ID: transition-LoopNode-*************
2025-06-30 14:59:35 - LoopExecutor - DEBUG - 💾 Also stored with backup keys: loop_iteration_transition-LoopNode-*************_4, backup_transition-LoopNode-*************_iteration_4
2025-06-30 14:59:35 - LoopExecutor - DEBUG - 💾 Current iteration data: {'current_item': 5, 'iteration_index': 4, 'iteration_metadata': {'timestamp': 312861.771238375, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-06-30 14:59:35 - LoopExecutor - DEBUG - 💾 Injected iteration payload into state: loop_iteration_4
2025-06-30 14:59:35 - LoopExecutor - DEBUG - 🚀 Executing transition: transition-CombineTextComponent-*************
2025-06-30 14:59:35 - LoopExecutor - DEBUG - 🔧 About to execute transition transition-CombineTextComponent-*************
2025-06-30 14:59:35 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-30 14:59:35 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 22, corr_id 895aaa41-37c0-4d36-805b-c66511b28040):
2025-06-30 14:59:35 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 895aaa41-37c0-4d36-805b-c66511b28040, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 22, 'workflow_status': 'running'}
2025-06-30 14:59:35 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-06-30 14:59:35 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-30 14:59:35 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-30 14:59:35 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-30 14:59:35 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-30 14:59:36 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-06-30 14:59:36 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-06-30 14:59:36 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-06-30 14:59:36 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-06-30 14:59:37 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-06-30 14:59:37 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-06-30 14:59:37 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-06-30 14:59:37 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-06-30 14:59:38 - StateManager - DEBUG - No result found in Redis for transition transition-LoopNode-*************. Trying PostgreSQL next.
2025-06-30 14:59:40 - PostgresManager - DEBUG - No result found for transition transition-LoopNode-************* in correlation 895aaa41-37c0-4d36-805b-c66511b28040
2025-06-30 14:59:40 - StateManager - DEBUG - No result found in PostgreSQL for transition transition-LoopNode-*************. Trying in-memory next.
2025-06-30 14:59:40 - StateManager - INFO - Using in-memory result for transition transition-LoopNode-*************.
2025-06-30 14:59:40 - StateManager - DEBUG - Extracted results for 3 tools in transition transition-LoopNode-*************
2025-06-30 14:59:40 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-30 14:59:40 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 1)
2025-06-30 14:59:40 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 2)
2025-06-30 14:59:40 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-30 14:59:40 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!"}
2025-06-30 14:59:40 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-30 14:59:40 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!
2025-06-30 14:59:40 - WorkflowUtils - DEBUG - Found result.result: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you! (type: <class 'str'>)
2025-06-30 14:59:40 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-30 14:59:40 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-06-30 14:59:40 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!"}
2025-06-30 14:59:40 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-30 14:59:40 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!
2025-06-30 14:59:40 - WorkflowUtils - DEBUG - Found result.result: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you! (type: <class 'str'>)
2025-06-30 14:59:40 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-30 14:59:40 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-06-30 14:59:40 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 5, 'iteration_index': 4, 'iteration_metadata': {'timestamp': 312861.771238375, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-06-30 14:59:40 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-30 14:59:40 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-30 14:59:40 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (3/3 compatible)
2025-06-30 14:59:40 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (3/3 compatible)
2025-06-30 14:59:40 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!"}
2025-06-30 14:59:40 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-30 14:59:40 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!
2025-06-30 14:59:40 - WorkflowUtils - DEBUG - Found result.result: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you! (type: <class 'str'>)
2025-06-30 14:59:40 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-30 14:59:40 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-06-30 14:59:40 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → input_2 via path 'result': I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!
2025-06-30 14:59:40 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!"}
2025-06-30 14:59:40 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-30 14:59:40 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!
2025-06-30 14:59:40 - WorkflowUtils - DEBUG - Found result.result: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you! (type: <class 'str'>)
2025-06-30 14:59:40 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-30 14:59:40 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-06-30 14:59:40 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → input_1 via path 'result': I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!
2025-06-30 14:59:40 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 5, 'iteration_index': 4, 'iteration_metadata': {'timestamp': 312861.771238375, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-06-30 14:59:40 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-30 14:59:40 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-30 14:59:40 - WorkflowUtils - DEBUG - ✅ Handle mapping success: current_item → main_input via path 'current_item': 5
2025-06-30 14:59:40 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 3/3 successful
2025-06-30 14:59:40 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 3/3 successful
2025-06-30 14:59:40 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-30 14:59:40 - WorkflowUtils - DEBUG - Filtering out field 'input_1' with null/empty value: None
2025-06-30 14:59:40 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: None
2025-06-30 14:59:40 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-06-30 14:59:40 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-06-30 14:59:40 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-06-30 14:59:40 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-06-30 14:59:40 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-06-30 14:59:40 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-06-30 14:59:40 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-06-30 14:59:40 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-06-30 14:59:40 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 2 fields (11 null/empty fields removed)
2025-06-30 14:59:40 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-30 14:59:40 - TransitionHandler - DEBUG - 📌 Added static parameter: separator = \n
2025-06-30 14:59:40 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'input_2': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'input_1': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'main_input': 5, 'num_additional_inputs': '1', 'separator': '\\n'}
2025-06-30 14:59:40 - TransitionHandler - DEBUG - tool Parameters: {'input_2': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'input_1': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'main_input': 5, 'num_additional_inputs': '1', 'separator': '\\n'}
2025-06-30 14:59:40 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'input_2': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'input_1': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'main_input': 5, 'num_additional_inputs': '1', 'separator': '\\n'}
2025-06-30 14:59:40 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 23, corr_id 895aaa41-37c0-4d36-805b-c66511b28040):
2025-06-30 14:59:40 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 895aaa41-37c0-4d36-805b-c66511b28040, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 23, 'workflow_status': 'running'}
2025-06-30 14:59:40 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: f2f7adc3-52c5-400a-a517-2568d9399c76) using provided producer.
2025-06-30 14:59:40 - NodeExecutor - DEBUG - Added correlation_id 895aaa41-37c0-4d36-805b-c66511b28040 to payload
2025-06-30 14:59:40 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-06-30 14:59:40 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'input_2': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'input_1': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'main_input': 5, 'num_additional_inputs': '1', 'separator': '\\n'}, 'request_id': 'f2f7adc3-52c5-400a-a517-2568d9399c76', 'correlation_id': '895aaa41-37c0-4d36-805b-c66511b28040', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-06-30 14:59:40 - NodeExecutor - DEBUG - Request f2f7adc3-52c5-400a-a517-2568d9399c76 sent successfully using provided producer.
2025-06-30 14:59:40 - NodeExecutor - DEBUG - Waiting indefinitely for result for request f2f7adc3-52c5-400a-a517-2568d9399c76...
2025-06-30 14:59:41 - NodeExecutor - DEBUG - Result consumer received message: Offset=948
2025-06-30 14:59:41 - NodeExecutor - WARNING - Received error response for request_id f2f7adc3-52c5-400a-a517-2568d9399c76: Error combining text for request_id f2f7adc3-52c5-400a-a517-2568d9399c76: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-06-30 14:59:41 - NodeExecutor - ERROR - Error during node execution f2f7adc3-52c5-400a-a517-2568d9399c76: Node execution failed: Error combining text for request_id f2f7adc3-52c5-400a-a517-2568d9399c76: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id f2f7adc3-52c5-400a-a517-2568d9399c76: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-06-30 14:59:41 - TransitionHandler - ERROR - Tool execution failed for tool 'CombineTextComponent' (tool_id: 1) in node 'CombineTextComponent' of transition 'transition-CombineTextComponent-*************': Node execution failed: Error combining text for request_id f2f7adc3-52c5-400a-a517-2568d9399c76: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 492, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 361, in execute_tool
    raise e
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id f2f7adc3-52c5-400a-a517-2568d9399c76: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"

2025-06-30 14:59:41 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 24, corr_id 895aaa41-37c0-4d36-805b-c66511b28040):
2025-06-30 14:59:41 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 895aaa41-37c0-4d36-805b-c66511b28040, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition faced an error during execution.', 'result': '[ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id f2f7adc3-52c5-400a-a517-2568d9399c76: "Required field \'input_data\', \'input_datta\', or \'text_inputs\' not found in parameters"', 'status': 'failed', 'sequence': 24, 'workflow_status': 'running'}
2025-06-30 14:59:41 - TransitionHandler - ERROR - Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id f2f7adc3-52c5-400a-a517-2568d9399c76: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-06-30 14:59:41 - LoopExecutor - ERROR - ❌ Iteration chain execution failed: Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id f2f7adc3-52c5-400a-a517-2568d9399c76: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-06-30 14:59:41 - LoopExecutor - ERROR - ❌ Iteration 5 failed: Iteration chain execution failed: Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id f2f7adc3-52c5-400a-a517-2568d9399c76: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-06-30 14:59:41 - LoopExecutor - INFO - 🔄 Starting iteration 6/6
2025-06-30 14:59:41 - LoopExecutor - DEBUG - 🔄 Executing iteration 6 chain directly
2025-06-30 14:59:41 - StateManager - DEBUG - Stored result for transition loop_iteration_5 in memory: 6
2025-06-30 14:59:41 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_5
2025-06-30 14:59:41 - RedisManager - DEBUG - Set key 'result:loop_iteration_5' with TTL of 900 seconds
2025-06-30 14:59:41 - StateManager - DEBUG - Stored result for transition loop_iteration_5 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:59:41 - StateManager - INFO - Marked transition loop_iteration_5 as completed (was_pending=False, was_waiting=False)
2025-06-30 14:59:41 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'backup_transition-LoopNode-*************_iteration_2', 'loop_iteration_4', 'loop_iteration_transition-LoopNode-*************_0', 'loop_iteration_2', 'backup_transition-LoopNode-*************_iteration_3', 'loop_iteration_1', 'loop_iteration_3', 'loop_iteration_5', 'loop_iteration_transition-LoopNode-*************_3', 'loop_iteration_transition-LoopNode-*************_2', 'loop_iteration_0', 'backup_transition-LoopNode-*************_iteration_4', 'current_iteration', 'loop_iteration_transition-LoopNode-*************_4', 'backup_transition-LoopNode-*************_iteration_1', 'transition-AgenticAI-*************', 'backup_transition-LoopNode-*************_iteration_0', 'loop_iteration_transition-LoopNode-*************_1', 'transition-AgenticAI-*************'}
2025-06-30 14:59:41 - StateManager - DEBUG - Stored result for transition current_iteration in memory: 6
2025-06-30 14:59:42 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-06-30 14:59:42 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-06-30 14:59:42 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:59:42 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-06-30 14:59:42 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'backup_transition-LoopNode-*************_iteration_2', 'loop_iteration_4', 'loop_iteration_transition-LoopNode-*************_0', 'loop_iteration_2', 'backup_transition-LoopNode-*************_iteration_3', 'loop_iteration_1', 'loop_iteration_3', 'loop_iteration_5', 'loop_iteration_transition-LoopNode-*************_3', 'loop_iteration_transition-LoopNode-*************_2', 'loop_iteration_0', 'backup_transition-LoopNode-*************_iteration_4', 'current_iteration', 'loop_iteration_transition-LoopNode-*************_4', 'backup_transition-LoopNode-*************_iteration_1', 'transition-AgenticAI-*************', 'backup_transition-LoopNode-*************_iteration_0', 'loop_iteration_transition-LoopNode-*************_1', 'transition-AgenticAI-*************'}
2025-06-30 14:59:42 - StateManager - DEBUG - Stored result for transition loop_iteration_transition-LoopNode-*************_5 in memory: {'current_item': 6, 'iteration_index': 5, 'iteration_metadata': {'timestamp': 312870.861575958, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-06-30 14:59:42 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_transition-LoopNode-*************_5
2025-06-30 14:59:43 - RedisManager - DEBUG - Set key 'result:loop_iteration_transition-LoopNode-*************_5' with TTL of 900 seconds
2025-06-30 14:59:43 - StateManager - DEBUG - Stored result for transition loop_iteration_transition-LoopNode-*************_5 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:59:43 - StateManager - INFO - Marked transition loop_iteration_transition-LoopNode-*************_5 as completed (was_pending=False, was_waiting=False)
2025-06-30 14:59:43 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'backup_transition-LoopNode-*************_iteration_2', 'loop_iteration_4', 'loop_iteration_transition-LoopNode-*************_0', 'loop_iteration_2', 'backup_transition-LoopNode-*************_iteration_3', 'loop_iteration_1', 'loop_iteration_3', 'loop_iteration_transition-LoopNode-*************_5', 'loop_iteration_5', 'loop_iteration_transition-LoopNode-*************_3', 'loop_iteration_transition-LoopNode-*************_2', 'loop_iteration_0', 'backup_transition-LoopNode-*************_iteration_4', 'current_iteration', 'loop_iteration_transition-LoopNode-*************_4', 'backup_transition-LoopNode-*************_iteration_1', 'transition-AgenticAI-*************', 'backup_transition-LoopNode-*************_iteration_0', 'loop_iteration_transition-LoopNode-*************_1', 'transition-AgenticAI-*************'}
2025-06-30 14:59:43 - StateManager - DEBUG - Stored result for transition backup_transition-LoopNode-*************_iteration_5 in memory: {'current_item': 6, 'iteration_index': 5, 'iteration_metadata': {'timestamp': 312870.861575958, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-06-30 14:59:44 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:backup_transition-LoopNode-*************_iteration_5'
2025-06-30 14:59:44 - RedisManager - DEBUG - Set key 'result:backup_transition-LoopNode-*************_iteration_5' with TTL of 300 seconds
2025-06-30 14:59:44 - StateManager - DEBUG - Stored result for transition backup_transition-LoopNode-*************_iteration_5 in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:59:44 - StateManager - INFO - Marked transition backup_transition-LoopNode-*************_iteration_5 as completed (was_pending=False, was_waiting=False)
2025-06-30 14:59:44 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'backup_transition-LoopNode-*************_iteration_2', 'backup_transition-LoopNode-*************_iteration_5', 'loop_iteration_4', 'loop_iteration_transition-LoopNode-*************_0', 'loop_iteration_2', 'backup_transition-LoopNode-*************_iteration_3', 'loop_iteration_1', 'loop_iteration_3', 'loop_iteration_transition-LoopNode-*************_5', 'loop_iteration_5', 'loop_iteration_transition-LoopNode-*************_3', 'loop_iteration_transition-LoopNode-*************_2', 'loop_iteration_0', 'backup_transition-LoopNode-*************_iteration_4', 'current_iteration', 'loop_iteration_transition-LoopNode-*************_4', 'backup_transition-LoopNode-*************_iteration_1', 'transition-AgenticAI-*************', 'backup_transition-LoopNode-*************_iteration_0', 'loop_iteration_transition-LoopNode-*************_1', 'transition-AgenticAI-*************'}
2025-06-30 14:59:44 - LoopExecutor - DEBUG - 💾 Temporarily stored current iteration data under loop transition ID: transition-LoopNode-*************
2025-06-30 14:59:44 - LoopExecutor - DEBUG - 💾 Also stored with backup keys: loop_iteration_transition-LoopNode-*************_5, backup_transition-LoopNode-*************_iteration_5
2025-06-30 14:59:44 - LoopExecutor - DEBUG - 💾 Current iteration data: {'current_item': 6, 'iteration_index': 5, 'iteration_metadata': {'timestamp': 312870.861575958, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-06-30 14:59:44 - LoopExecutor - DEBUG - 💾 Injected iteration payload into state: loop_iteration_5
2025-06-30 14:59:44 - LoopExecutor - DEBUG - 🚀 Executing transition: transition-CombineTextComponent-*************
2025-06-30 14:59:44 - LoopExecutor - DEBUG - 🔧 About to execute transition transition-CombineTextComponent-*************
2025-06-30 14:59:44 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-30 14:59:44 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 25, corr_id 895aaa41-37c0-4d36-805b-c66511b28040):
2025-06-30 14:59:44 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 895aaa41-37c0-4d36-805b-c66511b28040, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 25, 'workflow_status': 'running'}
2025-06-30 14:59:44 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-06-30 14:59:44 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-30 14:59:44 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-30 14:59:44 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-30 14:59:44 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-30 14:59:45 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-06-30 14:59:45 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-06-30 14:59:45 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-06-30 14:59:45 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-06-30 14:59:45 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-06-30 14:59:45 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-06-30 14:59:45 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-06-30 14:59:45 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-06-30 14:59:46 - StateManager - DEBUG - No result found in Redis for transition transition-LoopNode-*************. Trying PostgreSQL next.
2025-06-30 14:59:48 - PostgresManager - DEBUG - No result found for transition transition-LoopNode-************* in correlation 895aaa41-37c0-4d36-805b-c66511b28040
2025-06-30 14:59:49 - StateManager - DEBUG - No result found in PostgreSQL for transition transition-LoopNode-*************. Trying in-memory next.
2025-06-30 14:59:49 - StateManager - INFO - Using in-memory result for transition transition-LoopNode-*************.
2025-06-30 14:59:49 - StateManager - DEBUG - Extracted results for 3 tools in transition transition-LoopNode-*************
2025-06-30 14:59:49 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-30 14:59:49 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 1)
2025-06-30 14:59:49 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 2)
2025-06-30 14:59:49 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-30 14:59:49 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!"}
2025-06-30 14:59:49 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-30 14:59:49 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!
2025-06-30 14:59:49 - WorkflowUtils - DEBUG - Found result.result: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you! (type: <class 'str'>)
2025-06-30 14:59:49 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-30 14:59:49 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-06-30 14:59:49 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!"}
2025-06-30 14:59:49 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-30 14:59:49 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!
2025-06-30 14:59:49 - WorkflowUtils - DEBUG - Found result.result: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you! (type: <class 'str'>)
2025-06-30 14:59:49 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-30 14:59:49 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-06-30 14:59:49 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 6, 'iteration_index': 5, 'iteration_metadata': {'timestamp': 312870.861575958, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-06-30 14:59:49 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-30 14:59:49 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-30 14:59:49 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (3/3 compatible)
2025-06-30 14:59:49 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (3/3 compatible)
2025-06-30 14:59:49 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!"}
2025-06-30 14:59:49 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-30 14:59:49 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!
2025-06-30 14:59:49 - WorkflowUtils - DEBUG - Found result.result: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you! (type: <class 'str'>)
2025-06-30 14:59:49 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-30 14:59:49 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-06-30 14:59:49 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → input_2 via path 'result': I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!
2025-06-30 14:59:49 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!"}
2025-06-30 14:59:49 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-30 14:59:49 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!
2025-06-30 14:59:49 - WorkflowUtils - DEBUG - Found result.result: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you! (type: <class 'str'>)
2025-06-30 14:59:49 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-30 14:59:49 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-06-30 14:59:49 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → input_1 via path 'result': I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!
2025-06-30 14:59:49 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 6, 'iteration_index': 5, 'iteration_metadata': {'timestamp': 312870.861575958, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-06-30 14:59:49 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-30 14:59:49 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-30 14:59:49 - WorkflowUtils - DEBUG - ✅ Handle mapping success: current_item → main_input via path 'current_item': 6
2025-06-30 14:59:49 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 3/3 successful
2025-06-30 14:59:49 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 3/3 successful
2025-06-30 14:59:49 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-30 14:59:49 - WorkflowUtils - DEBUG - Filtering out field 'input_1' with null/empty value: None
2025-06-30 14:59:49 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: None
2025-06-30 14:59:49 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-06-30 14:59:49 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-06-30 14:59:49 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-06-30 14:59:49 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-06-30 14:59:49 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-06-30 14:59:49 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-06-30 14:59:49 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-06-30 14:59:49 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-06-30 14:59:49 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 2 fields (11 null/empty fields removed)
2025-06-30 14:59:49 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-30 14:59:49 - TransitionHandler - DEBUG - 📌 Added static parameter: separator = \n
2025-06-30 14:59:49 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'input_2': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'input_1': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'main_input': 6, 'num_additional_inputs': '1', 'separator': '\\n'}
2025-06-30 14:59:49 - TransitionHandler - DEBUG - tool Parameters: {'input_2': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'input_1': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'main_input': 6, 'num_additional_inputs': '1', 'separator': '\\n'}
2025-06-30 14:59:49 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'input_2': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'input_1': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'main_input': 6, 'num_additional_inputs': '1', 'separator': '\\n'}
2025-06-30 14:59:49 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 26, corr_id 895aaa41-37c0-4d36-805b-c66511b28040):
2025-06-30 14:59:49 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 895aaa41-37c0-4d36-805b-c66511b28040, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 26, 'workflow_status': 'running'}
2025-06-30 14:59:49 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: c0832bdc-ede7-4f7a-bf9a-64eb6a43f7c5) using provided producer.
2025-06-30 14:59:49 - NodeExecutor - DEBUG - Added correlation_id 895aaa41-37c0-4d36-805b-c66511b28040 to payload
2025-06-30 14:59:49 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-06-30 14:59:49 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'input_2': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'input_1': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'main_input': 6, 'num_additional_inputs': '1', 'separator': '\\n'}, 'request_id': 'c0832bdc-ede7-4f7a-bf9a-64eb6a43f7c5', 'correlation_id': '895aaa41-37c0-4d36-805b-c66511b28040', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-06-30 14:59:49 - NodeExecutor - DEBUG - Request c0832bdc-ede7-4f7a-bf9a-64eb6a43f7c5 sent successfully using provided producer.
2025-06-30 14:59:49 - NodeExecutor - DEBUG - Waiting indefinitely for result for request c0832bdc-ede7-4f7a-bf9a-64eb6a43f7c5...
2025-06-30 14:59:49 - NodeExecutor - DEBUG - Result consumer received message: Offset=949
2025-06-30 14:59:49 - NodeExecutor - WARNING - Received error response for request_id c0832bdc-ede7-4f7a-bf9a-64eb6a43f7c5: Error combining text for request_id c0832bdc-ede7-4f7a-bf9a-64eb6a43f7c5: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-06-30 14:59:49 - NodeExecutor - ERROR - Error during node execution c0832bdc-ede7-4f7a-bf9a-64eb6a43f7c5: Node execution failed: Error combining text for request_id c0832bdc-ede7-4f7a-bf9a-64eb6a43f7c5: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id c0832bdc-ede7-4f7a-bf9a-64eb6a43f7c5: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-06-30 14:59:49 - TransitionHandler - ERROR - Tool execution failed for tool 'CombineTextComponent' (tool_id: 1) in node 'CombineTextComponent' of transition 'transition-CombineTextComponent-*************': Node execution failed: Error combining text for request_id c0832bdc-ede7-4f7a-bf9a-64eb6a43f7c5: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 492, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 361, in execute_tool
    raise e
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id c0832bdc-ede7-4f7a-bf9a-64eb6a43f7c5: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"

2025-06-30 14:59:49 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 27, corr_id 895aaa41-37c0-4d36-805b-c66511b28040):
2025-06-30 14:59:49 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 895aaa41-37c0-4d36-805b-c66511b28040, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition faced an error during execution.', 'result': '[ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id c0832bdc-ede7-4f7a-bf9a-64eb6a43f7c5: "Required field \'input_data\', \'input_datta\', or \'text_inputs\' not found in parameters"', 'status': 'failed', 'sequence': 27, 'workflow_status': 'running'}
2025-06-30 14:59:49 - TransitionHandler - ERROR - Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id c0832bdc-ede7-4f7a-bf9a-64eb6a43f7c5: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-06-30 14:59:49 - LoopExecutor - ERROR - ❌ Iteration chain execution failed: Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id c0832bdc-ede7-4f7a-bf9a-64eb6a43f7c5: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-06-30 14:59:49 - LoopExecutor - ERROR - ❌ Iteration 6 failed: Iteration chain execution failed: Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id c0832bdc-ede7-4f7a-bf9a-64eb6a43f7c5: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-06-30 14:59:49 - LoopExecutor - INFO - 🔄 CALLING NEW AGGREGATION METHOD with 0 iteration results
2025-06-30 14:59:49 - LoopExecutor - DEBUG - 🔄 Aggregating 0 iteration results using aggregation_type: collect_all
2025-06-30 14:59:49 - LoopExecutor - DEBUG - 🔍 Extracted 0 data items from iterations
2025-06-30 14:59:49 - LoopExecutor - DEBUG - 🔍 Sample extracted data: None
2025-06-30 14:59:49 - LoopExecutor - INFO - 🔄 NEW AGGREGATION METHOD RETURNED: []
2025-06-30 14:59:49 - LoopExecutor - INFO - 📊 Loop execution completed with metadata: {'success': True, 'final_results': [], 'iteration_count': 0, 'total_iterations': 6}
2025-06-30 14:59:49 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 28, corr_id 895aaa41-37c0-4d36-805b-c66511b28040):
2025-06-30 14:59:49 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 895aaa41-37c0-4d36-805b-c66511b28040, response: {'status': 'loop_completed', 'metadata': {'success': True, 'final_results': [], 'iteration_count': 0, 'total_iterations': 6}, 'transition_id': 'transition-LoopNode-*************', 'sequence': 28, 'workflow_status': 'running'}
2025-06-30 14:59:49 - LoopExecutor - INFO - ✅ Loop node execution completed for transition: transition-LoopNode-*************
2025-06-30 14:59:49 - TransitionHandler - DEBUG - 🗑️ Unregistered loop executor for transition: transition-LoopNode-*************
2025-06-30 14:59:49 - LoopExecutor - DEBUG - 🗑️ Unregistered loop executor from transition handler for transition: transition-LoopNode-*************
2025-06-30 14:59:49 - TransitionHandler - INFO - Execution result from loop executor: {
  "final_results": []
}
2025-06-30 14:59:49 - TransitionHandler - INFO - Checking execution result for errors: {
  "final_results": []
}
2025-06-30 14:59:49 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 29, corr_id 895aaa41-37c0-4d36-805b-c66511b28040):
2025-06-30 14:59:49 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 895aaa41-37c0-4d36-805b-c66511b28040, response: {'transition_id': 'transition-LoopNode-*************', 'node_id': 'LoopNode', 'tool_name': 'LoopNode', 'message': 'Transition Result received.', 'result': {'final_results': []}, 'status': 'completed', 'sequence': 29, 'workflow_status': 'running', 'approval_required': False}
2025-06-30 14:59:49 - TransitionHandler - INFO - 🔄 Handling loop completion for transition: transition-LoopNode-*************
2025-06-30 14:59:49 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in memory: {'final_results': []}
2025-06-30 14:59:50 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-LoopNode-*************'
2025-06-30 14:59:50 - RedisManager - DEBUG - Set key 'result:transition-LoopNode-*************' with TTL of 300 seconds
2025-06-30 14:59:50 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:59:50 - StateManager - INFO - Marked transition transition-LoopNode-************* as completed (was_pending=False, was_waiting=False)
2025-06-30 14:59:50 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'backup_transition-LoopNode-*************_iteration_2', 'backup_transition-LoopNode-*************_iteration_5', 'transition-LoopNode-*************', 'loop_iteration_4', 'loop_iteration_transition-LoopNode-*************_0', 'loop_iteration_2', 'backup_transition-LoopNode-*************_iteration_3', 'loop_iteration_1', 'loop_iteration_3', 'loop_iteration_transition-LoopNode-*************_5', 'loop_iteration_5', 'loop_iteration_transition-LoopNode-*************_3', 'loop_iteration_transition-LoopNode-*************_2', 'loop_iteration_0', 'backup_transition-LoopNode-*************_iteration_4', 'current_iteration', 'loop_iteration_transition-LoopNode-*************_4', 'backup_transition-LoopNode-*************_iteration_1', 'transition-AgenticAI-*************', 'backup_transition-LoopNode-*************_iteration_0', 'loop_iteration_transition-LoopNode-*************_1', 'transition-AgenticAI-*************'}
2025-06-30 14:59:50 - TransitionHandler - DEBUG - 🔍 Detected exit transition: transition-MergeDataComponent-************* (has final/aggregated indicators)
2025-06-30 14:59:50 - TransitionHandler - DEBUG - 🔍 Auto-detected loop body transition: transition-CombineTextComponent-************* (has current_item/iteration indicators)
2025-06-30 14:59:50 - TransitionHandler - INFO - 🔍 Auto-detected loop body transitions: ['transition-CombineTextComponent-*************']
2025-06-30 14:59:50 - TransitionHandler - DEBUG - 🔍 Loop completion filtering - Loop config: {'iteration_behavior': 'independent', 'iteration_source': {'number_range': {'start': 1, 'end': 6}, 'step': 1}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'iteration_settings': {'parallel_execution': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': False}, 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-CombineTextComponent-1750920624318'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}, 'error_handling': {'on_iteration_error': 'retry_once', 'include_errors': True}}
2025-06-30 14:59:50 - TransitionHandler - DEBUG - 🔍 Loop body transitions to filter: ['transition-CombineTextComponent-*************']
2025-06-30 14:59:50 - TransitionHandler - DEBUG - 🔍 Output data configs: [{'to_transition_id': 'transition-MergeDataComponent-*************', 'target_node_id': 'Merge Data', 'data_type': 'string', 'output_handle_registry': {'handle_mappings': [{'handle_id': 'final_results', 'result_path': 'final_results', 'edge_id': 'reactflow__edge-LoopNode-*************final_results-MergeDataComponent-*************main_input'}]}}, {'to_transition_id': 'transition-CombineTextComponent-*************', 'target_node_id': 'Combine Text', 'data_type': 'string', 'output_handle_registry': {'handle_mappings': [{'handle_id': 'current_item', 'result_path': 'current_item', 'edge_id': 'reactflow__edge-LoopNode-*************current_item-CombineTextComponent-*************main_input'}]}}]
2025-06-30 14:59:50 - TransitionHandler - DEBUG - ✅ Adding exit transition 'transition-MergeDataComponent-*************' to next transitions
2025-06-30 14:59:50 - TransitionHandler - DEBUG - 🚫 Skipping loop body transition 'transition-CombineTextComponent-*************' - should have been executed internally by loop executor
2025-06-30 14:59:50 - TransitionHandler - INFO - ✅ Loop completion handled for transition-LoopNode-*************. Next transitions: ['transition-MergeDataComponent-*************']
2025-06-30 14:59:50 - TransitionHandler - INFO - Completed transition transition-LoopNode-************* in 54.96 seconds
2025-06-30 14:59:50 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 30, corr_id 895aaa41-37c0-4d36-805b-c66511b28040):
2025-06-30 14:59:50 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 895aaa41-37c0-4d36-805b-c66511b28040, response: {'result': 'Completed transition in 54.96 seconds', 'message': 'Transition completed in 54.96 seconds', 'transition_id': 'transition-LoopNode-*************', 'status': 'time_logged', 'sequence': 30, 'workflow_status': 'running'}
2025-06-30 14:59:50 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-LoopNode-*************: ['transition-MergeDataComponent-*************']
2025-06-30 14:59:50 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 1
2025-06-30 14:59:50 - EnhancedWorkflowEngine - DEBUG - Results: [['transition-MergeDataComponent-*************']]
2025-06-30 14:59:50 - EnhancedWorkflowEngine - DEBUG - 🔄 Orchestration engine received result for transition-LoopNode-*************: ['transition-MergeDataComponent-*************']
2025-06-30 14:59:50 - EnhancedWorkflowEngine - DEBUG - 🔄 Result type: <class 'list'>, is_list: True
2025-06-30 14:59:50 - EnhancedWorkflowEngine - INFO - Transition transition-LoopNode-************* completed successfully: 1 next transitions
2025-06-30 14:59:50 - EnhancedWorkflowEngine - DEBUG - 🔄 Added to next_transitions_to_execute: ['transition-MergeDataComponent-*************']
2025-06-30 14:59:50 - TransitionHandler - INFO - Resolved next transitions (direct transition IDs): ['transition-MergeDataComponent-*************']
2025-06-30 14:59:50 - EnhancedWorkflowEngine - INFO - Adding transition transition-MergeDataComponent-************* to pending (all dependencies met)
2025-06-30 14:59:50 - StateManager - DEBUG - Workflow active: {'transition-MergeDataComponent-*************'}
2025-06-30 14:59:51 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:895aaa41-37c0-4d36-805b-c66511b28040'
2025-06-30 14:59:51 - RedisManager - DEBUG - Set key 'workflow_state:895aaa41-37c0-4d36-805b-c66511b28040' with TTL of 600 seconds
2025-06-30 14:59:51 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 895aaa41-37c0-4d36-805b-c66511b28040. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:59:51 - StateManager - DEBUG - Checking waiting transitions: {'transition-CombineTextComponent-*************'}
2025-06-30 14:59:51 - StateManager - DEBUG - Transition transition-CombineTextComponent-************* is now ready (dependencies met: ['transition-AgenticAI-*************', 'transition-AgenticAI-*************', 'transition-LoopNode-*************'])
2025-06-30 14:59:51 - StateManager - INFO - Moved transitions from waiting to pending: {'transition-CombineTextComponent-*************'}
2025-06-30 14:59:51 - StateManager - DEBUG - Updated waiting=set(), pending={'transition-MergeDataComponent-*************', 'transition-CombineTextComponent-*************'}
2025-06-30 14:59:51 - StateManager - INFO - Cleared 2 pending transitions: {'transition-MergeDataComponent-*************', 'transition-CombineTextComponent-*************'}
2025-06-30 14:59:51 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-30 14:59:51 - StateManager - INFO - Terminated: False
2025-06-30 14:59:51 - StateManager - INFO - Pending transitions (0): []
2025-06-30 14:59:51 - StateManager - INFO - Waiting transitions (0): []
2025-06-30 14:59:51 - StateManager - INFO - Completed transitions (22): ['backup_transition-LoopNode-*************_iteration_0', 'backup_transition-LoopNode-*************_iteration_1', 'backup_transition-LoopNode-*************_iteration_2', 'backup_transition-LoopNode-*************_iteration_3', 'backup_transition-LoopNode-*************_iteration_4', 'backup_transition-LoopNode-*************_iteration_5', 'current_iteration', 'loop_iteration_0', 'loop_iteration_1', 'loop_iteration_2', 'loop_iteration_3', 'loop_iteration_4', 'loop_iteration_5', 'loop_iteration_transition-LoopNode-*************_0', 'loop_iteration_transition-LoopNode-*************_1', 'loop_iteration_transition-LoopNode-*************_2', 'loop_iteration_transition-LoopNode-*************_3', 'loop_iteration_transition-LoopNode-*************_4', 'loop_iteration_transition-LoopNode-*************_5', 'transition-AgenticAI-*************', 'transition-AgenticAI-*************', 'transition-LoopNode-*************']
2025-06-30 14:59:51 - StateManager - INFO - Results stored for 22 transitions
2025-06-30 14:59:51 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-30 14:59:51 - StateManager - INFO - Workflow status: inactive
2025-06-30 14:59:51 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-30 14:59:51 - StateManager - INFO - Workflow status: inactive
2025-06-30 14:59:51 - StateManager - INFO - Workflow paused: False
2025-06-30 14:59:51 - StateManager - INFO - ==============================
2025-06-30 14:59:51 - TransitionHandler - INFO - Starting parallel execution of transition: transition-MergeDataComponent-*************
2025-06-30 14:59:51 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 31, corr_id 895aaa41-37c0-4d36-805b-c66511b28040):
2025-06-30 14:59:51 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 895aaa41-37c0-4d36-805b-c66511b28040, response: {'result': 'Starting execution of transition: transition-MergeDataComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-MergeDataComponent-*************', 'status': 'started', 'sequence': 31, 'workflow_status': 'running'}
2025-06-30 14:59:51 - TransitionHandler - EXECUTE - Transition 'transition-MergeDataComponent-*************' (type=standard, execution_type=Components)
2025-06-30 14:59:51 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-30 14:59:51 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-MergeDataComponent-*************
2025-06-30 14:59:51 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-30 14:59:51 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-30 14:59:52 - StateManager - DEBUG - Retrieved result for transition transition-LoopNode-************* from Redis
2025-06-30 14:59:52 - StateManager - DEBUG - Extracted results for 1 tools in transition transition-LoopNode-*************
2025-06-30 14:59:52 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-30 14:59:52 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-30 14:59:52 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_results': {'final_results': []}
2025-06-30 14:59:52 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['final_results']
2025-06-30 14:59:52 - WorkflowUtils - DEBUG - Path tracking - Found handle 'final_results' directly in source_results
2025-06-30 14:59:52 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-30 14:59:52 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-30 14:59:52 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_results': {'final_results': []}
2025-06-30 14:59:52 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['final_results']
2025-06-30 14:59:52 - WorkflowUtils - DEBUG - Path tracking - Found handle 'final_results' directly in source_results
2025-06-30 14:59:52 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_results → main_input via path 'final_results': []
2025-06-30 14:59:52 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-30 14:59:52 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-30 14:59:52 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-30 14:59:52 - WorkflowUtils - DEBUG - Filtering out field 'output_key_1' with null/empty value: 
2025-06-30 14:59:52 - WorkflowUtils - DEBUG - Filtering out field 'output_key_2' with null/empty value: 
2025-06-30 14:59:52 - WorkflowUtils - DEBUG - Filtering out field 'output_key_3' with null/empty value: 
2025-06-30 14:59:52 - WorkflowUtils - DEBUG - Filtering out field 'output_key_4' with null/empty value: 
2025-06-30 14:59:52 - WorkflowUtils - DEBUG - Filtering out field 'output_key_5' with null/empty value: 
2025-06-30 14:59:52 - WorkflowUtils - DEBUG - Filtering out field 'output_key_6' with null/empty value: 
2025-06-30 14:59:52 - WorkflowUtils - DEBUG - Filtering out field 'output_key_7' with null/empty value: 
2025-06-30 14:59:52 - WorkflowUtils - DEBUG - Filtering out field 'output_key_8' with null/empty value: 
2025-06-30 14:59:52 - WorkflowUtils - DEBUG - Filtering out field 'output_key_9' with null/empty value: 
2025-06-30 14:59:52 - WorkflowUtils - DEBUG - Filtering out field 'output_key_10' with null/empty value: 
2025-06-30 14:59:52 - WorkflowUtils - DEBUG - Filtering out field 'output_key_11' with null/empty value: 
2025-06-30 14:59:52 - WorkflowUtils - DEBUG - Filtering out field 'input_1' with empty collection: {}
2025-06-30 14:59:52 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with empty collection: {}
2025-06-30 14:59:52 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with empty collection: {}
2025-06-30 14:59:52 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with empty collection: {}
2025-06-30 14:59:52 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with empty collection: {}
2025-06-30 14:59:52 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with empty collection: {}
2025-06-30 14:59:52 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with empty collection: {}
2025-06-30 14:59:52 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with empty collection: {}
2025-06-30 14:59:52 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with empty collection: {}
2025-06-30 14:59:52 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with empty collection: {}
2025-06-30 14:59:52 - WorkflowUtils - INFO - 🧹 Parameter filtering: 24 → 2 fields (22 null/empty fields removed)
2025-06-30 14:59:52 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-30 14:59:52 - TransitionHandler - DEBUG - 📌 Added static parameter: merge_strategy = Aggregate
2025-06-30 14:59:52 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'main_input': [], 'num_additional_inputs': '1', 'merge_strategy': 'Aggregate'}
2025-06-30 14:59:52 - TransitionHandler - DEBUG - tool Parameters: {'main_input': [], 'num_additional_inputs': '1', 'merge_strategy': 'Aggregate'}
2025-06-30 14:59:52 - TransitionHandler - INFO - Invoking tool 'MergeDataComponent' (tool_id: 1) for node 'MergeDataComponent' in transition 'transition-MergeDataComponent-*************' with parameters: {'main_input': [], 'num_additional_inputs': '1', 'merge_strategy': 'Aggregate'}
2025-06-30 14:59:52 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 32, corr_id 895aaa41-37c0-4d36-805b-c66511b28040):
2025-06-30 14:59:52 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 895aaa41-37c0-4d36-805b-c66511b28040, response: {'transition_id': 'transition-MergeDataComponent-*************', 'node_id': 'MergeDataComponent', 'tool_name': 'MergeDataComponent', 'message': 'Connecting to server', 'result': 'Connecting to server MergeDataComponent', 'status': 'connecting', 'sequence': 32, 'workflow_status': 'running'}
2025-06-30 14:59:52 - NodeExecutor - INFO - Executing tool 'MergeDataComponent' via Kafka (request_id: f98be01f-3def-4687-b3ae-f80695acea65) using provided producer.
2025-06-30 14:59:52 - NodeExecutor - DEBUG - Added correlation_id 895aaa41-37c0-4d36-805b-c66511b28040 to payload
2025-06-30 14:59:52 - NodeExecutor - DEBUG - Added transition_id transition-MergeDataComponent-************* to payload
2025-06-30 14:59:52 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'MergeDataComponent', 'tool_parameters': {'main_input': [], 'num_additional_inputs': '1', 'merge_strategy': 'Aggregate'}, 'request_id': 'f98be01f-3def-4687-b3ae-f80695acea65', 'correlation_id': '895aaa41-37c0-4d36-805b-c66511b28040', 'transition_id': 'transition-MergeDataComponent-*************'}
2025-06-30 14:59:52 - NodeExecutor - DEBUG - Request f98be01f-3def-4687-b3ae-f80695acea65 sent successfully using provided producer.
2025-06-30 14:59:52 - NodeExecutor - DEBUG - Waiting indefinitely for result for request f98be01f-3def-4687-b3ae-f80695acea65...
2025-06-30 14:59:52 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-30 14:59:52 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 33, corr_id 895aaa41-37c0-4d36-805b-c66511b28040):
2025-06-30 14:59:52 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 895aaa41-37c0-4d36-805b-c66511b28040, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 33, 'workflow_status': 'running'}
2025-06-30 14:59:52 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-06-30 14:59:52 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-30 14:59:52 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-30 14:59:52 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-30 14:59:52 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-30 14:59:53 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-06-30 14:59:53 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-06-30 14:59:53 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-06-30 14:59:53 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-06-30 14:59:53 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-06-30 14:59:53 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-06-30 14:59:53 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-06-30 14:59:53 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-06-30 14:59:54 - StateManager - DEBUG - Retrieved result for transition transition-LoopNode-************* from Redis
2025-06-30 14:59:54 - StateManager - DEBUG - Extracted results for 1 tools in transition transition-LoopNode-*************
2025-06-30 14:59:54 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-30 14:59:54 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 1)
2025-06-30 14:59:54 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 2)
2025-06-30 14:59:54 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-30 14:59:54 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!"}
2025-06-30 14:59:54 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-30 14:59:54 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!
2025-06-30 14:59:54 - WorkflowUtils - DEBUG - Found result.result: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you! (type: <class 'str'>)
2025-06-30 14:59:54 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-30 14:59:54 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-06-30 14:59:54 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!"}
2025-06-30 14:59:54 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-30 14:59:54 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!
2025-06-30 14:59:54 - WorkflowUtils - DEBUG - Found result.result: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you! (type: <class 'str'>)
2025-06-30 14:59:54 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-30 14:59:54 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-06-30 14:59:54 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'final_results': []}
2025-06-30 14:59:54 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['final_results']
2025-06-30 14:59:54 - WorkflowUtils - DEBUG - Could not find result.result or result in source_results for handle current_item
2025-06-30 14:59:54 - WorkflowUtils - DEBUG - Path tracking - Source results structure
2025-06-30 14:59:54 - WorkflowUtils - INFO - 🔍 Handle mapping validation: partially_compatible (2/3 compatible)
2025-06-30 14:59:54 - TransitionHandler - INFO - 🔍 Handle validation: partially_compatible (2/3 compatible)
2025-06-30 14:59:54 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!"}
2025-06-30 14:59:54 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-30 14:59:54 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!
2025-06-30 14:59:54 - WorkflowUtils - DEBUG - Found result.result: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you! (type: <class 'str'>)
2025-06-30 14:59:54 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-30 14:59:54 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-06-30 14:59:54 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → input_2 via path 'result': I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!
2025-06-30 14:59:54 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!"}
2025-06-30 14:59:54 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-30 14:59:54 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!
2025-06-30 14:59:54 - WorkflowUtils - DEBUG - Found result.result: I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you! (type: <class 'str'>)
2025-06-30 14:59:54 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-30 14:59:54 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-06-30 14:59:54 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → input_1 via path 'result': I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!
2025-06-30 14:59:54 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'final_results': []}
2025-06-30 14:59:54 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['final_results']
2025-06-30 14:59:54 - WorkflowUtils - DEBUG - Could not find result.result or result in source_results for handle current_item
2025-06-30 14:59:54 - WorkflowUtils - DEBUG - Path tracking - Source results structure
2025-06-30 14:59:54 - WorkflowUtils - WARNING - ❌ Handle mapping failed: current_item → main_input (no data found)
2025-06-30 14:59:54 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 2/3 successful
2025-06-30 14:59:54 - WorkflowUtils - WARNING - ⚠️ 1 universal handle mappings failed - this may cause tool execution errors
2025-06-30 14:59:54 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 2/3 successful
2025-06-30 14:59:54 - TransitionHandler - WARNING - ❌ Failed mapping: current_item → main_input (Error: No data found for handle current_item)
2025-06-30 14:59:54 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-30 14:59:54 - WorkflowUtils - DEBUG - Filtering out field 'input_1' with null/empty value: None
2025-06-30 14:59:54 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: None
2025-06-30 14:59:54 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-06-30 14:59:54 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-06-30 14:59:54 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-06-30 14:59:54 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-06-30 14:59:54 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-06-30 14:59:54 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-06-30 14:59:54 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-06-30 14:59:54 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-06-30 14:59:54 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 2 fields (11 null/empty fields removed)
2025-06-30 14:59:54 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-30 14:59:54 - TransitionHandler - DEBUG - 📌 Added static parameter: separator = \n
2025-06-30 14:59:54 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'input_2': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'input_1': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'num_additional_inputs': '1', 'separator': '\\n'}
2025-06-30 14:59:54 - TransitionHandler - DEBUG - tool Parameters: {'input_2': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'input_1': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'num_additional_inputs': '1', 'separator': '\\n'}
2025-06-30 14:59:54 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'input_2': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'input_1': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'num_additional_inputs': '1', 'separator': '\\n'}
2025-06-30 14:59:54 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 34, corr_id 895aaa41-37c0-4d36-805b-c66511b28040):
2025-06-30 14:59:54 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 895aaa41-37c0-4d36-805b-c66511b28040, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 34, 'workflow_status': 'running'}
2025-06-30 14:59:54 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 4f07c94f-bf44-4e35-922a-cd5e7525b336) using provided producer.
2025-06-30 14:59:54 - NodeExecutor - DEBUG - Added correlation_id 895aaa41-37c0-4d36-805b-c66511b28040 to payload
2025-06-30 14:59:54 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-06-30 14:59:54 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'input_2': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'input_1': "I'm here to help! Are you looking for information or tips about enjoying and understanding nature? Let me know what specific aspects of nature you’re interested in, such as wildlife, ecosystems, or outdoor activities, and I'll do my best to assist you!", 'num_additional_inputs': '1', 'separator': '\\n'}, 'request_id': '4f07c94f-bf44-4e35-922a-cd5e7525b336', 'correlation_id': '895aaa41-37c0-4d36-805b-c66511b28040', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-06-30 14:59:54 - NodeExecutor - DEBUG - Request 4f07c94f-bf44-4e35-922a-cd5e7525b336 sent successfully using provided producer.
2025-06-30 14:59:54 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 4f07c94f-bf44-4e35-922a-cd5e7525b336...
2025-06-30 14:59:55 - NodeExecutor - DEBUG - Result consumer received message: Offset=950
2025-06-30 14:59:55 - NodeExecutor - DEBUG - Received valid result for request_id f98be01f-3def-4687-b3ae-f80695acea65
2025-06-30 14:59:55 - NodeExecutor - DEBUG - Result consumer received message: Offset=951
2025-06-30 14:59:55 - NodeExecutor - WARNING - Received error response for request_id 4f07c94f-bf44-4e35-922a-cd5e7525b336: Error combining text for request_id 4f07c94f-bf44-4e35-922a-cd5e7525b336: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-06-30 14:59:55 - NodeExecutor - INFO - Result received for request f98be01f-3def-4687-b3ae-f80695acea65.
2025-06-30 14:59:55 - TransitionHandler - INFO - Execution result from Components executor: []
2025-06-30 14:59:55 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 35, corr_id 895aaa41-37c0-4d36-805b-c66511b28040):
2025-06-30 14:59:55 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 895aaa41-37c0-4d36-805b-c66511b28040, response: {'transition_id': 'transition-MergeDataComponent-*************', 'node_id': 'MergeDataComponent', 'tool_name': 'MergeDataComponent', 'message': 'Transition Result received.', 'result': [], 'status': 'completed', 'sequence': 35, 'workflow_status': 'running', 'approval_required': False}
2025-06-30 14:59:55 - StateManager - DEBUG - Stored result for transition transition-MergeDataComponent-************* in memory: {'MergeDataComponent': {'transition_id': 'transition-MergeDataComponent-*************', 'node_id': 'MergeDataComponent', 'tool_name': 'MergeDataComponent', 'result': {'result': []}, 'status': 'completed', 'timestamp': 1751275795.340492}}
2025-06-30 14:59:55 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-MergeDataComponent-*************'
2025-06-30 14:59:56 - RedisManager - DEBUG - Set key 'result:transition-MergeDataComponent-*************' with TTL of 300 seconds
2025-06-30 14:59:56 - StateManager - DEBUG - Stored result for transition transition-MergeDataComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:59:56 - StateManager - INFO - Marked transition transition-MergeDataComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-30 14:59:56 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'backup_transition-LoopNode-*************_iteration_2', 'backup_transition-LoopNode-*************_iteration_5', 'transition-LoopNode-*************', 'loop_iteration_4', 'loop_iteration_transition-LoopNode-*************_0', 'loop_iteration_2', 'backup_transition-LoopNode-*************_iteration_3', 'loop_iteration_1', 'transition-MergeDataComponent-*************', 'loop_iteration_3', 'loop_iteration_transition-LoopNode-*************_5', 'loop_iteration_5', 'loop_iteration_transition-LoopNode-*************_3', 'loop_iteration_transition-LoopNode-*************_2', 'loop_iteration_0', 'backup_transition-LoopNode-*************_iteration_4', 'current_iteration', 'loop_iteration_transition-LoopNode-*************_4', 'backup_transition-LoopNode-*************_iteration_1', 'transition-AgenticAI-*************', 'backup_transition-LoopNode-*************_iteration_0', 'loop_iteration_transition-LoopNode-*************_1', 'transition-AgenticAI-*************'}
2025-06-30 14:59:56 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-MergeDataComponent-*************
2025-06-30 14:59:56 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'list'>
2025-06-30 14:59:56 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-30 14:59:56 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'list'>
2025-06-30 14:59:56 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-MergeDataComponent-*************:
2025-06-30 14:59:56 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-30 14:59:56 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-30 14:59:56 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-MergeDataComponent-*************, returning empty list
2025-06-30 14:59:56 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-MergeDataComponent-*************
2025-06-30 14:59:56 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 0
2025-06-30 14:59:56 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: []
2025-06-30 14:59:56 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-06-30 14:59:56 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-06-30 14:59:56 - TransitionHandler - DEBUG - 🔗 Final next_transitions: []
2025-06-30 14:59:56 - TransitionHandler - INFO - Completed transition transition-MergeDataComponent-************* in 4.63 seconds
2025-06-30 14:59:56 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 36, corr_id 895aaa41-37c0-4d36-805b-c66511b28040):
2025-06-30 14:59:56 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 895aaa41-37c0-4d36-805b-c66511b28040, response: {'result': 'Completed transition in 4.63 seconds', 'message': 'Transition completed in 4.63 seconds', 'transition_id': 'transition-MergeDataComponent-*************', 'status': 'time_logged', 'sequence': 36, 'workflow_status': 'running'}
2025-06-30 14:59:56 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-MergeDataComponent-*************: []
2025-06-30 14:59:56 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 0
2025-06-30 14:59:56 - NodeExecutor - ERROR - Error during node execution 4f07c94f-bf44-4e35-922a-cd5e7525b336: Node execution failed: Error combining text for request_id 4f07c94f-bf44-4e35-922a-cd5e7525b336: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id 4f07c94f-bf44-4e35-922a-cd5e7525b336: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-06-30 14:59:56 - TransitionHandler - ERROR - Tool execution failed for tool 'CombineTextComponent' (tool_id: 1) in node 'CombineTextComponent' of transition 'transition-CombineTextComponent-*************': Node execution failed: Error combining text for request_id 4f07c94f-bf44-4e35-922a-cd5e7525b336: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 492, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 361, in execute_tool
    raise e
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id 4f07c94f-bf44-4e35-922a-cd5e7525b336: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"

2025-06-30 14:59:56 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 37, corr_id 895aaa41-37c0-4d36-805b-c66511b28040):
2025-06-30 14:59:56 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 895aaa41-37c0-4d36-805b-c66511b28040, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition faced an error during execution.', 'result': '[ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 4f07c94f-bf44-4e35-922a-cd5e7525b336: "Required field \'input_data\', \'input_datta\', or \'text_inputs\' not found in parameters"', 'status': 'failed', 'sequence': 37, 'workflow_status': 'running'}
2025-06-30 14:59:56 - TransitionHandler - ERROR - Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 4f07c94f-bf44-4e35-922a-cd5e7525b336: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-06-30 14:59:56 - EnhancedWorkflowEngine - DEBUG - Results: [[], Exception('Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 4f07c94f-bf44-4e35-922a-cd5e7525b336: "Required field \'input_data\', \'input_datta\', or \'text_inputs\' not found in parameters"')]
2025-06-30 14:59:56 - EnhancedWorkflowEngine - DEBUG - 🔄 Orchestration engine received result for transition-MergeDataComponent-*************: []
2025-06-30 14:59:56 - EnhancedWorkflowEngine - DEBUG - 🔄 Result type: <class 'list'>, is_list: True
2025-06-30 14:59:56 - StateManager - INFO - Workflow terminated flag set to: True
2025-06-30 14:59:56 - EnhancedWorkflowEngine - DEBUG - 🔄 Orchestration engine received result for transition-CombineTextComponent-*************: Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 4f07c94f-bf44-4e35-922a-cd5e7525b336: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-06-30 14:59:56 - EnhancedWorkflowEngine - DEBUG - 🔄 Result type: <class 'Exception'>, is_list: False
2025-06-30 14:59:56 - EnhancedWorkflowEngine - ERROR - Error in execution of transition transition-CombineTextComponent-*************: Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 4f07c94f-bf44-4e35-922a-cd5e7525b336: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-06-30 14:59:56 - EnhancedWorkflowEngine - ERROR - Traceback for transition transition-CombineTextComponent-*************: NoneType: None

2025-06-30 14:59:56 - EnhancedWorkflowEngine - ERROR - An unexpected error occurred during parallel execution of standard transitions: Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 4f07c94f-bf44-4e35-922a-cd5e7525b336: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-06-30 14:59:56 - EnhancedWorkflowEngine - ERROR - Traceback for unexpected error: Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 492, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 361, in execute_tool
    raise e
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id 4f07c94f-bf44-4e35-922a-cd5e7525b336: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 204, in _execute_transition_with_tracking
    result = await self._execute_standard_or_reflection_transition(transition)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 703, in _execute_standard_or_reflection_transition
    raise Exception(f"Tool execution error: {error_message}")
Exception: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 4f07c94f-bf44-4e35-922a-cd5e7525b336: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/executor_core.py", line 267, in execute
    raise result
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 230, in _execute_transition_with_tracking
    raise Exception(f"Exception in transition {transition_id}: {str(e)}")
Exception: Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 4f07c94f-bf44-4e35-922a-cd5e7525b336: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"

2025-06-30 14:59:56 - EnhancedWorkflowEngine - ERROR - An unexpected error occurred during workflow execution: Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 4f07c94f-bf44-4e35-922a-cd5e7525b336: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-06-30 14:59:56 - EnhancedWorkflowEngine - ERROR - Traceback for unexpected error: Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 492, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 361, in execute_tool
    raise e
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id 4f07c94f-bf44-4e35-922a-cd5e7525b336: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 204, in _execute_transition_with_tracking
    result = await self._execute_standard_or_reflection_transition(transition)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 703, in _execute_standard_or_reflection_transition
    raise Exception(f"Tool execution error: {error_message}")
Exception: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 4f07c94f-bf44-4e35-922a-cd5e7525b336: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/executor_core.py", line 285, in execute
    raise e
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/executor_core.py", line 267, in execute
    raise result
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 230, in _execute_transition_with_tracking
    raise Exception(f"Exception in transition {transition_id}: {str(e)}")
Exception: Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 4f07c94f-bf44-4e35-922a-cd5e7525b336: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"

2025-06-30 14:59:56 - KafkaWorkflowConsumer - ERROR - Exception in workflow execution: Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 4f07c94f-bf44-4e35-922a-cd5e7525b336: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-06-30 14:59:56 - KafkaWorkflowConsumer - INFO - Workflow '0c19c070-905e-46ef-9f57-62eb427bf396' final status: failed, result: Exception in workflow '0c19c070-905e-46ef-9f57-62eb427bf396': Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 4f07c94f-bf44-4e35-922a-cd5e7525b336: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-06-30 14:59:56 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 895aaa41-37c0-4d36-805b-c66511b28040, response: {'status': 'failed', 'result': 'Exception in workflow \'0c19c070-905e-46ef-9f57-62eb427bf396\': Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 4f07c94f-bf44-4e35-922a-cd5e7525b336: "Required field \'input_data\', \'input_datta\', or \'text_inputs\' not found in parameters"', 'workflow_status': 'failed', 'error': 'Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 4f07c94f-bf44-4e35-922a-cd5e7525b336: "Required field \'input_data\', \'input_datta\', or \'text_inputs\' not found in parameters"', 'error_type': 'Exception'}
2025-06-30 14:59:56 - KafkaWorkflowConsumer - DEBUG - Stopped workflow with correlation_id: 895aaa41-37c0-4d36-805b-c66511b28040 
2025-06-30 15:00:06 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-30 15:00:06 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 15:00:06 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 15:00:06 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-30 15:01:06 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-30 15:01:06 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 15:01:07 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 15:01:07 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-30 15:02:06 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-30 15:02:06 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 15:02:06 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 15:02:06 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-30 15:02:08 - KafkaWorkflowConsumer - INFO - Consumer task cancelled.
2025-06-30 15:02:08 - NodeExecutor - INFO - Result consumer loop cancelled.
2025-06-30 15:02:08 - MCPToolExecutor - INFO - Result consumer loop cancelled.
2025-06-30 15:02:08 - AgentExecutor - INFO - Result consumer loop cancelled.
2025-06-30 15:02:08 - Main - ERROR - Shutting down due to keyboard interrupt...
