2025-06-30 16:02:24 - Main - INFO - Starting Server
2025-06-30 16:02:24 - Main - INFO - Connection at: **************:9092
2025-06-30 16:02:24 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-06-30 16:02:24 - Node<PERSON>xecutor - INFO - NodeExecutor initialized.
2025-06-30 16:02:24 - AgentExecutor - INFO - AgentExecutor initialized.
2025-06-30 16:02:24 - KafkaWorkflowConsumer - INFO - Initializing database connections...
2025-06-30 16:02:24 - Red<PERSON><PERSON>anager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-30 16:02:26 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-30 16:02:26 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
