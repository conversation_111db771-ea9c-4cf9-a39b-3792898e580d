2025-06-30 17:39:52 - Main - INFO - Starting Server
2025-06-30 17:39:52 - Main - INFO - Connection at: **************:9092
2025-06-30 17:39:52 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-06-30 17:39:52 - Node<PERSON>xecutor - INFO - NodeExecutor initialized.
2025-06-30 17:39:52 - AgentExecutor - INFO - AgentExecutor initialized.
2025-06-30 17:39:52 - KafkaWorkflowConsumer - INFO - Initializing database connections...
2025-06-30 17:39:52 - Red<PERSON><PERSON>anager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-30 17:39:54 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-30 17:39:54 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-30 17:39:56 - <PERSON><PERSON><PERSON><PERSON><PERSON> - INFO - Successfully connected to Redis on DB index: 6!
2025-06-30 17:39:58 - PostgresManager - INFO - PostgreSQL connection pool created
2025-06-30 17:39:58 - PostgresManager - INFO - PostgreSQL connection pool is available
