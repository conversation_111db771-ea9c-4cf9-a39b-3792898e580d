2025-07-01 09:53:26 - Main - INFO - Starting Server
2025-07-01 09:53:26 - Main - INFO - Connection at: **************:9092
2025-07-01 09:53:26 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-07-01 09:53:26 - NodeExecutor - INFO - NodeExecutor initialized.
2025-07-01 09:53:26 - AgentExecutor - INFO - AgentExecutor initialized.
2025-07-01 09:53:26 - KafkaWorkflowConsumer - INFO - Initializing database connections...
2025-07-01 09:53:26 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-07-01 09:53:27 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-07-01 09:53:27 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-07-01 09:53:29 - <PERSON><PERSON><PERSON>anager - INFO - Successfully connected to Redis on DB index: 6!
2025-07-01 09:53:31 - PostgresManager - INFO - PostgreSQL connection pool created
2025-07-01 09:53:31 - PostgresManager - INFO - PostgreSQL connection pool is available
2025-07-01 09:53:33 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-07-01 09:53:34 - RedisEventListener - INFO - Creating new RedisEventListener instance
2025-07-01 09:53:34 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-07-01 09:53:36 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-07-01 09:53:36 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-07-01 09:53:38 - RedisManager - INFO - Successfully connected to Redis on DB index: 6!
2025-07-01 09:53:38 - RedisEventListener - INFO - Starting Redis event listener thread
2025-07-01 09:53:38 - RedisEventListener - INFO - Redis event listener started
2025-07-01 09:53:38 - KafkaWorkflowConsumer - INFO - Database connections initialized successfully
2025-07-01 09:53:38 - StateManager - DEBUG - Using provided database connections
2025-07-01 09:53:38 - RedisEventListener - INFO - Workflow state manager reference updated
2025-07-01 09:53:38 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-07-01 09:53:38 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-07-01 09:53:38 - RedisEventListener - INFO - Configured Redis results DB for keyspace notifications including expirations
2025-07-01 09:53:38 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-07-01 09:53:38 - StateManager - INFO - WorkflowStateManager initialized
2025-07-01 09:53:38 - KafkaWorkflowConsumer - INFO - WorkflowStateManager reference set in RedisEventListener for archival operations
2025-07-01 09:53:38 - KafkaWorkflowConsumer - INFO - KafkaWorkflowConsumer initialized successfully
2025-07-01 09:53:39 - RedisEventListener - INFO - Configured Redis state DB for keyspace notifications including expirations
2025-07-01 09:53:39 - RedisEventListener - INFO - Created dedicated Redis clients for pubsub with decode_responses=False
2025-07-01 09:53:42 - RedisEventListener - INFO - Redis results client decode_responses: True
2025-07-01 09:53:42 - RedisEventListener - INFO - Redis state client decode_responses: True
2025-07-01 09:53:42 - RedisEventListener - INFO - Subscribed to keyspace events for Redis DB 5 and 6
2025-07-01 09:53:47 - MCPToolExecutor - INFO - Starting KafkaToolExecutor internal consumer...
2025-07-01 09:53:53 - MCPToolExecutor - INFO - Internal consumer started. Listening for results on: 'mcp_results', Group: 'tool-executor-consumer'
2025-07-01 09:53:53 - MCPToolExecutor - INFO - Background result consumer loop started.
2025-07-01 09:53:53 - NodeExecutor - INFO - Starting NodeExecutor internal consumer...
2025-07-01 09:54:00 - NodeExecutor - INFO - Internal consumer started. Listening for results on: 'node_results', Group: 'node-executor-consumer'
2025-07-01 09:54:00 - NodeExecutor - INFO - Background result consumer loop started.
2025-07-01 09:54:00 - AgentExecutor - INFO - Starting AgentExecutor internal consumer...
2025-07-01 09:54:06 - AgentExecutor - INFO - Internal consumer started. Listening for results on: 'agent_chat_responses', Group: 'agent-executor-consumer'
2025-07-01 09:54:06 - AgentExecutor - INFO - Background result consumer loop started.
2025-07-01 09:54:34 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-07-01 09:54:35 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-01 09:54:35 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-01 09:54:35 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
