2025-06-30 14:29:50 - Main - INFO - Starting Server
2025-06-30 14:29:50 - Main - INFO - Connection at: **************:9092
2025-06-30 14:29:50 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-06-30 14:29:50 - NodeExecutor - INFO - NodeExecutor initialized.
2025-06-30 14:29:50 - AgentExecutor - INFO - AgentExecutor initialized.
2025-06-30 14:29:50 - KafkaWorkflowConsumer - INFO - Initializing database connections...
2025-06-30 14:29:50 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-30 14:29:52 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-30 14:29:52 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-30 14:29:53 - Red<PERSON><PERSON>anager - INFO - Successfully connected to Redis on DB index: 6!
2025-06-30 14:29:56 - PostgresManager - INFO - PostgreSQL connection pool created
2025-06-30 14:29:56 - PostgresManager - INFO - PostgreSQL connection pool is available
2025-06-30 14:29:58 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-30 14:29:59 - RedisEventListener - INFO - Creating new RedisEventListener instance
2025-06-30 14:29:59 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-30 14:30:00 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-30 14:30:00 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-30 14:30:02 - RedisManager - INFO - Successfully connected to Redis on DB index: 6!
2025-06-30 14:30:02 - RedisEventListener - INFO - Starting Redis event listener thread
2025-06-30 14:30:02 - RedisEventListener - INFO - Redis event listener started
2025-06-30 14:30:02 - KafkaWorkflowConsumer - INFO - Database connections initialized successfully
2025-06-30 14:30:02 - StateManager - DEBUG - Using provided database connections
2025-06-30 14:30:02 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-30 14:30:02 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-30 14:30:02 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-30 14:30:03 - RedisEventListener - INFO - Configured Redis results DB for keyspace notifications including expirations
2025-06-30 14:30:03 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-30 14:30:03 - StateManager - INFO - WorkflowStateManager initialized
2025-06-30 14:30:03 - KafkaWorkflowConsumer - INFO - WorkflowStateManager reference set in RedisEventListener for archival operations
2025-06-30 14:30:03 - KafkaWorkflowConsumer - INFO - KafkaWorkflowConsumer initialized successfully
2025-06-30 14:30:03 - RedisEventListener - INFO - Configured Redis state DB for keyspace notifications including expirations
2025-06-30 14:30:03 - RedisEventListener - INFO - Created dedicated Redis clients for pubsub with decode_responses=False
2025-06-30 14:30:06 - RedisEventListener - INFO - Redis results client decode_responses: True
2025-06-30 14:30:06 - RedisEventListener - INFO - Redis state client decode_responses: True
2025-06-30 14:30:06 - RedisEventListener - INFO - Subscribed to keyspace events for Redis DB 5 and 6
2025-06-30 14:30:19 - MCPToolExecutor - INFO - Starting KafkaToolExecutor internal consumer...
2025-06-30 14:30:26 - MCPToolExecutor - INFO - Internal consumer started. Listening for results on: 'mcp_results', Group: 'tool-executor-consumer'
2025-06-30 14:30:26 - MCPToolExecutor - INFO - Background result consumer loop started.
2025-06-30 14:30:26 - NodeExecutor - INFO - Starting NodeExecutor internal consumer...
2025-06-30 14:30:32 - NodeExecutor - INFO - Internal consumer started. Listening for results on: 'node_results', Group: 'node-executor-consumer'
2025-06-30 14:30:32 - NodeExecutor - INFO - Background result consumer loop started.
2025-06-30 14:30:32 - AgentExecutor - INFO - Starting AgentExecutor internal consumer...
2025-06-30 14:30:38 - AgentExecutor - INFO - Internal consumer started. Listening for results on: 'agent_chat_responses', Group: 'agent-executor-consumer'
2025-06-30 14:30:38 - AgentExecutor - INFO - Background result consumer loop started.
2025-06-30 14:30:38 - KafkaWorkflowConsumer - INFO - Received: topic=workflow-requests, partition=0, offset=1065
2025-06-30 14:30:38 - KafkaWorkflowConsumer - DEBUG - message json: {'task_id': 1751274009, 'task_type': 'workflow', 'data': {'workflow_id': '0c19c070-905e-46ef-9f57-62eb427bf396', 'payload': {'user_dependent_fields': ['query'], 'user_payload_template': {'query': {'value': 'nature 101', 'transition_id': 'AgenticAI-175**********'}}}, 'approval': True, 'user_id': '91a237fd-0225-4e02-9e9f-805eff073b07'}, 'approval': True}
2025-06-30 14:30:38 - KafkaWorkflowConsumer - INFO - Extracted user_id: 91a237fd-0225-4e02-9e9f-805eff073b07 for workflow: 0c19c070-905e-46ef-9f57-62eb427bf396
2025-06-30 14:30:38 - WorkflowService - DEBUG - Sending GET request to: https://app-dev.rapidinnovation.dev/api/v1/workflows/orchestration/0c19c070-905e-46ef-9f57-62eb427bf396
2025-06-30 14:30:40 - WorkflowService - DEBUG - Received response with status code: 200
2025-06-30 14:30:40 - WorkflowService - DEBUG - Parsed JSON response: {
  "success": true,
  "message": "Workflow Ruh_Video_Generation retrieved successfully",
  "workflow": {
    "id": "0c19c070-905e-46ef-9f57-62eb427bf396",
    "name": "Ruh_Video_Generation",
    "description": "Ruh_Video_Generation",
    "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/e2c5a041-2b84-4b33-be90-3d1e07e44353.json",
    "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/451d0c28-d066-4833-afad-080c37e7563e.json",
    "start_nodes": [
      {
        "field": "query",
        "type": "string",
        "transition_id": "transition-AgenticAI-175**********"
      }
    ],
    "owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07",
    "user_ids": [
      "91a237fd-0225-4e02-9e9f-805eff073b07"
    ],
    "owner_type": "user",
    "workflow_template_id": "8d4b3f88-71ea-48dd-b8a2-dc101d22bf8a",
    "template_owner_id": "180c7469-1db0-4707-bc17-eeaa5e7ff64d",
    "is_imported": true,
    "version": "1.0.0",
    "visibility": "private",
    "category": null,
    "tags": null,
    "status": "active",
    "is_changes_marketplace": false,
    "is_customizable": true,
    "auto_version_on_update": false,
    "created_at": "2025-06-29T08:29:44.634200",
    "updated_at": "2025-06-30T08:40:55.500303",
    "available_nodes": [
      {
        "name": "CombineTextComponent",
        "display_name": "Combine Text",
        "type": "component",
        "transition_id": "transition-CombineTextComponent-*************"
      },
      {
        "name": "MergeDataComponent",
        "display_name": "Merge Data",
        "type": "component",
        "transition_id": "transition-MergeDataComponent-*************"
      },
      {
        "name": "CombineTextComponent",
        "display_name": "Combine Text",
        "type": "component",
        "transition_id": "transition-CombineTextComponent-1750920624318"
      }
    ],
    "is_updated": true
  }
}
2025-06-30 14:30:40 - KafkaWorkflowConsumer - DEBUG - Workflow loaded for 0c19c070-905e-46ef-9f57-62eb427bf396 - server_script_path is optional
2025-06-30 14:30:40 - WorkflowUtils - INFO - WorkflowUtils initialized
2025-06-30 14:30:40 - StateManager - DEBUG - Using global database connections from initializer
2025-06-30 14:30:40 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-30 14:30:40 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-30 14:30:40 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-30 14:30:41 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-30 14:30:41 - StateManager - INFO - WorkflowStateManager initialized
2025-06-30 14:30:41 - WorkflowUtils - INFO - Workflow JSON is valid against the enhanced schema.
2025-06-30 14:30:41 - StateManager - DEBUG - Using provided database connections
2025-06-30 14:30:41 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-30 14:30:41 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-30 14:30:41 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-30 14:30:42 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-30 14:30:42 - StateManager - INFO - WorkflowStateManager initialized
2025-06-30 14:30:42 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-*************: ['transition-AgenticAI-175**********']
2025-06-30 14:30:42 - StateManager - DEBUG - Extracted dependencies for transition transition-LoopNode-*************: ['transition-AgenticAI-*************']
2025-06-30 14:30:42 - StateManager - DEBUG - Extracted dependencies for transition transition-MergeDataComponent-*************: ['transition-LoopNode-*************']
2025-06-30 14:30:42 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-*************: ['transition-AgenticAI-175**********', 'transition-AgenticAI-175**********', 'transition-LoopNode-*************']
2025-06-30 14:30:42 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1750505490787: ['transition-CombineTextComponent-*************']
2025-06-30 14:30:42 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1751001474606: ['transition-AgenticAI-1750505490787']
2025-06-30 14:30:42 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1751001471087: ['transition-AgenticAI-1750505490787']
2025-06-30 14:30:42 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-1750920624318: ['transition-AgenticAI-1750505490787', 'transition-AgenticAI-1751001471087', 'transition-AgenticAI-1751001474606']
2025-06-30 14:30:42 - StateManager - INFO - Built dependency map for 9 transitions
2025-06-30 14:30:42 - StateManager - DEBUG - Transition transition-AgenticAI-************* depends on: ['transition-AgenticAI-175**********']
2025-06-30 14:30:42 - StateManager - DEBUG - Transition transition-LoopNode-************* depends on: ['transition-AgenticAI-*************']
2025-06-30 14:30:42 - StateManager - DEBUG - Transition transition-MergeDataComponent-************* depends on: ['transition-LoopNode-*************']
2025-06-30 14:30:42 - StateManager - DEBUG - Transition transition-CombineTextComponent-************* depends on: ['transition-AgenticAI-175**********', 'transition-AgenticAI-175**********', 'transition-LoopNode-*************']
2025-06-30 14:30:42 - StateManager - DEBUG - Transition transition-AgenticAI-1750505490787 depends on: ['transition-CombineTextComponent-*************']
2025-06-30 14:30:42 - StateManager - DEBUG - Transition transition-AgenticAI-1751001474606 depends on: ['transition-AgenticAI-1750505490787']
2025-06-30 14:30:42 - StateManager - DEBUG - Transition transition-AgenticAI-1751001471087 depends on: ['transition-AgenticAI-1750505490787']
2025-06-30 14:30:42 - StateManager - DEBUG - Transition transition-CombineTextComponent-1750920624318 depends on: ['transition-AgenticAI-1750505490787', 'transition-AgenticAI-1751001471087', 'transition-AgenticAI-1751001474606']
2025-06-30 14:30:42 - MCPToolExecutor - DEBUG - Set correlation ID to: 973e3432-58ac-433a-a97a-97514ea48579
2025-06-30 14:30:42 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 973e3432-58ac-433a-a97a-97514ea48579 in tool_executor
2025-06-30 14:30:42 - MCPToolExecutor - DEBUG - Set user ID to: 91a237fd-0225-4e02-9e9f-805eff073b07
2025-06-30 14:30:42 - EnhancedWorkflowEngine - DEBUG - Set user_id 91a237fd-0225-4e02-9e9f-805eff073b07 in tool_executor
2025-06-30 14:30:42 - NodeExecutor - DEBUG - Set correlation ID to: 973e3432-58ac-433a-a97a-97514ea48579
2025-06-30 14:30:42 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 973e3432-58ac-433a-a97a-97514ea48579 in node_executor
2025-06-30 14:30:42 - AgentExecutor - DEBUG - Set correlation ID to: 973e3432-58ac-433a-a97a-97514ea48579
2025-06-30 14:30:42 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 973e3432-58ac-433a-a97a-97514ea48579 in agent_executor
2025-06-30 14:30:42 - AgentExecutor - DEBUG - Set user ID to: 91a237fd-0225-4e02-9e9f-805eff073b07
2025-06-30 14:30:42 - EnhancedWorkflowEngine - DEBUG - Set user_id 91a237fd-0225-4e02-9e9f-805eff073b07 in agent_executor
2025-06-30 14:30:42 - TransitionHandler - INFO - TransitionHandler initialized
2025-06-30 14:30:42 - EnhancedWorkflowEngine - INFO - EnhancedWorkflowEngine initialized with workflow ID: 973e3432-58ac-433a-a97a-97514ea48579
2025-06-30 14:30:42 - KafkaWorkflowConsumer - INFO - Workflow execution started in background for task-request, corr_id: 973e3432-58ac-433a-a97a-97514ea48579
2025-06-30 14:30:42 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 973e3432-58ac-433a-a97a-97514ea48579, response: {'status': 'Workflow Initialized', 'result': 'Workflow Initialized', 'workflow_status': 'running'}
2025-06-30 14:30:42 - StateManager - INFO - Workflow initialized with initial transition: transition-AgenticAI-175**********
2025-06-30 14:30:42 - StateManager - DEBUG - State: pending={'transition-AgenticAI-175**********'}, waiting=set(), completed=set()
2025-06-30 14:30:42 - EnhancedWorkflowEngine - INFO - Initializing workflow with single initial transition: transition-AgenticAI-175**********
2025-06-30 14:30:42 - StateManager - DEBUG - Workflow active: {'transition-AgenticAI-175**********'}
2025-06-30 14:30:43 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:973e3432-58ac-433a-a97a-97514ea48579'
2025-06-30 14:30:43 - RedisManager - DEBUG - Set key 'workflow_state:973e3432-58ac-433a-a97a-97514ea48579' with TTL of 600 seconds
2025-06-30 14:30:43 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 973e3432-58ac-433a-a97a-97514ea48579. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:30:43 - StateManager - DEBUG - Checking waiting transitions: set()
2025-06-30 14:30:43 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-30 14:30:43 - StateManager - INFO - Cleared 1 pending transitions: {'transition-AgenticAI-175**********'}
2025-06-30 14:30:43 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-30 14:30:43 - StateManager - INFO - Terminated: False
2025-06-30 14:30:43 - StateManager - INFO - Pending transitions (0): []
2025-06-30 14:30:43 - StateManager - INFO - Waiting transitions (0): []
2025-06-30 14:30:43 - StateManager - INFO - Completed transitions (0): []
2025-06-30 14:30:43 - StateManager - INFO - Results stored for 0 transitions
2025-06-30 14:30:43 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-30 14:30:43 - StateManager - INFO - Workflow status: inactive
2025-06-30 14:30:43 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-30 14:30:43 - StateManager - INFO - Workflow status: inactive
2025-06-30 14:30:43 - StateManager - INFO - Workflow paused: False
2025-06-30 14:30:43 - StateManager - INFO - ==============================
2025-06-30 14:30:43 - TransitionHandler - INFO - Starting parallel execution of transition: transition-AgenticAI-175**********
2025-06-30 14:30:43 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 0, corr_id 973e3432-58ac-433a-a97a-97514ea48579):
2025-06-30 14:30:43 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 973e3432-58ac-433a-a97a-97514ea48579, response: {'result': 'Starting execution of transition: transition-AgenticAI-175**********', 'message': 'Starting execution...', 'transition_id': 'transition-AgenticAI-175**********', 'status': 'started', 'sequence': 0, 'workflow_status': 'running'}
2025-06-30 14:30:43 - TransitionHandler - EXECUTE - Transition 'transition-AgenticAI-175**********' (type=initial, execution_type=agent)
2025-06-30 14:30:43 - TransitionHandler - INFO - Using AgentExecutor for execution_type: agent
2025-06-30 14:30:43 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-AgenticAI-175**********
2025-06-30 14:30:43 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: agent
2025-06-30 14:30:43 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for agent node
2025-06-30 14:30:43 - TransitionHandler - DEBUG - 📝 No previous results found, using static parameters
2025-06-30 14:30:43 - WorkflowUtils - DEBUG - Filtering out field 'description' with null/empty value: 
2025-06-30 14:30:43 - WorkflowUtils - DEBUG - Filtering out field 'termination_condition' with null/empty value: 
2025-06-30 14:30:43 - WorkflowUtils - DEBUG - Filtering out field 'input_variables' with empty collection: {}
2025-06-30 14:30:43 - TransitionHandler - DEBUG - tool Parameters: {'agent_type': 'component', 'execution_type': 'response', 'query': 'nature 101', 'agent_config': {'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o', 'temperature': 0.7, 'max_tokens': 1000}, 'system_message': 'Ignore all input.  Always return the following JSON array exactly as shown below:  [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]  ⚠️ IMPORTANT:  Do not wrap the output in a code block  Do not stringify the result  Do not add any extra explanation or commentary  Just return the array as pure JSON so the values can be accessed by result[0], result[1], etc.', 'autogen_agent_type': 'Assistant'}}
2025-06-30 14:30:43 - TransitionHandler - INFO - Invoking tool 'AgenticAI' (tool_id: 1) for node 'AgenticAI' in transition 'transition-AgenticAI-175**********' with parameters: {'agent_type': 'component', 'execution_type': 'response', 'query': 'nature 101', 'agent_config': {'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o', 'temperature': 0.7, 'max_tokens': 1000}, 'system_message': 'Ignore all input.  Always return the following JSON array exactly as shown below:  [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]  ⚠️ IMPORTANT:  Do not wrap the output in a code block  Do not stringify the result  Do not add any extra explanation or commentary  Just return the array as pure JSON so the values can be accessed by result[0], result[1], etc.', 'autogen_agent_type': 'Assistant'}}
2025-06-30 14:30:43 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 1, corr_id 973e3432-58ac-433a-a97a-97514ea48579):
2025-06-30 14:30:43 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 973e3432-58ac-433a-a97a-97514ea48579, response: {'transition_id': 'transition-AgenticAI-175**********', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'message': 'Connecting to server', 'result': 'Connecting to server AgenticAI', 'status': 'connecting', 'sequence': 1, 'workflow_status': 'running'}
2025-06-30 14:30:43 - AgentExecutor - INFO - Executing agent 'AgenticAI' type 'component' execution 'response' via Kafka (request_id: 9ccc6bfa-03f6-4f45-b868-7233d86c8712) with correlation_id: 973e3432-58ac-433a-a97a-97514ea48579, user_id: 91a237fd-0225-4e02-9e9f-805eff073b07 using provided producer.
2025-06-30 14:30:43 - AgentExecutor - INFO - Building component agent request for execution_type: response
2025-06-30 14:30:43 - AgentExecutor - DEBUG - Added correlation_id 973e3432-58ac-433a-a97a-97514ea48579 to payload
2025-06-30 14:30:43 - AgentExecutor - DEBUG - Sending request to topic 'agent_message_requests': {'request_id': '9ccc6bfa-03f6-4f45-b868-7233d86c8712', 'user_id': '91a237fd-0225-4e02-9e9f-805eff073b07', 'correlation_id': '973e3432-58ac-433a-a97a-97514ea48579', 'agent_type': 'component', 'execution_type': 'response', 'query': 'nature 101', 'variables': {}, 'agent_config': {'id': 'd5885674-1c9c-467d-aa37-b793526502c8', 'name': 'AI Agent', 'description': 'Basic AI Agent', 'system_message': 'Ignore all input.  Always return the following JSON array exactly as shown below:  [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]  ⚠️ IMPORTANT:  Do not wrap the output in a code block  Do not stringify the result  Do not add any extra explanation or commentary  Just return the array as pure JSON so the values can be accessed by result[0], result[1], etc.', 'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o', 'temperature': 0.7, 'max_tokens': 1000}, 'mcps': []}}
2025-06-30 14:30:43 - AgentExecutor - DEBUG - Request 9ccc6bfa-03f6-4f45-b868-7233d86c8712 sent successfully using provided producer.
2025-06-30 14:30:43 - AgentExecutor - DEBUG - Waiting for single response result for request 9ccc6bfa-03f6-4f45-b868-7233d86c8712...
2025-06-30 14:30:43 - KafkaWorkflowConsumer - INFO - Committed offset after starting engine for task-request: 1065, corr_id: 973e3432-58ac-433a-a97a-97514ea48579
2025-06-30 14:30:50 - AgentExecutor - DEBUG - Result consumer received message: Offset=24305
2025-06-30 14:30:50 - AgentExecutor - DEBUG - Received valid result for request_id 9ccc6bfa-03f6-4f45-b868-7233d86c8712
2025-06-30 14:30:50 - AgentExecutor - INFO - Single response received for request 9ccc6bfa-03f6-4f45-b868-7233d86c8712.
2025-06-30 14:30:50 - TransitionHandler - INFO - Execution result from agent executor: "[ { \"part\": \"(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)\" }, { \"part\": \"Narrator: \"Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?\"\" }, { \"part\": \"(Close-ups of the iPhone\u2019s minimalist and advanced design.)\\n\\nNarrator: \"Check this out \u2013 the latest iPhone redefines what a smartphone can do! Isn't that wild?\"\" }, { \"part\": \"(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: \"Start your day... like, super smoothly. The new chip launches apps faster than ever\u2014whether you\u2019re keeping up with emails or sharing those killer moments!\"\" }, { \"part\": \"(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: \"Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow.\"\" }, { \"part\": \"(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: \"Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?\"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)\" } ]"
2025-06-30 14:30:50 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 2, corr_id 973e3432-58ac-433a-a97a-97514ea48579):
2025-06-30 14:30:50 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 973e3432-58ac-433a-a97a-97514ea48579, response: {'transition_id': 'transition-AgenticAI-175**********', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'message': 'Transition Result received.', 'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'status': 'completed', 'sequence': 2, 'workflow_status': 'running', 'approval_required': False}
2025-06-30 14:30:50 - StateManager - DEBUG - Stored result for transition transition-AgenticAI-175********** in memory: {'AgenticAI': {'transition_id': 'transition-AgenticAI-175**********', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'result': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}, 'status': 'completed', 'timestamp': 1751274050.2503839}}
2025-06-30 14:30:51 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-AgenticAI-175**********'
2025-06-30 14:30:51 - RedisManager - DEBUG - Set key 'result:transition-AgenticAI-175**********' with TTL of 300 seconds
2025-06-30 14:30:51 - StateManager - DEBUG - Stored result for transition transition-AgenticAI-175********** in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:30:51 - StateManager - INFO - Marked transition transition-AgenticAI-175********** as completed (was_pending=False, was_waiting=False)
2025-06-30 14:30:51 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-AgenticAI-175**********'}
2025-06-30 14:30:51 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-AgenticAI-175**********
2025-06-30 14:30:51 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-06-30 14:30:51 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-30 14:30:51 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-06-30 14:30:51 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-AgenticAI-175**********:
2025-06-30 14:30:51 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-30 14:30:51 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-30 14:30:51 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-AgenticAI-175**********, returning empty list
2025-06-30 14:30:51 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-AgenticAI-175**********
2025-06-30 14:30:51 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 3
2025-06-30 14:30:51 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: ['transition-AgenticAI-*************', 'transition-CombineTextComponent-*************', 'transition-CombineTextComponent-*************']
2025-06-30 14:30:51 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-06-30 14:30:51 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-06-30 14:30:51 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-AgenticAI-*************
2025-06-30 14:30:51 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-CombineTextComponent-*************
2025-06-30 14:30:51 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-CombineTextComponent-*************
2025-06-30 14:30:51 - TransitionHandler - DEBUG - 🔗 Final next_transitions: ['transition-AgenticAI-*************', 'transition-CombineTextComponent-*************']
2025-06-30 14:30:51 - TransitionHandler - INFO - Completed transition transition-AgenticAI-175********** in 7.68 seconds
2025-06-30 14:30:51 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 3, corr_id 973e3432-58ac-433a-a97a-97514ea48579):
2025-06-30 14:30:51 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 973e3432-58ac-433a-a97a-97514ea48579, response: {'result': 'Completed transition in 7.68 seconds', 'message': 'Transition completed in 7.68 seconds', 'transition_id': 'transition-AgenticAI-175**********', 'status': 'time_logged', 'sequence': 3, 'workflow_status': 'running'}
2025-06-30 14:30:51 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-AgenticAI-175**********: ['transition-AgenticAI-*************', 'transition-CombineTextComponent-*************']
2025-06-30 14:30:51 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 2
2025-06-30 14:30:51 - EnhancedWorkflowEngine - DEBUG - Results: [['transition-AgenticAI-*************', 'transition-CombineTextComponent-*************']]
2025-06-30 14:30:51 - EnhancedWorkflowEngine - DEBUG - 🔄 Orchestration engine received result for transition-AgenticAI-175**********: ['transition-AgenticAI-*************', 'transition-CombineTextComponent-*************']
2025-06-30 14:30:51 - EnhancedWorkflowEngine - DEBUG - 🔄 Result type: <class 'list'>, is_list: True
2025-06-30 14:30:51 - EnhancedWorkflowEngine - INFO - Transition transition-AgenticAI-175********** completed successfully: 2 next transitions
2025-06-30 14:30:51 - EnhancedWorkflowEngine - DEBUG - 🔄 Added to next_transitions_to_execute: ['transition-AgenticAI-*************', 'transition-CombineTextComponent-*************']
2025-06-30 14:30:51 - TransitionHandler - INFO - Resolved next transitions (direct transition IDs): ['transition-AgenticAI-*************', 'transition-CombineTextComponent-*************']
2025-06-30 14:30:51 - EnhancedWorkflowEngine - INFO - Adding transition transition-AgenticAI-************* to pending (all dependencies met)
2025-06-30 14:30:51 - EnhancedWorkflowEngine - INFO - Adding transition transition-CombineTextComponent-************* to waiting (dependencies not yet met)
2025-06-30 14:30:51 - StateManager - DEBUG - Workflow active: {'transition-AgenticAI-*************'}
2025-06-30 14:30:51 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:973e3432-58ac-433a-a97a-97514ea48579'
2025-06-30 14:30:52 - RedisManager - DEBUG - Set key 'workflow_state:973e3432-58ac-433a-a97a-97514ea48579' with TTL of 600 seconds
2025-06-30 14:30:52 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 973e3432-58ac-433a-a97a-97514ea48579. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:30:52 - StateManager - DEBUG - Checking waiting transitions: {'transition-CombineTextComponent-*************'}
2025-06-30 14:30:52 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-30 14:30:52 - StateManager - INFO - Cleared 1 pending transitions: {'transition-AgenticAI-*************'}
2025-06-30 14:30:52 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-30 14:30:52 - StateManager - INFO - Terminated: False
2025-06-30 14:30:52 - StateManager - INFO - Pending transitions (0): []
2025-06-30 14:30:52 - StateManager - INFO - Waiting transitions (1): ['transition-CombineTextComponent-*************']
2025-06-30 14:30:52 - StateManager - INFO - Completed transitions (1): ['transition-AgenticAI-175**********']
2025-06-30 14:30:52 - StateManager - INFO - Results stored for 1 transitions
2025-06-30 14:30:52 - StateManager - DEBUG - Workflow active: {'transition-CombineTextComponent-*************'}
2025-06-30 14:30:52 - StateManager - INFO - Workflow status: active
2025-06-30 14:30:52 - StateManager - DEBUG - Workflow active: {'transition-CombineTextComponent-*************'}
2025-06-30 14:30:52 - StateManager - INFO - Workflow status: active
2025-06-30 14:30:52 - StateManager - INFO - Workflow paused: False
2025-06-30 14:30:52 - StateManager - INFO - ==============================
2025-06-30 14:30:52 - TransitionHandler - INFO - Starting parallel execution of transition: transition-AgenticAI-*************
2025-06-30 14:30:52 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 4, corr_id 973e3432-58ac-433a-a97a-97514ea48579):
2025-06-30 14:30:52 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 973e3432-58ac-433a-a97a-97514ea48579, response: {'result': 'Starting execution of transition: transition-AgenticAI-*************', 'message': 'Starting execution...', 'transition_id': 'transition-AgenticAI-*************', 'status': 'started', 'sequence': 4, 'workflow_status': 'running'}
2025-06-30 14:30:52 - TransitionHandler - EXECUTE - Transition 'transition-AgenticAI-*************' (type=standard, execution_type=agent)
2025-06-30 14:30:52 - TransitionHandler - INFO - Using AgentExecutor for execution_type: agent
2025-06-30 14:30:52 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-AgenticAI-*************
2025-06-30 14:30:52 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: agent
2025-06-30 14:30:52 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for agent node
2025-06-30 14:30:52 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-175********** from Redis
2025-06-30 14:30:52 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-175**********, extracting data
2025-06-30 14:30:52 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-175**********
2025-06-30 14:30:52 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-175**********
2025-06-30 14:30:52 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-175********** (total: 1)
2025-06-30 14:30:52 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}
2025-06-30 14:30:52 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-30 14:30:52 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-06-30 14:30:52 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] (type: <class 'str'>)
2025-06-30 14:30:52 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-30 14:30:52 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-06-30 14:30:52 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-30 14:30:52 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-30 14:30:52 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}
2025-06-30 14:30:52 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-30 14:30:52 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-06-30 14:30:52 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] (type: <class 'str'>)
2025-06-30 14:30:52 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-30 14:30:52 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-06-30 14:30:52 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → query via path 'result': [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-06-30 14:30:52 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-30 14:30:52 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-30 14:30:52 - WorkflowUtils - DEBUG - Filtering out field 'termination_condition' with null/empty value: 
2025-06-30 14:30:52 - WorkflowUtils - DEBUG - Filtering out field 'input_variables' with empty collection: {}
2025-06-30 14:30:52 - TransitionHandler - DEBUG - 📌 Added static parameter: agent_type = component
2025-06-30 14:30:52 - TransitionHandler - DEBUG - 📌 Added static parameter: execution_type = response
2025-06-30 14:30:52 - TransitionHandler - DEBUG - 📌 Added static parameter: agent_config = {'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'description': 'This agent acts as a loop executor initializer, always returning the starting range value 0 while ignoring all input.', 'system_message': 'You are a minimal-response AI agent.  Ignore all input, regardless of content or format. Your only task is to always return the single-digit number 0.  Output Rules: Return only: 0  Do NOT return any text, explanation, quotes, or formatting.  Do NOT wrap the output in JSON, markdown, or code blocks.  Do NOT acknowledge or reference the input in any way.', 'autogen_agent_type': 'Assistant'}
2025-06-30 14:30:52 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'query': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'agent_type': 'component', 'execution_type': 'response', 'agent_config': {'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'description': 'This agent acts as a loop executor initializer, always returning the starting range value 0 while ignoring all input.', 'system_message': 'You are a minimal-response AI agent.  Ignore all input, regardless of content or format. Your only task is to always return the single-digit number 0.  Output Rules: Return only: 0  Do NOT return any text, explanation, quotes, or formatting.  Do NOT wrap the output in JSON, markdown, or code blocks.  Do NOT acknowledge or reference the input in any way.', 'autogen_agent_type': 'Assistant'}}
2025-06-30 14:30:52 - TransitionHandler - DEBUG - tool Parameters: {'query': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'agent_type': 'component', 'execution_type': 'response', 'agent_config': {'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'description': 'This agent acts as a loop executor initializer, always returning the starting range value 0 while ignoring all input.', 'system_message': 'You are a minimal-response AI agent.  Ignore all input, regardless of content or format. Your only task is to always return the single-digit number 0.  Output Rules: Return only: 0  Do NOT return any text, explanation, quotes, or formatting.  Do NOT wrap the output in JSON, markdown, or code blocks.  Do NOT acknowledge or reference the input in any way.', 'autogen_agent_type': 'Assistant'}}
2025-06-30 14:30:52 - TransitionHandler - INFO - Invoking tool 'AgenticAI' (tool_id: 1) for node 'AgenticAI' in transition 'transition-AgenticAI-*************' with parameters: {'query': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'agent_type': 'component', 'execution_type': 'response', 'agent_config': {'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'description': 'This agent acts as a loop executor initializer, always returning the starting range value 0 while ignoring all input.', 'system_message': 'You are a minimal-response AI agent.  Ignore all input, regardless of content or format. Your only task is to always return the single-digit number 0.  Output Rules: Return only: 0  Do NOT return any text, explanation, quotes, or formatting.  Do NOT wrap the output in JSON, markdown, or code blocks.  Do NOT acknowledge or reference the input in any way.', 'autogen_agent_type': 'Assistant'}}
2025-06-30 14:30:52 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 5, corr_id 973e3432-58ac-433a-a97a-97514ea48579):
2025-06-30 14:30:52 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 973e3432-58ac-433a-a97a-97514ea48579, response: {'transition_id': 'transition-AgenticAI-*************', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'message': 'Connecting to server', 'result': 'Connecting to server AgenticAI', 'status': 'connecting', 'sequence': 5, 'workflow_status': 'running'}
2025-06-30 14:30:52 - AgentExecutor - INFO - Executing agent 'AgenticAI' type 'component' execution 'response' via Kafka (request_id: 56cb889a-8581-492f-b0b8-046fd50cda03) with correlation_id: 973e3432-58ac-433a-a97a-97514ea48579, user_id: 91a237fd-0225-4e02-9e9f-805eff073b07 using provided producer.
2025-06-30 14:30:52 - AgentExecutor - INFO - Building component agent request for execution_type: response
2025-06-30 14:30:52 - AgentExecutor - DEBUG - Added correlation_id 973e3432-58ac-433a-a97a-97514ea48579 to payload
2025-06-30 14:30:52 - AgentExecutor - DEBUG - Sending request to topic 'agent_message_requests': {'request_id': '56cb889a-8581-492f-b0b8-046fd50cda03', 'user_id': '91a237fd-0225-4e02-9e9f-805eff073b07', 'correlation_id': '973e3432-58ac-433a-a97a-97514ea48579', 'agent_type': 'component', 'execution_type': 'response', 'query': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'variables': {}, 'agent_config': {'id': '0a0aadeb-8234-4fca-8018-d7e36a6efed4', 'name': 'AI Agent', 'description': 'This agent acts as a loop executor initializer, always returning the starting range value 0 while ignoring all input.', 'system_message': 'You are a minimal-response AI agent.  Ignore all input, regardless of content or format. Your only task is to always return the single-digit number 0.  Output Rules: Return only: 0  Do NOT return any text, explanation, quotes, or formatting.  Do NOT wrap the output in JSON, markdown, or code blocks.  Do NOT acknowledge or reference the input in any way.', 'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'mcps': []}}
2025-06-30 14:30:52 - AgentExecutor - DEBUG - Request 56cb889a-8581-492f-b0b8-046fd50cda03 sent successfully using provided producer.
2025-06-30 14:30:52 - AgentExecutor - DEBUG - Waiting for single response result for request 56cb889a-8581-492f-b0b8-046fd50cda03...
2025-06-30 14:30:54 - AgentExecutor - DEBUG - Result consumer received message: Offset=24306
2025-06-30 14:30:54 - AgentExecutor - DEBUG - Received valid result for request_id 56cb889a-8581-492f-b0b8-046fd50cda03
2025-06-30 14:30:54 - AgentExecutor - INFO - Single response received for request 56cb889a-8581-492f-b0b8-046fd50cda03.
2025-06-30 14:30:54 - TransitionHandler - INFO - Execution result from agent executor: "0"
2025-06-30 14:30:54 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 6, corr_id 973e3432-58ac-433a-a97a-97514ea48579):
2025-06-30 14:30:54 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 973e3432-58ac-433a-a97a-97514ea48579, response: {'transition_id': 'transition-AgenticAI-*************', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'message': 'Transition Result received.', 'result': '0', 'status': 'completed', 'sequence': 6, 'workflow_status': 'running', 'approval_required': False}
2025-06-30 14:30:54 - StateManager - DEBUG - Stored result for transition transition-AgenticAI-************* in memory: {'AgenticAI': {'transition_id': 'transition-AgenticAI-*************', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'result': {'result': '0'}, 'status': 'completed', 'timestamp': 1751274054.0113}}
2025-06-30 14:30:54 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-AgenticAI-*************'
2025-06-30 14:30:54 - RedisManager - DEBUG - Set key 'result:transition-AgenticAI-*************' with TTL of 300 seconds
2025-06-30 14:30:54 - StateManager - DEBUG - Stored result for transition transition-AgenticAI-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:30:54 - StateManager - INFO - Marked transition transition-AgenticAI-************* as completed (was_pending=False, was_waiting=False)
2025-06-30 14:30:54 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'transition-AgenticAI-175**********', 'transition-AgenticAI-*************'}
2025-06-30 14:30:54 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-AgenticAI-*************
2025-06-30 14:30:54 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-06-30 14:30:54 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-30 14:30:54 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-06-30 14:30:54 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-AgenticAI-*************:
2025-06-30 14:30:54 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-30 14:30:54 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-30 14:30:54 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-AgenticAI-*************, returning empty list
2025-06-30 14:30:54 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-AgenticAI-*************
2025-06-30 14:30:54 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 1
2025-06-30 14:30:54 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: ['transition-LoopNode-*************']
2025-06-30 14:30:54 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-06-30 14:30:54 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-06-30 14:30:54 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-LoopNode-*************
2025-06-30 14:30:54 - TransitionHandler - DEBUG - 🔗 Final next_transitions: ['transition-LoopNode-*************']
2025-06-30 14:30:54 - TransitionHandler - INFO - Completed transition transition-AgenticAI-************* in 2.77 seconds
2025-06-30 14:30:54 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 7, corr_id 973e3432-58ac-433a-a97a-97514ea48579):
2025-06-30 14:30:54 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 973e3432-58ac-433a-a97a-97514ea48579, response: {'result': 'Completed transition in 2.77 seconds', 'message': 'Transition completed in 2.77 seconds', 'transition_id': 'transition-AgenticAI-*************', 'status': 'time_logged', 'sequence': 7, 'workflow_status': 'running'}
2025-06-30 14:30:54 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-AgenticAI-*************: ['transition-LoopNode-*************']
2025-06-30 14:30:54 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 1
2025-06-30 14:30:54 - EnhancedWorkflowEngine - DEBUG - Results: [['transition-LoopNode-*************']]
2025-06-30 14:30:54 - EnhancedWorkflowEngine - DEBUG - 🔄 Orchestration engine received result for transition-AgenticAI-*************: ['transition-LoopNode-*************']
2025-06-30 14:30:54 - EnhancedWorkflowEngine - DEBUG - 🔄 Result type: <class 'list'>, is_list: True
2025-06-30 14:30:54 - EnhancedWorkflowEngine - INFO - Transition transition-AgenticAI-************* completed successfully: 1 next transitions
2025-06-30 14:30:54 - EnhancedWorkflowEngine - DEBUG - 🔄 Added to next_transitions_to_execute: ['transition-LoopNode-*************']
2025-06-30 14:30:54 - TransitionHandler - INFO - Resolved next transitions (direct transition IDs): ['transition-LoopNode-*************']
2025-06-30 14:30:54 - EnhancedWorkflowEngine - INFO - Adding transition transition-LoopNode-************* to pending (all dependencies met)
2025-06-30 14:30:54 - StateManager - DEBUG - Workflow active: {'transition-LoopNode-*************'}
2025-06-30 14:30:55 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:973e3432-58ac-433a-a97a-97514ea48579'
2025-06-30 14:30:55 - RedisManager - DEBUG - Set key 'workflow_state:973e3432-58ac-433a-a97a-97514ea48579' with TTL of 600 seconds
2025-06-30 14:30:55 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 973e3432-58ac-433a-a97a-97514ea48579. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:30:55 - StateManager - DEBUG - Checking waiting transitions: {'transition-CombineTextComponent-*************'}
2025-06-30 14:30:55 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-30 14:30:55 - StateManager - INFO - Cleared 1 pending transitions: {'transition-LoopNode-*************'}
2025-06-30 14:30:55 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-30 14:30:55 - StateManager - INFO - Terminated: False
2025-06-30 14:30:55 - StateManager - INFO - Pending transitions (0): []
2025-06-30 14:30:55 - StateManager - INFO - Waiting transitions (1): ['transition-CombineTextComponent-*************']
2025-06-30 14:30:55 - StateManager - INFO - Completed transitions (2): ['transition-AgenticAI-175**********', 'transition-AgenticAI-*************']
2025-06-30 14:30:55 - StateManager - INFO - Results stored for 2 transitions
2025-06-30 14:30:55 - StateManager - DEBUG - Workflow active: {'transition-CombineTextComponent-*************'}
2025-06-30 14:30:55 - StateManager - INFO - Workflow status: active
2025-06-30 14:30:55 - StateManager - DEBUG - Workflow active: {'transition-CombineTextComponent-*************'}
2025-06-30 14:30:55 - StateManager - INFO - Workflow status: active
2025-06-30 14:30:55 - StateManager - INFO - Workflow paused: False
2025-06-30 14:30:55 - StateManager - INFO - ==============================
2025-06-30 14:30:55 - TransitionHandler - INFO - Starting parallel execution of transition: transition-LoopNode-*************
2025-06-30 14:30:55 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 8, corr_id 973e3432-58ac-433a-a97a-97514ea48579):
2025-06-30 14:30:55 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 973e3432-58ac-433a-a97a-97514ea48579, response: {'result': 'Starting execution of transition: transition-LoopNode-*************', 'message': 'Starting execution...', 'transition_id': 'transition-LoopNode-*************', 'status': 'started', 'sequence': 8, 'workflow_status': 'running'}
2025-06-30 14:30:55 - TransitionHandler - EXECUTE - Transition 'transition-LoopNode-*************' (type=standard, execution_type=loop)
2025-06-30 14:30:55 - LoopExecutor - DEBUG - 🔗 Orchestration engine set for loop executor integration
2025-06-30 14:30:55 - TransitionHandler - DEBUG - 🔗 Set orchestration engine for loop executor
2025-06-30 14:30:55 - TransitionHandler - INFO - Using KafkaToolExecutor for execution_type: loop
2025-06-30 14:30:55 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-LoopNode-*************
2025-06-30 14:30:55 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: loop
2025-06-30 14:30:55 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for loop node
2025-06-30 14:30:56 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-06-30 14:30:56 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-06-30 14:30:56 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-06-30 14:30:56 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-06-30 14:30:56 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 1)
2025-06-30 14:30:56 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '0'}
2025-06-30 14:30:56 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-30 14:30:56 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: 0
2025-06-30 14:30:56 - WorkflowUtils - DEBUG - Found result.result: 0 (type: <class 'str'>)
2025-06-30 14:30:56 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-30 14:30:56 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-06-30 14:30:56 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-30 14:30:56 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-30 14:30:56 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '0'}
2025-06-30 14:30:56 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-30 14:30:56 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: 0
2025-06-30 14:30:56 - WorkflowUtils - DEBUG - Found result.result: 0 (type: <class 'str'>)
2025-06-30 14:30:56 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-30 14:30:56 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-06-30 14:30:56 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → start via path 'result': 0
2025-06-30 14:30:56 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-30 14:30:56 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-30 14:30:56 - WorkflowUtils - DEBUG - Filtering out field 'iteration_list' with empty collection: []
2025-06-30 14:30:56 - WorkflowUtils - DEBUG - Filtering out field 'start' with null/empty value: None
2025-06-30 14:30:56 - WorkflowUtils - INFO - 🧹 Parameter filtering: 14 → 12 fields (2 null/empty fields removed)
2025-06-30 14:30:56 - TransitionHandler - DEBUG - 📌 Added static parameter: source_type = number_range
2025-06-30 14:30:56 - TransitionHandler - DEBUG - 📌 Added static parameter: batch_size = 1
2025-06-30 14:30:56 - TransitionHandler - DEBUG - 📌 Added static parameter: end = 6
2025-06-30 14:30:56 - TransitionHandler - DEBUG - 📌 Added static parameter: step = 1
2025-06-30 14:30:56 - TransitionHandler - DEBUG - 📌 Added static parameter: parallel_execution = False
2025-06-30 14:30:56 - TransitionHandler - DEBUG - 📌 Added static parameter: max_concurrent = 3
2025-06-30 14:30:56 - TransitionHandler - DEBUG - 📌 Added static parameter: preserve_order = True
2025-06-30 14:30:56 - TransitionHandler - DEBUG - 📌 Added static parameter: iteration_timeout = 60
2025-06-30 14:30:56 - TransitionHandler - DEBUG - 📌 Added static parameter: aggregation_type = collect_all
2025-06-30 14:30:56 - TransitionHandler - DEBUG - 📌 Added static parameter: include_metadata = False
2025-06-30 14:30:56 - TransitionHandler - DEBUG - 📌 Added static parameter: on_iteration_error = retry_once
2025-06-30 14:30:56 - TransitionHandler - DEBUG - 📌 Added static parameter: include_errors = True
2025-06-30 14:30:56 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'start': '0', 'source_type': 'number_range', 'batch_size': '1', 'end': '6', 'step': '1', 'parallel_execution': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': False, 'on_iteration_error': 'retry_once', 'include_errors': True}
2025-06-30 14:30:56 - TransitionHandler - DEBUG - tool Parameters: {'start': '0', 'source_type': 'number_range', 'batch_size': '1', 'end': '6', 'step': '1', 'parallel_execution': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': False, 'on_iteration_error': 'retry_once', 'include_errors': True}
2025-06-30 14:30:56 - TransitionHandler - INFO - Invoking tool 'LoopNode' (tool_id: 1) for node 'LoopNode' in transition 'transition-LoopNode-*************' with parameters: {'start': '0', 'source_type': 'number_range', 'batch_size': '1', 'end': '6', 'step': '1', 'parallel_execution': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': False, 'on_iteration_error': 'retry_once', 'include_errors': True}
2025-06-30 14:30:56 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 9, corr_id 973e3432-58ac-433a-a97a-97514ea48579):
2025-06-30 14:30:56 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 973e3432-58ac-433a-a97a-97514ea48579, response: {'transition_id': 'transition-LoopNode-*************', 'node_id': 'LoopNode', 'tool_name': 'LoopNode', 'message': 'Connecting to server', 'result': 'Connecting to server LoopNode', 'status': 'connecting', 'sequence': 9, 'workflow_status': 'running'}
2025-06-30 14:30:56 - TransitionHandler - DEBUG - 🔄 Resolving loop config parameters for transition: transition-LoopNode-*************
2025-06-30 14:30:56 - TransitionHandler - DEBUG - ✅ Loop config parameter resolution completed for transition: transition-LoopNode-*************
2025-06-30 14:30:56 - TransitionHandler - DEBUG - 🔍 Detected exit transition: transition-MergeDataComponent-************* (has final/aggregated indicators)
2025-06-30 14:30:56 - TransitionHandler - DEBUG - 🔍 Auto-detected loop body transition: transition-CombineTextComponent-************* (has current_item/iteration indicators)
2025-06-30 14:30:56 - TransitionHandler - INFO - 🔍 Auto-detected and added loop body transitions to config: ['transition-CombineTextComponent-*************']
2025-06-30 14:30:56 - LoopExecutor - INFO - 🚨 execute_tool called with transition_id: transition-LoopNode-*************
2025-06-30 14:30:56 - LoopExecutor - INFO - 🔄 Starting loop node execution for transition: transition-LoopNode-*************
2025-06-30 14:30:56 - LoopExecutor - DEBUG - 🔢 Detected number_range from 1 to 6 (step: 1), batch_size: 1
2025-06-30 14:30:56 - LoopExecutor - DEBUG - Parsed loop config: {'loop_type': 'context_independent', 'aggregation_config': {'type': 'list'}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': False}, 'iteration_source': {'type': 'range', 'data': {'start': 1, 'stop': 7, 'step': 1}, 'batch_size': 1, 'original': {'number_range': {'start': 1, 'end': 6}, 'step': 1}}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'concurrency': {'enabled': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'retry_config': {'max_retries': 1, 'retry_delay': 1.0}, 'iteration_behavior': 'independent', 'iteration_settings': {'parallel_execution': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'error_handling': {'on_iteration_error': 'retry_once', 'include_errors': True}, 'loop_body_transitions': ['transition-CombineTextComponent-*************'], 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-CombineTextComponent-1750920624318'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}}
2025-06-30 14:30:56 - LoopExecutor - DEBUG - Loop configuration validation passed
2025-06-30 14:30:56 - TransitionHandler - DEBUG - 📝 Registered loop executor for transition: transition-LoopNode-*************
2025-06-30 14:30:56 - LoopExecutor - DEBUG - 📝 Registered loop executor with transition handler for transition: transition-LoopNode-*************
2025-06-30 14:30:56 - LoopExecutor - INFO - Prepared 6 iterations from range source
2025-06-30 14:30:56 - LoopExecutor - INFO - 🔄 Initialized loop state for 6 iterations
2025-06-30 14:30:56 - LoopExecutor - DEBUG - Starting loop workflow execution
2025-06-30 14:30:56 - LoopExecutor - DEBUG - Loop state: {'loop_config': {'loop_type': 'context_independent', 'aggregation_config': {'type': 'list'}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': False}, 'iteration_source': {'type': 'range', 'data': {'start': 1, 'stop': 7, 'step': 1}, 'batch_size': 1, 'original': {'number_range': {'start': 1, 'end': 6}, 'step': 1}}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'concurrency': {'enabled': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'retry_config': {'max_retries': 1, 'retry_delay': 1.0}, 'iteration_behavior': 'independent', 'iteration_settings': {'parallel_execution': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'error_handling': {'on_iteration_error': 'retry_once', 'include_errors': True}, 'loop_body_transitions': ['transition-CombineTextComponent-*************'], 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-CombineTextComponent-1750920624318'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}}, 'transition_id': 'transition-LoopNode-*************', 'iteration_data': [1, 2, 3, 4, 5, 6], 'loop_context': {'start_time': 311144.445553041, 'total_iterations': 6, 'current_iteration': 0}, 'input_data': {'start': '0', 'source_type': 'number_range', 'batch_size': '1', 'end': '6', 'step': '1', 'parallel_execution': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': False, 'on_iteration_error': 'retry_once', 'include_errors': True}}
2025-06-30 14:30:56 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_state keys: ['loop_config', 'transition_id', 'iteration_data', 'loop_context', 'input_data']
2025-06-30 14:30:56 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_config keys: ['loop_type', 'aggregation_config', 'result_aggregation', 'iteration_source', 'exit_condition', 'concurrency', 'retry_config', 'iteration_behavior', 'iteration_settings', 'error_handling', 'loop_body_transitions', 'loop_body_configuration']
2025-06-30 14:30:56 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_body_config: {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-CombineTextComponent-1750920624318'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}
2025-06-30 14:30:56 - LoopExecutor - DEBUG - 🔍 DEBUG: entry_transitions: ['transition-CombineTextComponent-*************']
2025-06-30 14:30:56 - LoopExecutor - DEBUG - 🔍 DEBUG: exit_transitions: ['transition-CombineTextComponent-1750920624318']
2025-06-30 14:30:56 - LoopExecutor - INFO - 🔄 About to start loop execution with 6 iterations
2025-06-30 14:30:56 - LoopExecutor - INFO - 🔄 Starting orchestration-coordinated iteration 1/6
2025-06-30 14:30:56 - LoopExecutor - DEBUG - 🔄 Executing iteration 1 chain with orchestration coordination
2025-06-30 14:30:56 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in memory: 1
2025-06-30 14:30:56 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_0
2025-06-30 14:30:57 - RedisManager - DEBUG - Set key 'result:loop_iteration_0' with TTL of 900 seconds
2025-06-30 14:30:57 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:30:57 - StateManager - INFO - Marked transition loop_iteration_0 as completed (was_pending=False, was_waiting=False)
2025-06-30 14:30:57 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'transition-AgenticAI-175**********', 'transition-AgenticAI-*************', 'loop_iteration_0'}
2025-06-30 14:30:57 - StateManager - DEBUG - Stored result for transition current_iteration in memory: 1
2025-06-30 14:30:57 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-06-30 14:30:57 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-06-30 14:30:57 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:30:57 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-06-30 14:30:57 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'transition-AgenticAI-175**********', 'current_iteration', 'transition-AgenticAI-*************', 'loop_iteration_0'}
2025-06-30 14:30:57 - StateManager - DEBUG - Stored result for transition loop_iteration_transition-LoopNode-*************_0 in memory: {'current_item': 1, 'iteration_index': 0, 'iteration_metadata': {'timestamp': 311145.963848666, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-06-30 14:30:58 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_transition-LoopNode-*************_0
2025-06-30 14:30:58 - RedisManager - DEBUG - Set key 'result:loop_iteration_transition-LoopNode-*************_0' with TTL of 900 seconds
2025-06-30 14:30:58 - StateManager - DEBUG - Stored result for transition loop_iteration_transition-LoopNode-*************_0 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:30:58 - StateManager - INFO - Marked transition loop_iteration_transition-LoopNode-*************_0 as completed (was_pending=False, was_waiting=False)
2025-06-30 14:30:58 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'transition-AgenticAI-175**********', 'current_iteration', 'loop_iteration_0', 'loop_iteration_transition-LoopNode-*************_0', 'transition-AgenticAI-*************'}
2025-06-30 14:30:58 - StateManager - DEBUG - Stored result for transition backup_transition-LoopNode-*************_iteration_0 in memory: {'current_item': 1, 'iteration_index': 0, 'iteration_metadata': {'timestamp': 311145.963848666, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-06-30 14:30:59 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:backup_transition-LoopNode-*************_iteration_0'
2025-06-30 14:30:59 - RedisManager - DEBUG - Set key 'result:backup_transition-LoopNode-*************_iteration_0' with TTL of 300 seconds
2025-06-30 14:30:59 - StateManager - DEBUG - Stored result for transition backup_transition-LoopNode-*************_iteration_0 in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:30:59 - StateManager - INFO - Marked transition backup_transition-LoopNode-*************_iteration_0 as completed (was_pending=False, was_waiting=False)
2025-06-30 14:30:59 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'backup_transition-LoopNode-*************_iteration_0', 'transition-AgenticAI-175**********', 'current_iteration', 'loop_iteration_0', 'loop_iteration_transition-LoopNode-*************_0', 'transition-AgenticAI-*************'}
2025-06-30 14:30:59 - LoopExecutor - DEBUG - 💾 Temporarily stored current iteration data under loop transition ID: transition-LoopNode-*************
2025-06-30 14:30:59 - LoopExecutor - DEBUG - 💾 Also stored with backup keys: loop_iteration_transition-LoopNode-*************_0, backup_transition-LoopNode-*************_iteration_0
2025-06-30 14:30:59 - LoopExecutor - DEBUG - 💾 Current iteration data: {'current_item': 1, 'iteration_index': 0, 'iteration_metadata': {'timestamp': 311145.963848666, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-06-30 14:30:59 - LoopExecutor - DEBUG - 💾 Injected iteration payload into state: loop_iteration_0
2025-06-30 14:30:59 - LoopExecutor - DEBUG - 🔄 Coordinating iteration 1 with orchestration engine
2025-06-30 14:30:59 - LoopExecutor - ERROR - ❌ Loop iteration coordination failed: 'LoopExecutor' object has no attribute 'transition_id'
2025-06-30 14:30:59 - LoopExecutor - ERROR - ❌ Iteration chain execution failed: 'LoopExecutor' object has no attribute 'transition_id'
2025-06-30 14:30:59 - LoopExecutor - DEBUG - Iteration 1 result: {'error': "Iteration chain execution failed: 'LoopExecutor' object has no attribute 'transition_id'", 'success': False, 'iteration_index': 0}
2025-06-30 14:30:59 - LoopExecutor - ERROR - Iteration 1 failed: Iteration chain execution failed: 'LoopExecutor' object has no attribute 'transition_id'
2025-06-30 14:30:59 - LoopExecutor - INFO - 🔄 Starting orchestration-coordinated iteration 2/6
2025-06-30 14:30:59 - LoopExecutor - DEBUG - 🔄 Executing iteration 2 chain with orchestration coordination
2025-06-30 14:30:59 - StateManager - DEBUG - Stored result for transition loop_iteration_1 in memory: 2
2025-06-30 14:30:59 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-30 14:30:59 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_1
2025-06-30 14:30:59 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 14:30:59 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 14:30:59 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-30 14:31:00 - RedisManager - DEBUG - Set key 'result:loop_iteration_1' with TTL of 900 seconds
2025-06-30 14:31:00 - StateManager - DEBUG - Stored result for transition loop_iteration_1 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:31:00 - StateManager - INFO - Marked transition loop_iteration_1 as completed (was_pending=False, was_waiting=False)
2025-06-30 14:31:00 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'backup_transition-LoopNode-*************_iteration_0', 'transition-AgenticAI-175**********', 'current_iteration', 'loop_iteration_0', 'loop_iteration_transition-LoopNode-*************_0', 'loop_iteration_1', 'transition-AgenticAI-*************'}
2025-06-30 14:31:00 - StateManager - DEBUG - Stored result for transition current_iteration in memory: 2
2025-06-30 14:31:00 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-06-30 14:31:00 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-06-30 14:31:00 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:31:00 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-06-30 14:31:00 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'backup_transition-LoopNode-*************_iteration_0', 'transition-AgenticAI-175**********', 'current_iteration', 'loop_iteration_0', 'loop_iteration_transition-LoopNode-*************_0', 'loop_iteration_1', 'transition-AgenticAI-*************'}
2025-06-30 14:31:00 - StateManager - DEBUG - Stored result for transition loop_iteration_transition-LoopNode-*************_1 in memory: {'current_item': 2, 'iteration_index': 1, 'iteration_metadata': {'timestamp': 311149.024439916, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-06-30 14:31:01 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_transition-LoopNode-*************_1
2025-06-30 14:31:01 - RedisManager - DEBUG - Set key 'result:loop_iteration_transition-LoopNode-*************_1' with TTL of 900 seconds
2025-06-30 14:31:01 - StateManager - DEBUG - Stored result for transition loop_iteration_transition-LoopNode-*************_1 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:31:01 - StateManager - INFO - Marked transition loop_iteration_transition-LoopNode-*************_1 as completed (was_pending=False, was_waiting=False)
2025-06-30 14:31:01 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'backup_transition-LoopNode-*************_iteration_0', 'transition-AgenticAI-175**********', 'current_iteration', 'loop_iteration_0', 'loop_iteration_transition-LoopNode-*************_0', 'loop_iteration_1', 'loop_iteration_transition-LoopNode-*************_1', 'transition-AgenticAI-*************'}
2025-06-30 14:31:01 - StateManager - DEBUG - Stored result for transition backup_transition-LoopNode-*************_iteration_1 in memory: {'current_item': 2, 'iteration_index': 1, 'iteration_metadata': {'timestamp': 311149.024439916, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-06-30 14:31:02 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:backup_transition-LoopNode-*************_iteration_1'
2025-06-30 14:31:02 - RedisManager - DEBUG - Set key 'result:backup_transition-LoopNode-*************_iteration_1' with TTL of 300 seconds
2025-06-30 14:31:02 - StateManager - DEBUG - Stored result for transition backup_transition-LoopNode-*************_iteration_1 in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:31:02 - StateManager - INFO - Marked transition backup_transition-LoopNode-*************_iteration_1 as completed (was_pending=False, was_waiting=False)
2025-06-30 14:31:02 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'backup_transition-LoopNode-*************_iteration_0', 'backup_transition-LoopNode-*************_iteration_1', 'transition-AgenticAI-175**********', 'current_iteration', 'loop_iteration_0', 'loop_iteration_transition-LoopNode-*************_0', 'loop_iteration_1', 'loop_iteration_transition-LoopNode-*************_1', 'transition-AgenticAI-*************'}
2025-06-30 14:31:02 - LoopExecutor - DEBUG - 💾 Temporarily stored current iteration data under loop transition ID: transition-LoopNode-*************
2025-06-30 14:31:02 - LoopExecutor - DEBUG - 💾 Also stored with backup keys: loop_iteration_transition-LoopNode-*************_1, backup_transition-LoopNode-*************_iteration_1
2025-06-30 14:31:02 - LoopExecutor - DEBUG - 💾 Current iteration data: {'current_item': 2, 'iteration_index': 1, 'iteration_metadata': {'timestamp': 311149.024439916, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-06-30 14:31:02 - LoopExecutor - DEBUG - 💾 Injected iteration payload into state: loop_iteration_1
2025-06-30 14:31:02 - LoopExecutor - DEBUG - 🔄 Coordinating iteration 2 with orchestration engine
2025-06-30 14:31:02 - LoopExecutor - ERROR - ❌ Loop iteration coordination failed: 'LoopExecutor' object has no attribute 'transition_id'
2025-06-30 14:31:02 - LoopExecutor - ERROR - ❌ Iteration chain execution failed: 'LoopExecutor' object has no attribute 'transition_id'
2025-06-30 14:31:02 - LoopExecutor - DEBUG - Iteration 2 result: {'error': "Iteration chain execution failed: 'LoopExecutor' object has no attribute 'transition_id'", 'success': False, 'iteration_index': 1}
2025-06-30 14:31:02 - LoopExecutor - ERROR - Iteration 2 failed: Iteration chain execution failed: 'LoopExecutor' object has no attribute 'transition_id'
2025-06-30 14:31:02 - LoopExecutor - INFO - 🔄 Starting orchestration-coordinated iteration 3/6
2025-06-30 14:31:02 - LoopExecutor - DEBUG - 🔄 Executing iteration 3 chain with orchestration coordination
2025-06-30 14:31:02 - StateManager - DEBUG - Stored result for transition loop_iteration_2 in memory: 3
2025-06-30 14:31:02 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_2
2025-06-30 14:31:03 - RedisManager - DEBUG - Set key 'result:loop_iteration_2' with TTL of 900 seconds
2025-06-30 14:31:03 - StateManager - DEBUG - Stored result for transition loop_iteration_2 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:31:03 - StateManager - INFO - Marked transition loop_iteration_2 as completed (was_pending=False, was_waiting=False)
2025-06-30 14:31:03 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'backup_transition-LoopNode-*************_iteration_0', 'backup_transition-LoopNode-*************_iteration_1', 'transition-AgenticAI-175**********', 'current_iteration', 'loop_iteration_0', 'loop_iteration_transition-LoopNode-*************_0', 'loop_iteration_1', 'loop_iteration_transition-LoopNode-*************_1', 'loop_iteration_2', 'transition-AgenticAI-*************'}
2025-06-30 14:31:03 - StateManager - DEBUG - Stored result for transition current_iteration in memory: 3
2025-06-30 14:31:03 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-06-30 14:31:03 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-06-30 14:31:03 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:31:03 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-06-30 14:31:03 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'backup_transition-LoopNode-*************_iteration_0', 'backup_transition-LoopNode-*************_iteration_1', 'transition-AgenticAI-175**********', 'current_iteration', 'loop_iteration_0', 'loop_iteration_transition-LoopNode-*************_0', 'loop_iteration_1', 'loop_iteration_transition-LoopNode-*************_1', 'loop_iteration_2', 'transition-AgenticAI-*************'}
2025-06-30 14:31:03 - StateManager - DEBUG - Stored result for transition loop_iteration_transition-LoopNode-*************_2 in memory: {'current_item': 3, 'iteration_index': 2, 'iteration_metadata': {'timestamp': 311152.061019, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-06-30 14:31:04 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_transition-LoopNode-*************_2
2025-06-30 14:31:04 - RedisManager - DEBUG - Set key 'result:loop_iteration_transition-LoopNode-*************_2' with TTL of 900 seconds
2025-06-30 14:31:04 - StateManager - DEBUG - Stored result for transition loop_iteration_transition-LoopNode-*************_2 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:31:04 - StateManager - INFO - Marked transition loop_iteration_transition-LoopNode-*************_2 as completed (was_pending=False, was_waiting=False)
2025-06-30 14:31:04 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'backup_transition-LoopNode-*************_iteration_0', 'backup_transition-LoopNode-*************_iteration_1', 'loop_iteration_transition-LoopNode-*************_2', 'transition-AgenticAI-175**********', 'current_iteration', 'loop_iteration_0', 'loop_iteration_transition-LoopNode-*************_0', 'loop_iteration_1', 'loop_iteration_transition-LoopNode-*************_1', 'loop_iteration_2', 'transition-AgenticAI-*************'}
2025-06-30 14:31:04 - StateManager - DEBUG - Stored result for transition backup_transition-LoopNode-*************_iteration_2 in memory: {'current_item': 3, 'iteration_index': 2, 'iteration_metadata': {'timestamp': 311152.061019, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-06-30 14:31:05 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:backup_transition-LoopNode-*************_iteration_2'
2025-06-30 14:31:05 - RedisManager - DEBUG - Set key 'result:backup_transition-LoopNode-*************_iteration_2' with TTL of 300 seconds
2025-06-30 14:31:05 - StateManager - DEBUG - Stored result for transition backup_transition-LoopNode-*************_iteration_2 in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:31:05 - StateManager - INFO - Marked transition backup_transition-LoopNode-*************_iteration_2 as completed (was_pending=False, was_waiting=False)
2025-06-30 14:31:05 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'backup_transition-LoopNode-*************_iteration_0', 'backup_transition-LoopNode-*************_iteration_1', 'loop_iteration_transition-LoopNode-*************_2', 'transition-AgenticAI-175**********', 'current_iteration', 'loop_iteration_0', 'loop_iteration_transition-LoopNode-*************_0', 'loop_iteration_1', 'loop_iteration_transition-LoopNode-*************_1', 'loop_iteration_2', 'backup_transition-LoopNode-*************_iteration_2', 'transition-AgenticAI-*************'}
2025-06-30 14:31:05 - LoopExecutor - DEBUG - 💾 Temporarily stored current iteration data under loop transition ID: transition-LoopNode-*************
2025-06-30 14:31:05 - LoopExecutor - DEBUG - 💾 Also stored with backup keys: loop_iteration_transition-LoopNode-*************_2, backup_transition-LoopNode-*************_iteration_2
2025-06-30 14:31:05 - LoopExecutor - DEBUG - 💾 Current iteration data: {'current_item': 3, 'iteration_index': 2, 'iteration_metadata': {'timestamp': 311152.061019, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-06-30 14:31:05 - LoopExecutor - DEBUG - 💾 Injected iteration payload into state: loop_iteration_2
2025-06-30 14:31:05 - LoopExecutor - DEBUG - 🔄 Coordinating iteration 3 with orchestration engine
2025-06-30 14:31:05 - LoopExecutor - ERROR - ❌ Loop iteration coordination failed: 'LoopExecutor' object has no attribute 'transition_id'
2025-06-30 14:31:05 - LoopExecutor - ERROR - ❌ Iteration chain execution failed: 'LoopExecutor' object has no attribute 'transition_id'
2025-06-30 14:31:05 - LoopExecutor - DEBUG - Iteration 3 result: {'error': "Iteration chain execution failed: 'LoopExecutor' object has no attribute 'transition_id'", 'success': False, 'iteration_index': 2}
2025-06-30 14:31:05 - LoopExecutor - ERROR - Iteration 3 failed: Iteration chain execution failed: 'LoopExecutor' object has no attribute 'transition_id'
2025-06-30 14:31:05 - LoopExecutor - INFO - 🔄 Starting orchestration-coordinated iteration 4/6
2025-06-30 14:31:05 - LoopExecutor - DEBUG - 🔄 Executing iteration 4 chain with orchestration coordination
2025-06-30 14:31:05 - StateManager - DEBUG - Stored result for transition loop_iteration_3 in memory: 4
2025-06-30 14:31:05 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_3
2025-06-30 14:31:06 - RedisManager - DEBUG - Set key 'result:loop_iteration_3' with TTL of 900 seconds
2025-06-30 14:31:06 - StateManager - DEBUG - Stored result for transition loop_iteration_3 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:31:06 - StateManager - INFO - Marked transition loop_iteration_3 as completed (was_pending=False, was_waiting=False)
2025-06-30 14:31:06 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'backup_transition-LoopNode-*************_iteration_0', 'backup_transition-LoopNode-*************_iteration_1', 'loop_iteration_transition-LoopNode-*************_2', 'transition-AgenticAI-175**********', 'current_iteration', 'loop_iteration_0', 'loop_iteration_transition-LoopNode-*************_0', 'loop_iteration_1', 'loop_iteration_transition-LoopNode-*************_1', 'loop_iteration_2', 'backup_transition-LoopNode-*************_iteration_2', 'loop_iteration_3', 'transition-AgenticAI-*************'}
2025-06-30 14:31:06 - StateManager - DEBUG - Stored result for transition current_iteration in memory: 4
2025-06-30 14:31:06 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-06-30 14:31:06 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-06-30 14:31:06 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:31:06 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-06-30 14:31:06 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'backup_transition-LoopNode-*************_iteration_0', 'backup_transition-LoopNode-*************_iteration_1', 'loop_iteration_transition-LoopNode-*************_2', 'transition-AgenticAI-175**********', 'current_iteration', 'loop_iteration_0', 'loop_iteration_transition-LoopNode-*************_0', 'loop_iteration_1', 'loop_iteration_transition-LoopNode-*************_1', 'loop_iteration_2', 'backup_transition-LoopNode-*************_iteration_2', 'loop_iteration_3', 'transition-AgenticAI-*************'}
2025-06-30 14:31:06 - StateManager - DEBUG - Stored result for transition loop_iteration_transition-LoopNode-*************_3 in memory: {'current_item': 4, 'iteration_index': 3, 'iteration_metadata': {'timestamp': 311155.097497, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-06-30 14:31:07 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_transition-LoopNode-*************_3
2025-06-30 14:31:07 - RedisManager - DEBUG - Set key 'result:loop_iteration_transition-LoopNode-*************_3' with TTL of 900 seconds
2025-06-30 14:31:07 - StateManager - DEBUG - Stored result for transition loop_iteration_transition-LoopNode-*************_3 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:31:07 - StateManager - INFO - Marked transition loop_iteration_transition-LoopNode-*************_3 as completed (was_pending=False, was_waiting=False)
2025-06-30 14:31:07 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'backup_transition-LoopNode-*************_iteration_0', 'backup_transition-LoopNode-*************_iteration_1', 'loop_iteration_transition-LoopNode-*************_2', 'transition-AgenticAI-175**********', 'current_iteration', 'loop_iteration_0', 'loop_iteration_transition-LoopNode-*************_0', 'loop_iteration_1', 'loop_iteration_transition-LoopNode-*************_1', 'loop_iteration_2', 'backup_transition-LoopNode-*************_iteration_2', 'loop_iteration_3', 'transition-AgenticAI-*************', 'loop_iteration_transition-LoopNode-*************_3'}
2025-06-30 14:31:07 - StateManager - DEBUG - Stored result for transition backup_transition-LoopNode-*************_iteration_3 in memory: {'current_item': 4, 'iteration_index': 3, 'iteration_metadata': {'timestamp': 311155.097497, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-06-30 14:31:08 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:backup_transition-LoopNode-*************_iteration_3'
2025-06-30 14:31:08 - RedisManager - DEBUG - Set key 'result:backup_transition-LoopNode-*************_iteration_3' with TTL of 300 seconds
2025-06-30 14:31:08 - StateManager - DEBUG - Stored result for transition backup_transition-LoopNode-*************_iteration_3 in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:31:08 - StateManager - INFO - Marked transition backup_transition-LoopNode-*************_iteration_3 as completed (was_pending=False, was_waiting=False)
2025-06-30 14:31:08 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'backup_transition-LoopNode-*************_iteration_0', 'backup_transition-LoopNode-*************_iteration_1', 'backup_transition-LoopNode-*************_iteration_3', 'loop_iteration_transition-LoopNode-*************_2', 'transition-AgenticAI-175**********', 'current_iteration', 'loop_iteration_0', 'loop_iteration_transition-LoopNode-*************_0', 'loop_iteration_1', 'loop_iteration_transition-LoopNode-*************_1', 'loop_iteration_2', 'backup_transition-LoopNode-*************_iteration_2', 'loop_iteration_3', 'transition-AgenticAI-*************', 'loop_iteration_transition-LoopNode-*************_3'}
2025-06-30 14:31:08 - LoopExecutor - DEBUG - 💾 Temporarily stored current iteration data under loop transition ID: transition-LoopNode-*************
2025-06-30 14:31:08 - LoopExecutor - DEBUG - 💾 Also stored with backup keys: loop_iteration_transition-LoopNode-*************_3, backup_transition-LoopNode-*************_iteration_3
2025-06-30 14:31:08 - LoopExecutor - DEBUG - 💾 Current iteration data: {'current_item': 4, 'iteration_index': 3, 'iteration_metadata': {'timestamp': 311155.097497, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-06-30 14:31:08 - LoopExecutor - DEBUG - 💾 Injected iteration payload into state: loop_iteration_3
2025-06-30 14:31:08 - LoopExecutor - DEBUG - 🔄 Coordinating iteration 4 with orchestration engine
2025-06-30 14:31:08 - LoopExecutor - ERROR - ❌ Loop iteration coordination failed: 'LoopExecutor' object has no attribute 'transition_id'
2025-06-30 14:31:08 - LoopExecutor - ERROR - ❌ Iteration chain execution failed: 'LoopExecutor' object has no attribute 'transition_id'
2025-06-30 14:31:08 - LoopExecutor - DEBUG - Iteration 4 result: {'error': "Iteration chain execution failed: 'LoopExecutor' object has no attribute 'transition_id'", 'success': False, 'iteration_index': 3}
2025-06-30 14:31:08 - LoopExecutor - ERROR - Iteration 4 failed: Iteration chain execution failed: 'LoopExecutor' object has no attribute 'transition_id'
2025-06-30 14:31:08 - LoopExecutor - INFO - 🔄 Starting orchestration-coordinated iteration 5/6
2025-06-30 14:31:08 - LoopExecutor - DEBUG - 🔄 Executing iteration 5 chain with orchestration coordination
2025-06-30 14:31:08 - StateManager - DEBUG - Stored result for transition loop_iteration_4 in memory: 5
2025-06-30 14:31:08 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_4
2025-06-30 14:31:09 - RedisManager - DEBUG - Set key 'result:loop_iteration_4' with TTL of 900 seconds
2025-06-30 14:31:09 - StateManager - DEBUG - Stored result for transition loop_iteration_4 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:31:09 - StateManager - INFO - Marked transition loop_iteration_4 as completed (was_pending=False, was_waiting=False)
2025-06-30 14:31:09 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'backup_transition-LoopNode-*************_iteration_0', 'backup_transition-LoopNode-*************_iteration_1', 'backup_transition-LoopNode-*************_iteration_3', 'loop_iteration_transition-LoopNode-*************_2', 'transition-AgenticAI-175**********', 'current_iteration', 'loop_iteration_0', 'loop_iteration_transition-LoopNode-*************_0', 'loop_iteration_1', 'loop_iteration_transition-LoopNode-*************_1', 'loop_iteration_2', 'backup_transition-LoopNode-*************_iteration_2', 'loop_iteration_3', 'transition-AgenticAI-*************', 'loop_iteration_4', 'loop_iteration_transition-LoopNode-*************_3'}
2025-06-30 14:31:09 - StateManager - DEBUG - Stored result for transition current_iteration in memory: 5
2025-06-30 14:31:09 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-06-30 14:31:09 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-06-30 14:31:09 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:31:09 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-06-30 14:31:09 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'backup_transition-LoopNode-*************_iteration_0', 'backup_transition-LoopNode-*************_iteration_1', 'backup_transition-LoopNode-*************_iteration_3', 'loop_iteration_transition-LoopNode-*************_2', 'transition-AgenticAI-175**********', 'current_iteration', 'loop_iteration_0', 'loop_iteration_transition-LoopNode-*************_0', 'loop_iteration_1', 'loop_iteration_transition-LoopNode-*************_1', 'loop_iteration_2', 'backup_transition-LoopNode-*************_iteration_2', 'loop_iteration_3', 'transition-AgenticAI-*************', 'loop_iteration_4', 'loop_iteration_transition-LoopNode-*************_3'}
2025-06-30 14:31:09 - StateManager - DEBUG - Stored result for transition loop_iteration_transition-LoopNode-*************_4 in memory: {'current_item': 5, 'iteration_index': 4, 'iteration_metadata': {'timestamp': 311158.118851291, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-06-30 14:31:10 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_transition-LoopNode-*************_4
2025-06-30 14:31:10 - RedisManager - DEBUG - Set key 'result:loop_iteration_transition-LoopNode-*************_4' with TTL of 900 seconds
2025-06-30 14:31:10 - StateManager - DEBUG - Stored result for transition loop_iteration_transition-LoopNode-*************_4 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:31:10 - StateManager - INFO - Marked transition loop_iteration_transition-LoopNode-*************_4 as completed (was_pending=False, was_waiting=False)
2025-06-30 14:31:10 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'backup_transition-LoopNode-*************_iteration_0', 'backup_transition-LoopNode-*************_iteration_1', 'loop_iteration_transition-LoopNode-*************_4', 'backup_transition-LoopNode-*************_iteration_3', 'loop_iteration_transition-LoopNode-*************_2', 'transition-AgenticAI-175**********', 'current_iteration', 'loop_iteration_0', 'loop_iteration_transition-LoopNode-*************_0', 'loop_iteration_1', 'loop_iteration_transition-LoopNode-*************_1', 'loop_iteration_2', 'backup_transition-LoopNode-*************_iteration_2', 'loop_iteration_3', 'transition-AgenticAI-*************', 'loop_iteration_4', 'loop_iteration_transition-LoopNode-*************_3'}
2025-06-30 14:31:10 - StateManager - DEBUG - Stored result for transition backup_transition-LoopNode-*************_iteration_4 in memory: {'current_item': 5, 'iteration_index': 4, 'iteration_metadata': {'timestamp': 311158.118851291, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-06-30 14:31:11 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:backup_transition-LoopNode-*************_iteration_4'
2025-06-30 14:31:11 - RedisManager - DEBUG - Set key 'result:backup_transition-LoopNode-*************_iteration_4' with TTL of 300 seconds
2025-06-30 14:31:11 - StateManager - DEBUG - Stored result for transition backup_transition-LoopNode-*************_iteration_4 in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:31:11 - StateManager - INFO - Marked transition backup_transition-LoopNode-*************_iteration_4 as completed (was_pending=False, was_waiting=False)
2025-06-30 14:31:11 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'backup_transition-LoopNode-*************_iteration_0', 'backup_transition-LoopNode-*************_iteration_1', 'loop_iteration_transition-LoopNode-*************_4', 'backup_transition-LoopNode-*************_iteration_3', 'loop_iteration_transition-LoopNode-*************_2', 'transition-AgenticAI-175**********', 'current_iteration', 'loop_iteration_0', 'loop_iteration_transition-LoopNode-*************_0', 'loop_iteration_1', 'loop_iteration_transition-LoopNode-*************_1', 'loop_iteration_2', 'backup_transition-LoopNode-*************_iteration_2', 'loop_iteration_3', 'transition-AgenticAI-*************', 'loop_iteration_4', 'backup_transition-LoopNode-*************_iteration_4', 'loop_iteration_transition-LoopNode-*************_3'}
2025-06-30 14:31:11 - LoopExecutor - DEBUG - 💾 Temporarily stored current iteration data under loop transition ID: transition-LoopNode-*************
2025-06-30 14:31:11 - LoopExecutor - DEBUG - 💾 Also stored with backup keys: loop_iteration_transition-LoopNode-*************_4, backup_transition-LoopNode-*************_iteration_4
2025-06-30 14:31:11 - LoopExecutor - DEBUG - 💾 Current iteration data: {'current_item': 5, 'iteration_index': 4, 'iteration_metadata': {'timestamp': 311158.118851291, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-06-30 14:31:11 - LoopExecutor - DEBUG - 💾 Injected iteration payload into state: loop_iteration_4
2025-06-30 14:31:11 - LoopExecutor - DEBUG - 🔄 Coordinating iteration 5 with orchestration engine
2025-06-30 14:31:11 - LoopExecutor - ERROR - ❌ Loop iteration coordination failed: 'LoopExecutor' object has no attribute 'transition_id'
2025-06-30 14:31:11 - LoopExecutor - ERROR - ❌ Iteration chain execution failed: 'LoopExecutor' object has no attribute 'transition_id'
2025-06-30 14:31:11 - LoopExecutor - DEBUG - Iteration 5 result: {'error': "Iteration chain execution failed: 'LoopExecutor' object has no attribute 'transition_id'", 'success': False, 'iteration_index': 4}
2025-06-30 14:31:11 - LoopExecutor - ERROR - Iteration 5 failed: Iteration chain execution failed: 'LoopExecutor' object has no attribute 'transition_id'
2025-06-30 14:31:11 - LoopExecutor - INFO - 🔄 Starting orchestration-coordinated iteration 6/6
2025-06-30 14:31:11 - LoopExecutor - DEBUG - 🔄 Executing iteration 6 chain with orchestration coordination
2025-06-30 14:31:11 - StateManager - DEBUG - Stored result for transition loop_iteration_5 in memory: 6
2025-06-30 14:31:11 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_5
2025-06-30 14:31:12 - RedisManager - DEBUG - Set key 'result:loop_iteration_5' with TTL of 900 seconds
2025-06-30 14:31:12 - StateManager - DEBUG - Stored result for transition loop_iteration_5 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:31:12 - StateManager - INFO - Marked transition loop_iteration_5 as completed (was_pending=False, was_waiting=False)
2025-06-30 14:31:12 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'backup_transition-LoopNode-*************_iteration_0', 'backup_transition-LoopNode-*************_iteration_3', 'backup_transition-LoopNode-*************_iteration_2', 'loop_iteration_4', 'loop_iteration_transition-LoopNode-*************_1', 'transition-AgenticAI-*************', 'backup_transition-LoopNode-*************_iteration_4', 'loop_iteration_transition-LoopNode-*************_0', 'loop_iteration_transition-LoopNode-*************_2', 'transition-AgenticAI-175**********', 'current_iteration', 'loop_iteration_2', 'loop_iteration_5', 'loop_iteration_3', 'backup_transition-LoopNode-*************_iteration_1', 'loop_iteration_transition-LoopNode-*************_4', 'loop_iteration_0', 'loop_iteration_1', 'loop_iteration_transition-LoopNode-*************_3'}
2025-06-30 14:31:12 - StateManager - DEBUG - Stored result for transition current_iteration in memory: 6
2025-06-30 14:31:12 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-06-30 14:31:13 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-06-30 14:31:13 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:31:13 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-06-30 14:31:13 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'backup_transition-LoopNode-*************_iteration_0', 'backup_transition-LoopNode-*************_iteration_3', 'backup_transition-LoopNode-*************_iteration_2', 'loop_iteration_4', 'loop_iteration_transition-LoopNode-*************_1', 'transition-AgenticAI-*************', 'backup_transition-LoopNode-*************_iteration_4', 'loop_iteration_transition-LoopNode-*************_0', 'loop_iteration_transition-LoopNode-*************_2', 'transition-AgenticAI-175**********', 'current_iteration', 'loop_iteration_2', 'loop_iteration_5', 'loop_iteration_3', 'backup_transition-LoopNode-*************_iteration_1', 'loop_iteration_transition-LoopNode-*************_4', 'loop_iteration_0', 'loop_iteration_1', 'loop_iteration_transition-LoopNode-*************_3'}
2025-06-30 14:31:13 - StateManager - DEBUG - Stored result for transition loop_iteration_transition-LoopNode-*************_5 in memory: {'current_item': 6, 'iteration_index': 5, 'iteration_metadata': {'timestamp': 311161.157600083, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-06-30 14:31:13 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_transition-LoopNode-*************_5
2025-06-30 14:31:13 - RedisManager - DEBUG - Set key 'result:loop_iteration_transition-LoopNode-*************_5' with TTL of 900 seconds
2025-06-30 14:31:13 - StateManager - DEBUG - Stored result for transition loop_iteration_transition-LoopNode-*************_5 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:31:13 - StateManager - INFO - Marked transition loop_iteration_transition-LoopNode-*************_5 as completed (was_pending=False, was_waiting=False)
2025-06-30 14:31:13 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'backup_transition-LoopNode-*************_iteration_0', 'loop_iteration_transition-LoopNode-*************_5', 'backup_transition-LoopNode-*************_iteration_3', 'backup_transition-LoopNode-*************_iteration_2', 'loop_iteration_4', 'loop_iteration_transition-LoopNode-*************_1', 'transition-AgenticAI-*************', 'backup_transition-LoopNode-*************_iteration_4', 'loop_iteration_transition-LoopNode-*************_0', 'loop_iteration_transition-LoopNode-*************_2', 'transition-AgenticAI-175**********', 'current_iteration', 'loop_iteration_2', 'loop_iteration_5', 'loop_iteration_3', 'backup_transition-LoopNode-*************_iteration_1', 'loop_iteration_transition-LoopNode-*************_4', 'loop_iteration_0', 'loop_iteration_1', 'loop_iteration_transition-LoopNode-*************_3'}
2025-06-30 14:31:13 - StateManager - DEBUG - Stored result for transition backup_transition-LoopNode-*************_iteration_5 in memory: {'current_item': 6, 'iteration_index': 5, 'iteration_metadata': {'timestamp': 311161.157600083, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-06-30 14:31:14 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:backup_transition-LoopNode-*************_iteration_5'
2025-06-30 14:31:14 - RedisManager - DEBUG - Set key 'result:backup_transition-LoopNode-*************_iteration_5' with TTL of 300 seconds
2025-06-30 14:31:14 - StateManager - DEBUG - Stored result for transition backup_transition-LoopNode-*************_iteration_5 in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:31:14 - StateManager - INFO - Marked transition backup_transition-LoopNode-*************_iteration_5 as completed (was_pending=False, was_waiting=False)
2025-06-30 14:31:14 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'backup_transition-LoopNode-*************_iteration_0', 'loop_iteration_transition-LoopNode-*************_5', 'backup_transition-LoopNode-*************_iteration_3', 'backup_transition-LoopNode-*************_iteration_2', 'loop_iteration_4', 'loop_iteration_transition-LoopNode-*************_1', 'transition-AgenticAI-*************', 'backup_transition-LoopNode-*************_iteration_4', 'loop_iteration_transition-LoopNode-*************_0', 'loop_iteration_transition-LoopNode-*************_2', 'transition-AgenticAI-175**********', 'current_iteration', 'loop_iteration_2', 'loop_iteration_5', 'loop_iteration_3', 'backup_transition-LoopNode-*************_iteration_1', 'loop_iteration_transition-LoopNode-*************_4', 'backup_transition-LoopNode-*************_iteration_5', 'loop_iteration_0', 'loop_iteration_1', 'loop_iteration_transition-LoopNode-*************_3'}
2025-06-30 14:31:14 - LoopExecutor - DEBUG - 💾 Temporarily stored current iteration data under loop transition ID: transition-LoopNode-*************
2025-06-30 14:31:14 - LoopExecutor - DEBUG - 💾 Also stored with backup keys: loop_iteration_transition-LoopNode-*************_5, backup_transition-LoopNode-*************_iteration_5
2025-06-30 14:31:14 - LoopExecutor - DEBUG - 💾 Current iteration data: {'current_item': 6, 'iteration_index': 5, 'iteration_metadata': {'timestamp': 311161.157600083, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-06-30 14:31:14 - LoopExecutor - DEBUG - 💾 Injected iteration payload into state: loop_iteration_5
2025-06-30 14:31:14 - LoopExecutor - DEBUG - 🔄 Coordinating iteration 6 with orchestration engine
2025-06-30 14:31:14 - LoopExecutor - ERROR - ❌ Loop iteration coordination failed: 'LoopExecutor' object has no attribute 'transition_id'
2025-06-30 14:31:14 - LoopExecutor - ERROR - ❌ Iteration chain execution failed: 'LoopExecutor' object has no attribute 'transition_id'
2025-06-30 14:31:14 - LoopExecutor - DEBUG - Iteration 6 result: {'error': "Iteration chain execution failed: 'LoopExecutor' object has no attribute 'transition_id'", 'success': False, 'iteration_index': 5}
2025-06-30 14:31:14 - LoopExecutor - ERROR - Iteration 6 failed: Iteration chain execution failed: 'LoopExecutor' object has no attribute 'transition_id'
2025-06-30 14:31:14 - LoopExecutor - DEBUG - Aggregating 0 iteration results
2025-06-30 14:31:14 - LoopExecutor - DEBUG - Using aggregation_config: {'type': 'list'}
2025-06-30 14:31:14 - LoopExecutor - DEBUG - Aggregating 0 iteration results using aggregation_type: list
2025-06-30 14:31:14 - LoopExecutor - DEBUG - 🔍 Extracted 0 data items from iterations
2025-06-30 14:31:14 - LoopExecutor - DEBUG - 🔍 Sample extracted data: None
2025-06-30 14:31:14 - LoopExecutor - WARNING - ⚠️ Unknown aggregation type: list, using collect_all
2025-06-30 14:31:14 - LoopExecutor - DEBUG - Aggregation completed, returning: 0 results
2025-06-30 14:31:14 - LoopExecutor - INFO - 📊 Loop execution completed with metadata: {'success': True, 'final_results': [], 'iteration_count': 0, 'total_iterations': 6}
2025-06-30 14:31:14 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 10, corr_id 973e3432-58ac-433a-a97a-97514ea48579):
2025-06-30 14:31:14 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 973e3432-58ac-433a-a97a-97514ea48579, response: {'status': 'loop_completed', 'metadata': {'success': True, 'final_results': [], 'iteration_count': 0, 'total_iterations': 6}, 'transition_id': 'transition-LoopNode-*************', 'sequence': 10, 'workflow_status': 'running'}
2025-06-30 14:31:14 - LoopExecutor - DEBUG - With orchestration engine, returning structured result
2025-06-30 14:31:14 - LoopExecutor - INFO - ✅ Loop node execution completed for transition: transition-LoopNode-*************
2025-06-30 14:31:14 - TransitionHandler - DEBUG - 🗑️ Unregistered loop executor for transition: transition-LoopNode-*************
2025-06-30 14:31:14 - LoopExecutor - DEBUG - 🗑️ Unregistered loop executor from transition handler for transition: transition-LoopNode-*************
2025-06-30 14:31:14 - TransitionHandler - INFO - Execution result from loop executor: {
  "final_results": []
}
2025-06-30 14:31:14 - TransitionHandler - INFO - Checking execution result for errors: {
  "final_results": []
}
2025-06-30 14:31:14 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 11, corr_id 973e3432-58ac-433a-a97a-97514ea48579):
2025-06-30 14:31:14 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 973e3432-58ac-433a-a97a-97514ea48579, response: {'transition_id': 'transition-LoopNode-*************', 'node_id': 'LoopNode', 'tool_name': 'LoopNode', 'message': 'Transition Result received.', 'result': {'final_results': []}, 'status': 'completed', 'sequence': 11, 'workflow_status': 'running', 'approval_required': False}
2025-06-30 14:31:14 - TransitionHandler - INFO - 🔄 Handling loop completion for transition: transition-LoopNode-*************
2025-06-30 14:31:14 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in memory: {'final_results': []}
2025-06-30 14:31:15 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-LoopNode-*************'
2025-06-30 14:31:15 - RedisManager - DEBUG - Set key 'result:transition-LoopNode-*************' with TTL of 300 seconds
2025-06-30 14:31:15 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:31:15 - StateManager - INFO - Marked transition transition-LoopNode-************* as completed (was_pending=False, was_waiting=False)
2025-06-30 14:31:15 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'backup_transition-LoopNode-*************_iteration_0', 'loop_iteration_transition-LoopNode-*************_5', 'backup_transition-LoopNode-*************_iteration_3', 'backup_transition-LoopNode-*************_iteration_2', 'loop_iteration_4', 'loop_iteration_transition-LoopNode-*************_1', 'transition-AgenticAI-*************', 'backup_transition-LoopNode-*************_iteration_4', 'loop_iteration_transition-LoopNode-*************_0', 'loop_iteration_transition-LoopNode-*************_2', 'transition-AgenticAI-175**********', 'transition-LoopNode-*************', 'current_iteration', 'loop_iteration_2', 'loop_iteration_5', 'loop_iteration_3', 'backup_transition-LoopNode-*************_iteration_1', 'loop_iteration_transition-LoopNode-*************_4', 'backup_transition-LoopNode-*************_iteration_5', 'loop_iteration_0', 'loop_iteration_1', 'loop_iteration_transition-LoopNode-*************_3'}
2025-06-30 14:31:15 - TransitionHandler - DEBUG - 🔍 Detected exit transition: transition-MergeDataComponent-************* (has final/aggregated indicators)
2025-06-30 14:31:15 - TransitionHandler - DEBUG - 🔍 Auto-detected loop body transition: transition-CombineTextComponent-************* (has current_item/iteration indicators)
2025-06-30 14:31:15 - TransitionHandler - INFO - 🔍 Auto-detected loop body transitions: ['transition-CombineTextComponent-*************']
2025-06-30 14:31:15 - TransitionHandler - DEBUG - 🔍 Loop completion filtering - Loop config: {'iteration_behavior': 'independent', 'iteration_source': {'number_range': {'start': 1, 'end': 6}, 'step': 1}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'iteration_settings': {'parallel_execution': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': False}, 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-CombineTextComponent-1750920624318'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}, 'error_handling': {'on_iteration_error': 'retry_once', 'include_errors': True}}
2025-06-30 14:31:15 - TransitionHandler - DEBUG - 🔍 Loop body transitions to filter: ['transition-CombineTextComponent-*************']
2025-06-30 14:31:15 - TransitionHandler - DEBUG - 🔍 Output data configs: [{'to_transition_id': 'transition-MergeDataComponent-*************', 'target_node_id': 'Merge Data', 'data_type': 'string', 'output_handle_registry': {'handle_mappings': [{'handle_id': 'final_results', 'result_path': 'final_results', 'edge_id': 'reactflow__edge-LoopNode-*************final_results-MergeDataComponent-*************main_input'}]}}, {'to_transition_id': 'transition-CombineTextComponent-*************', 'target_node_id': 'Combine Text', 'data_type': 'string', 'output_handle_registry': {'handle_mappings': [{'handle_id': 'current_item', 'result_path': 'current_item', 'edge_id': 'reactflow__edge-LoopNode-*************current_item-CombineTextComponent-*************main_input'}]}}]
2025-06-30 14:31:15 - TransitionHandler - DEBUG - ✅ Adding exit transition 'transition-MergeDataComponent-*************' to next transitions
2025-06-30 14:31:15 - TransitionHandler - DEBUG - 🚫 Skipping loop body transition 'transition-CombineTextComponent-*************' - should have been executed internally by loop executor
2025-06-30 14:31:15 - TransitionHandler - INFO - ✅ Loop completion handled for transition-LoopNode-*************. Next transitions: ['transition-MergeDataComponent-*************']
2025-06-30 14:31:15 - TransitionHandler - INFO - Completed transition transition-LoopNode-************* in 19.77 seconds
2025-06-30 14:31:15 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 12, corr_id 973e3432-58ac-433a-a97a-97514ea48579):
2025-06-30 14:31:15 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 973e3432-58ac-433a-a97a-97514ea48579, response: {'result': 'Completed transition in 19.77 seconds', 'message': 'Transition completed in 19.77 seconds', 'transition_id': 'transition-LoopNode-*************', 'status': 'time_logged', 'sequence': 12, 'workflow_status': 'running'}
2025-06-30 14:31:15 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-LoopNode-*************: ['transition-MergeDataComponent-*************']
2025-06-30 14:31:15 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 1
2025-06-30 14:31:15 - EnhancedWorkflowEngine - DEBUG - Results: [['transition-MergeDataComponent-*************']]
2025-06-30 14:31:15 - EnhancedWorkflowEngine - DEBUG - 🔄 Orchestration engine received result for transition-LoopNode-*************: ['transition-MergeDataComponent-*************']
2025-06-30 14:31:15 - EnhancedWorkflowEngine - DEBUG - 🔄 Result type: <class 'list'>, is_list: True
2025-06-30 14:31:15 - EnhancedWorkflowEngine - INFO - Transition transition-LoopNode-************* completed successfully: 1 next transitions
2025-06-30 14:31:15 - EnhancedWorkflowEngine - DEBUG - 🔄 Added to next_transitions_to_execute: ['transition-MergeDataComponent-*************']
2025-06-30 14:31:15 - TransitionHandler - INFO - Resolved next transitions (direct transition IDs): ['transition-MergeDataComponent-*************']
2025-06-30 14:31:15 - EnhancedWorkflowEngine - INFO - Adding transition transition-MergeDataComponent-************* to pending (all dependencies met)
2025-06-30 14:31:15 - StateManager - DEBUG - Workflow active: {'transition-MergeDataComponent-*************'}
2025-06-30 14:31:15 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:973e3432-58ac-433a-a97a-97514ea48579'
2025-06-30 14:31:16 - RedisManager - DEBUG - Set key 'workflow_state:973e3432-58ac-433a-a97a-97514ea48579' with TTL of 600 seconds
2025-06-30 14:31:16 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 973e3432-58ac-433a-a97a-97514ea48579. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:31:16 - StateManager - DEBUG - Checking waiting transitions: {'transition-CombineTextComponent-*************'}
2025-06-30 14:31:16 - StateManager - DEBUG - Transition transition-CombineTextComponent-************* is now ready (dependencies met: ['transition-AgenticAI-175**********', 'transition-AgenticAI-175**********', 'transition-LoopNode-*************'])
2025-06-30 14:31:16 - StateManager - INFO - Moved transitions from waiting to pending: {'transition-CombineTextComponent-*************'}
2025-06-30 14:31:16 - StateManager - DEBUG - Updated waiting=set(), pending={'transition-CombineTextComponent-*************', 'transition-MergeDataComponent-*************'}
2025-06-30 14:31:16 - StateManager - INFO - Cleared 2 pending transitions: {'transition-CombineTextComponent-*************', 'transition-MergeDataComponent-*************'}
2025-06-30 14:31:16 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-30 14:31:16 - StateManager - INFO - Terminated: False
2025-06-30 14:31:16 - StateManager - INFO - Pending transitions (0): []
2025-06-30 14:31:16 - StateManager - INFO - Waiting transitions (0): []
2025-06-30 14:31:16 - StateManager - INFO - Completed transitions (22): ['backup_transition-LoopNode-*************_iteration_0', 'backup_transition-LoopNode-*************_iteration_1', 'backup_transition-LoopNode-*************_iteration_2', 'backup_transition-LoopNode-*************_iteration_3', 'backup_transition-LoopNode-*************_iteration_4', 'backup_transition-LoopNode-*************_iteration_5', 'current_iteration', 'loop_iteration_0', 'loop_iteration_1', 'loop_iteration_2', 'loop_iteration_3', 'loop_iteration_4', 'loop_iteration_5', 'loop_iteration_transition-LoopNode-*************_0', 'loop_iteration_transition-LoopNode-*************_1', 'loop_iteration_transition-LoopNode-*************_2', 'loop_iteration_transition-LoopNode-*************_3', 'loop_iteration_transition-LoopNode-*************_4', 'loop_iteration_transition-LoopNode-*************_5', 'transition-AgenticAI-175**********', 'transition-AgenticAI-*************', 'transition-LoopNode-*************']
2025-06-30 14:31:16 - StateManager - INFO - Results stored for 22 transitions
2025-06-30 14:31:16 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-30 14:31:16 - StateManager - INFO - Workflow status: inactive
2025-06-30 14:31:16 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-30 14:31:16 - StateManager - INFO - Workflow status: inactive
2025-06-30 14:31:16 - StateManager - INFO - Workflow paused: False
2025-06-30 14:31:16 - StateManager - INFO - ==============================
2025-06-30 14:31:16 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-30 14:31:16 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 13, corr_id 973e3432-58ac-433a-a97a-97514ea48579):
2025-06-30 14:31:16 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 973e3432-58ac-433a-a97a-97514ea48579, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 13, 'workflow_status': 'running'}
2025-06-30 14:31:16 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-06-30 14:31:16 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-30 14:31:16 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-30 14:31:16 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-30 14:31:16 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-30 14:31:16 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-175********** from Redis
2025-06-30 14:31:16 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-175**********, extracting data
2025-06-30 14:31:16 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-175**********
2025-06-30 14:31:16 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-175**********
2025-06-30 14:31:17 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-175********** from Redis
2025-06-30 14:31:17 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-175**********, extracting data
2025-06-30 14:31:17 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-175**********
2025-06-30 14:31:17 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-175**********
2025-06-30 14:31:18 - StateManager - DEBUG - Retrieved result for transition transition-LoopNode-************* from Redis
2025-06-30 14:31:18 - StateManager - DEBUG - Extracted results for 1 tools in transition transition-LoopNode-*************
2025-06-30 14:31:18 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-30 14:31:18 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-175********** (total: 1)
2025-06-30 14:31:18 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-175********** (total: 2)
2025-06-30 14:31:18 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-30 14:31:18 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}
2025-06-30 14:31:18 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-30 14:31:18 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-06-30 14:31:18 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] (type: <class 'str'>)
2025-06-30 14:31:18 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-30 14:31:18 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-06-30 14:31:18 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}
2025-06-30 14:31:18 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-30 14:31:18 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-06-30 14:31:18 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] (type: <class 'str'>)
2025-06-30 14:31:18 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-30 14:31:18 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-06-30 14:31:18 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'final_results': []}
2025-06-30 14:31:18 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['final_results']
2025-06-30 14:31:18 - WorkflowUtils - DEBUG - Could not find result.result or result in source_results for handle current_item
2025-06-30 14:31:18 - WorkflowUtils - DEBUG - Path tracking - Source results structure
2025-06-30 14:31:18 - WorkflowUtils - INFO - 🔍 Handle mapping validation: partially_compatible (2/3 compatible)
2025-06-30 14:31:18 - TransitionHandler - INFO - 🔍 Handle validation: partially_compatible (2/3 compatible)
2025-06-30 14:31:18 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}
2025-06-30 14:31:18 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-30 14:31:18 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-06-30 14:31:18 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] (type: <class 'str'>)
2025-06-30 14:31:18 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-30 14:31:18 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-06-30 14:31:18 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → input_2 via path 'result': [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-06-30 14:31:18 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}
2025-06-30 14:31:18 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-06-30 14:31:18 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-06-30 14:31:18 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] (type: <class 'str'>)
2025-06-30 14:31:18 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-06-30 14:31:18 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-06-30 14:31:18 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → input_1 via path 'result': [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-06-30 14:31:18 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'final_results': []}
2025-06-30 14:31:18 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['final_results']
2025-06-30 14:31:18 - WorkflowUtils - DEBUG - Could not find result.result or result in source_results for handle current_item
2025-06-30 14:31:18 - WorkflowUtils - DEBUG - Path tracking - Source results structure
2025-06-30 14:31:18 - WorkflowUtils - WARNING - ❌ Handle mapping failed: current_item → main_input (no data found)
2025-06-30 14:31:18 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 2/3 successful
2025-06-30 14:31:18 - WorkflowUtils - WARNING - ⚠️ 1 universal handle mappings failed - this may cause tool execution errors
2025-06-30 14:31:18 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 2/3 successful
2025-06-30 14:31:18 - TransitionHandler - WARNING - ❌ Failed mapping: current_item → main_input (Error: No data found for handle current_item)
2025-06-30 14:31:18 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-30 14:31:18 - WorkflowUtils - DEBUG - Filtering out field 'input_1' with null/empty value: None
2025-06-30 14:31:18 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: None
2025-06-30 14:31:18 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-06-30 14:31:18 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-06-30 14:31:18 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-06-30 14:31:18 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-06-30 14:31:18 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-06-30 14:31:18 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-06-30 14:31:18 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-06-30 14:31:18 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-06-30 14:31:18 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 2 fields (11 null/empty fields removed)
2025-06-30 14:31:18 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-30 14:31:18 - TransitionHandler - DEBUG - 📌 Added static parameter: separator = \n
2025-06-30 14:31:18 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'input_2': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'input_1': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'num_additional_inputs': '1', 'separator': '\\n'}
2025-06-30 14:31:18 - TransitionHandler - DEBUG - tool Parameters: {'input_2': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'input_1': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'num_additional_inputs': '1', 'separator': '\\n'}
2025-06-30 14:31:18 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'input_2': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'input_1': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'num_additional_inputs': '1', 'separator': '\\n'}
2025-06-30 14:31:18 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 14, corr_id 973e3432-58ac-433a-a97a-97514ea48579):
2025-06-30 14:31:18 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 973e3432-58ac-433a-a97a-97514ea48579, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 14, 'workflow_status': 'running'}
2025-06-30 14:31:18 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 9fe2aec3-b7de-4676-b513-13cb438d3a94) using provided producer.
2025-06-30 14:31:18 - NodeExecutor - DEBUG - Added correlation_id 973e3432-58ac-433a-a97a-97514ea48579 to payload
2025-06-30 14:31:18 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-06-30 14:31:18 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'input_2': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'input_1': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'num_additional_inputs': '1', 'separator': '\\n'}, 'request_id': '9fe2aec3-b7de-4676-b513-13cb438d3a94', 'correlation_id': '973e3432-58ac-433a-a97a-97514ea48579', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-06-30 14:31:18 - NodeExecutor - DEBUG - Request 9fe2aec3-b7de-4676-b513-13cb438d3a94 sent successfully using provided producer.
2025-06-30 14:31:18 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 9fe2aec3-b7de-4676-b513-13cb438d3a94...
2025-06-30 14:31:18 - TransitionHandler - INFO - Starting parallel execution of transition: transition-MergeDataComponent-*************
2025-06-30 14:31:18 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 15, corr_id 973e3432-58ac-433a-a97a-97514ea48579):
2025-06-30 14:31:18 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 973e3432-58ac-433a-a97a-97514ea48579, response: {'result': 'Starting execution of transition: transition-MergeDataComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-MergeDataComponent-*************', 'status': 'started', 'sequence': 15, 'workflow_status': 'running'}
2025-06-30 14:31:18 - TransitionHandler - EXECUTE - Transition 'transition-MergeDataComponent-*************' (type=standard, execution_type=Components)
2025-06-30 14:31:18 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-30 14:31:18 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-MergeDataComponent-*************
2025-06-30 14:31:18 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-30 14:31:18 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-30 14:31:19 - StateManager - DEBUG - Retrieved result for transition transition-LoopNode-************* from Redis
2025-06-30 14:31:19 - StateManager - DEBUG - Extracted results for 1 tools in transition transition-LoopNode-*************
2025-06-30 14:31:19 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-30 14:31:19 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-30 14:31:19 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_results': {'final_results': []}
2025-06-30 14:31:19 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['final_results']
2025-06-30 14:31:19 - WorkflowUtils - DEBUG - Path tracking - Found handle 'final_results' directly in source_results
2025-06-30 14:31:19 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-30 14:31:19 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-30 14:31:19 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_results': {'final_results': []}
2025-06-30 14:31:19 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['final_results']
2025-06-30 14:31:19 - WorkflowUtils - DEBUG - Path tracking - Found handle 'final_results' directly in source_results
2025-06-30 14:31:19 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_results → main_input via path 'final_results': []
2025-06-30 14:31:19 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-30 14:31:19 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-30 14:31:19 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-30 14:31:19 - WorkflowUtils - DEBUG - Filtering out field 'output_key_1' with null/empty value: 
2025-06-30 14:31:19 - WorkflowUtils - DEBUG - Filtering out field 'output_key_2' with null/empty value: 
2025-06-30 14:31:19 - WorkflowUtils - DEBUG - Filtering out field 'output_key_3' with null/empty value: 
2025-06-30 14:31:19 - WorkflowUtils - DEBUG - Filtering out field 'output_key_4' with null/empty value: 
2025-06-30 14:31:19 - WorkflowUtils - DEBUG - Filtering out field 'output_key_5' with null/empty value: 
2025-06-30 14:31:19 - WorkflowUtils - DEBUG - Filtering out field 'output_key_6' with null/empty value: 
2025-06-30 14:31:19 - WorkflowUtils - DEBUG - Filtering out field 'output_key_7' with null/empty value: 
2025-06-30 14:31:19 - WorkflowUtils - DEBUG - Filtering out field 'output_key_8' with null/empty value: 
2025-06-30 14:31:19 - WorkflowUtils - DEBUG - Filtering out field 'output_key_9' with null/empty value: 
2025-06-30 14:31:19 - WorkflowUtils - DEBUG - Filtering out field 'output_key_10' with null/empty value: 
2025-06-30 14:31:19 - WorkflowUtils - DEBUG - Filtering out field 'output_key_11' with null/empty value: 
2025-06-30 14:31:19 - WorkflowUtils - DEBUG - Filtering out field 'input_1' with empty collection: {}
2025-06-30 14:31:19 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with empty collection: {}
2025-06-30 14:31:19 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with empty collection: {}
2025-06-30 14:31:19 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with empty collection: {}
2025-06-30 14:31:19 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with empty collection: {}
2025-06-30 14:31:19 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with empty collection: {}
2025-06-30 14:31:19 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with empty collection: {}
2025-06-30 14:31:19 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with empty collection: {}
2025-06-30 14:31:19 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with empty collection: {}
2025-06-30 14:31:19 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with empty collection: {}
2025-06-30 14:31:19 - WorkflowUtils - INFO - 🧹 Parameter filtering: 24 → 2 fields (22 null/empty fields removed)
2025-06-30 14:31:19 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-30 14:31:19 - TransitionHandler - DEBUG - 📌 Added static parameter: merge_strategy = Aggregate
2025-06-30 14:31:19 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'main_input': [], 'num_additional_inputs': '1', 'merge_strategy': 'Aggregate'}
2025-06-30 14:31:19 - TransitionHandler - DEBUG - tool Parameters: {'main_input': [], 'num_additional_inputs': '1', 'merge_strategy': 'Aggregate'}
2025-06-30 14:31:19 - TransitionHandler - INFO - Invoking tool 'MergeDataComponent' (tool_id: 1) for node 'MergeDataComponent' in transition 'transition-MergeDataComponent-*************' with parameters: {'main_input': [], 'num_additional_inputs': '1', 'merge_strategy': 'Aggregate'}
2025-06-30 14:31:19 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 16, corr_id 973e3432-58ac-433a-a97a-97514ea48579):
2025-06-30 14:31:19 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 973e3432-58ac-433a-a97a-97514ea48579, response: {'transition_id': 'transition-MergeDataComponent-*************', 'node_id': 'MergeDataComponent', 'tool_name': 'MergeDataComponent', 'message': 'Connecting to server', 'result': 'Connecting to server MergeDataComponent', 'status': 'connecting', 'sequence': 16, 'workflow_status': 'running'}
2025-06-30 14:31:19 - NodeExecutor - INFO - Executing tool 'MergeDataComponent' via Kafka (request_id: b0ac5238-d64d-421e-a507-a21b4f5d91b5) using provided producer.
2025-06-30 14:31:19 - NodeExecutor - DEBUG - Added correlation_id 973e3432-58ac-433a-a97a-97514ea48579 to payload
2025-06-30 14:31:19 - NodeExecutor - DEBUG - Added transition_id transition-MergeDataComponent-************* to payload
2025-06-30 14:31:19 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'MergeDataComponent', 'tool_parameters': {'main_input': [], 'num_additional_inputs': '1', 'merge_strategy': 'Aggregate'}, 'request_id': 'b0ac5238-d64d-421e-a507-a21b4f5d91b5', 'correlation_id': '973e3432-58ac-433a-a97a-97514ea48579', 'transition_id': 'transition-MergeDataComponent-*************'}
2025-06-30 14:31:19 - NodeExecutor - DEBUG - Request b0ac5238-d64d-421e-a507-a21b4f5d91b5 sent successfully using provided producer.
2025-06-30 14:31:19 - NodeExecutor - DEBUG - Waiting indefinitely for result for request b0ac5238-d64d-421e-a507-a21b4f5d91b5...
2025-06-30 14:31:24 - NodeExecutor - DEBUG - Result consumer received message: Offset=942
2025-06-30 14:31:24 - NodeExecutor - WARNING - Received error response for request_id 9fe2aec3-b7de-4676-b513-13cb438d3a94: Error combining text for request_id 9fe2aec3-b7de-4676-b513-13cb438d3a94: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-06-30 14:31:24 - NodeExecutor - DEBUG - Result consumer received message: Offset=943
2025-06-30 14:31:24 - NodeExecutor - DEBUG - Received valid result for request_id b0ac5238-d64d-421e-a507-a21b4f5d91b5
2025-06-30 14:31:24 - NodeExecutor - ERROR - Error during node execution 9fe2aec3-b7de-4676-b513-13cb438d3a94: Node execution failed: Error combining text for request_id 9fe2aec3-b7de-4676-b513-13cb438d3a94: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id 9fe2aec3-b7de-4676-b513-13cb438d3a94: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-06-30 14:31:24 - TransitionHandler - ERROR - Tool execution failed for tool 'CombineTextComponent' (tool_id: 1) in node 'CombineTextComponent' of transition 'transition-CombineTextComponent-*************': Node execution failed: Error combining text for request_id 9fe2aec3-b7de-4676-b513-13cb438d3a94: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 492, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 361, in execute_tool
    raise e
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id 9fe2aec3-b7de-4676-b513-13cb438d3a94: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"

2025-06-30 14:31:24 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 17, corr_id 973e3432-58ac-433a-a97a-97514ea48579):
2025-06-30 14:31:24 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 973e3432-58ac-433a-a97a-97514ea48579, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition faced an error during execution.', 'result': '[ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 9fe2aec3-b7de-4676-b513-13cb438d3a94: "Required field \'input_data\', \'input_datta\', or \'text_inputs\' not found in parameters"', 'status': 'failed', 'sequence': 17, 'workflow_status': 'running'}
2025-06-30 14:31:24 - TransitionHandler - ERROR - Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 9fe2aec3-b7de-4676-b513-13cb438d3a94: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-06-30 14:31:24 - NodeExecutor - INFO - Result received for request b0ac5238-d64d-421e-a507-a21b4f5d91b5.
2025-06-30 14:31:24 - TransitionHandler - INFO - Execution result from Components executor: []
2025-06-30 14:31:24 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 18, corr_id 973e3432-58ac-433a-a97a-97514ea48579):
2025-06-30 14:31:24 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 973e3432-58ac-433a-a97a-97514ea48579, response: {'transition_id': 'transition-MergeDataComponent-*************', 'node_id': 'MergeDataComponent', 'tool_name': 'MergeDataComponent', 'message': 'Transition Result received.', 'result': [], 'status': 'completed', 'sequence': 18, 'workflow_status': 'running', 'approval_required': False}
2025-06-30 14:31:24 - StateManager - DEBUG - Stored result for transition transition-MergeDataComponent-************* in memory: {'MergeDataComponent': {'transition_id': 'transition-MergeDataComponent-*************', 'node_id': 'MergeDataComponent', 'tool_name': 'MergeDataComponent', 'result': {'result': []}, 'status': 'completed', 'timestamp': 1751274084.6260521}}
2025-06-30 14:31:25 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-MergeDataComponent-*************'
2025-06-30 14:31:25 - RedisManager - DEBUG - Set key 'result:transition-MergeDataComponent-*************' with TTL of 300 seconds
2025-06-30 14:31:25 - StateManager - DEBUG - Stored result for transition transition-MergeDataComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-30 14:31:25 - StateManager - INFO - Marked transition transition-MergeDataComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-30 14:31:25 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'backup_transition-LoopNode-*************_iteration_0', 'loop_iteration_transition-LoopNode-*************_5', 'backup_transition-LoopNode-*************_iteration_3', 'backup_transition-LoopNode-*************_iteration_2', 'loop_iteration_4', 'loop_iteration_transition-LoopNode-*************_1', 'transition-AgenticAI-*************', 'backup_transition-LoopNode-*************_iteration_4', 'loop_iteration_transition-LoopNode-*************_0', 'loop_iteration_transition-LoopNode-*************_2', 'transition-AgenticAI-175**********', 'transition-LoopNode-*************', 'current_iteration', 'loop_iteration_2', 'loop_iteration_5', 'loop_iteration_3', 'backup_transition-LoopNode-*************_iteration_1', 'loop_iteration_transition-LoopNode-*************_4', 'backup_transition-LoopNode-*************_iteration_5', 'loop_iteration_0', 'loop_iteration_1', 'transition-MergeDataComponent-*************', 'loop_iteration_transition-LoopNode-*************_3'}
2025-06-30 14:31:25 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-MergeDataComponent-*************
2025-06-30 14:31:25 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'list'>
2025-06-30 14:31:25 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-30 14:31:25 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'list'>
2025-06-30 14:31:25 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-MergeDataComponent-*************:
2025-06-30 14:31:25 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-30 14:31:25 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-30 14:31:25 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-MergeDataComponent-*************, returning empty list
2025-06-30 14:31:25 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-MergeDataComponent-*************
2025-06-30 14:31:25 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 0
2025-06-30 14:31:25 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: []
2025-06-30 14:31:25 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-06-30 14:31:25 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-06-30 14:31:25 - TransitionHandler - DEBUG - 🔗 Final next_transitions: []
2025-06-30 14:31:25 - TransitionHandler - INFO - Completed transition transition-MergeDataComponent-************* in 6.93 seconds
2025-06-30 14:31:25 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 19, corr_id 973e3432-58ac-433a-a97a-97514ea48579):
2025-06-30 14:31:25 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 973e3432-58ac-433a-a97a-97514ea48579, response: {'result': 'Completed transition in 6.93 seconds', 'message': 'Transition completed in 6.93 seconds', 'transition_id': 'transition-MergeDataComponent-*************', 'status': 'time_logged', 'sequence': 19, 'workflow_status': 'running'}
2025-06-30 14:31:25 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-MergeDataComponent-*************: []
2025-06-30 14:31:25 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 0
2025-06-30 14:31:25 - EnhancedWorkflowEngine - DEBUG - Results: [Exception('Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 9fe2aec3-b7de-4676-b513-13cb438d3a94: "Required field \'input_data\', \'input_datta\', or \'text_inputs\' not found in parameters"'), []]
2025-06-30 14:31:25 - EnhancedWorkflowEngine - DEBUG - 🔄 Orchestration engine received result for transition-CombineTextComponent-*************: Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 9fe2aec3-b7de-4676-b513-13cb438d3a94: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-06-30 14:31:25 - EnhancedWorkflowEngine - DEBUG - 🔄 Result type: <class 'Exception'>, is_list: False
2025-06-30 14:31:25 - EnhancedWorkflowEngine - ERROR - Error in execution of transition transition-CombineTextComponent-*************: Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 9fe2aec3-b7de-4676-b513-13cb438d3a94: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-06-30 14:31:25 - EnhancedWorkflowEngine - ERROR - Traceback for transition transition-CombineTextComponent-*************: NoneType: None

2025-06-30 14:31:25 - EnhancedWorkflowEngine - ERROR - An unexpected error occurred during parallel execution of standard transitions: Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 9fe2aec3-b7de-4676-b513-13cb438d3a94: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-06-30 14:31:25 - EnhancedWorkflowEngine - ERROR - Traceback for unexpected error: Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 492, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 361, in execute_tool
    raise e
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id 9fe2aec3-b7de-4676-b513-13cb438d3a94: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 204, in _execute_transition_with_tracking
    result = await self._execute_standard_or_reflection_transition(transition)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 703, in _execute_standard_or_reflection_transition
    raise Exception(f"Tool execution error: {error_message}")
Exception: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 9fe2aec3-b7de-4676-b513-13cb438d3a94: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/executor_core.py", line 267, in execute
    raise result
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 230, in _execute_transition_with_tracking
    raise Exception(f"Exception in transition {transition_id}: {str(e)}")
Exception: Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 9fe2aec3-b7de-4676-b513-13cb438d3a94: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"

2025-06-30 14:31:25 - EnhancedWorkflowEngine - ERROR - An unexpected error occurred during workflow execution: Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 9fe2aec3-b7de-4676-b513-13cb438d3a94: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-06-30 14:31:25 - EnhancedWorkflowEngine - ERROR - Traceback for unexpected error: Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 492, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 361, in execute_tool
    raise e
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id 9fe2aec3-b7de-4676-b513-13cb438d3a94: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 204, in _execute_transition_with_tracking
    result = await self._execute_standard_or_reflection_transition(transition)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 703, in _execute_standard_or_reflection_transition
    raise Exception(f"Tool execution error: {error_message}")
Exception: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 9fe2aec3-b7de-4676-b513-13cb438d3a94: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/executor_core.py", line 285, in execute
    raise e
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/executor_core.py", line 267, in execute
    raise result
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 230, in _execute_transition_with_tracking
    raise Exception(f"Exception in transition {transition_id}: {str(e)}")
Exception: Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 9fe2aec3-b7de-4676-b513-13cb438d3a94: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"

2025-06-30 14:31:25 - KafkaWorkflowConsumer - ERROR - Exception in workflow execution: Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 9fe2aec3-b7de-4676-b513-13cb438d3a94: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-06-30 14:31:25 - KafkaWorkflowConsumer - INFO - Workflow '0c19c070-905e-46ef-9f57-62eb427bf396' final status: failed, result: Exception in workflow '0c19c070-905e-46ef-9f57-62eb427bf396': Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 9fe2aec3-b7de-4676-b513-13cb438d3a94: "Required field 'input_data', 'input_datta', or 'text_inputs' not found in parameters"
2025-06-30 14:31:25 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 973e3432-58ac-433a-a97a-97514ea48579, response: {'status': 'failed', 'result': 'Exception in workflow \'0c19c070-905e-46ef-9f57-62eb427bf396\': Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 9fe2aec3-b7de-4676-b513-13cb438d3a94: "Required field \'input_data\', \'input_datta\', or \'text_inputs\' not found in parameters"', 'workflow_status': 'failed', 'error': 'Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 9fe2aec3-b7de-4676-b513-13cb438d3a94: "Required field \'input_data\', \'input_datta\', or \'text_inputs\' not found in parameters"', 'error_type': 'Exception'}
2025-06-30 14:31:25 - KafkaWorkflowConsumer - DEBUG - Stopped workflow with correlation_id: 973e3432-58ac-433a-a97a-97514ea48579 
2025-06-30 14:31:59 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-30 14:31:59 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 14:31:59 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 14:31:59 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-30 14:32:59 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-30 14:32:59 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 14:32:59 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 14:32:59 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-30 14:33:59 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-30 14:33:59 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 14:33:59 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 14:33:59 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-30 14:34:59 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-30 14:34:59 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 14:34:59 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 14:34:59 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-30 14:35:51 - RedisEventListener - DEBUG - Raw results DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@5__:*', 'channel': b'__keyspace@5__:result:transition-AgenticAI-175**********', 'data': b'expired'}
2025-06-30 14:35:51 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@5__:result:transition-AgenticAI-175**********'
2025-06-30 14:35:51 - RedisEventListener - DEBUG - Decoded channel: __keyspace@5__:result:transition-AgenticAI-175**********
2025-06-30 14:35:51 - RedisEventListener - DEBUG - Extracted key: result:transition-AgenticAI-175**********
2025-06-30 14:35:51 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-06-30 14:35:51 - RedisEventListener - DEBUG - Decoded event: expired
2025-06-30 14:35:51 - RedisEventListener - INFO - Detected expired event for result of transition: transition-AgenticAI-175**********
2025-06-30 14:35:51 - RedisEventListener - INFO - Archiving result for transition: transition-AgenticAI-175**********
2025-06-30 14:35:51 - StateManager - DEBUG - Attempting to archive result for transition transition-AgenticAI-175**********
2025-06-30 14:35:52 - StateManager - DEBUG - Provided result: False
2025-06-30 14:35:52 - StateManager - DEBUG - Trying to get result from Redis for transition transition-AgenticAI-175**********
2025-06-30 14:35:53 - StateManager - DEBUG - No result found in Redis for transition transition-AgenticAI-175**********
2025-06-30 14:35:53 - StateManager - DEBUG - Trying to get result from memory for transition transition-AgenticAI-175**********
2025-06-30 14:35:53 - StateManager - DEBUG - Found result in memory for transition transition-AgenticAI-175**********
2025-06-30 14:35:53 - StateManager - DEBUG - Archiving result to PostgreSQL for transition transition-AgenticAI-175**********
2025-06-30 14:35:53 - PostgresManager - DEBUG - Attempting to store transition result for transition-AgenticAI-175********** in correlation 973e3432-58ac-433a-a97a-97514ea48579
2025-06-30 14:35:53 - PostgresManager - DEBUG - Result data type: <class 'dict'>
2025-06-30 14:35:53 - PostgresManager - DEBUG - Result data: {'AgenticAI': {'transition_id': 'transition-AgenticAI-175**********', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'result': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}, 'status': 'completed', 'timestamp': 1751274050.2503839}}
2025-06-30 14:35:57 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-30 14:35:57 - PostgresManager - DEBUG - Successfully serialized result data to JSON string
2025-06-30 14:35:57 - PostgresManager - DEBUG - Converted to psycopg2 Json object
2025-06-30 14:35:57 - PostgresManager - DEBUG - Inserting new record for transition transition-AgenticAI-175**********
2025-06-30 14:35:58 - PostgresManager - DEBUG - Inserted new result for transition transition-AgenticAI-175********** in correlation 973e3432-58ac-433a-a97a-97514ea48579
2025-06-30 14:35:58 - PostgresManager - DEBUG - Successfully stored transition result for transition-AgenticAI-175**********
2025-06-30 14:35:58 - StateManager - INFO - Archived result for transition transition-AgenticAI-175********** to PostgreSQL
2025-06-30 14:35:58 - RedisEventListener - DEBUG - Raw results DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@5__:*', 'channel': b'__keyspace@5__:result:transition-AgenticAI-*************', 'data': b'expired'}
2025-06-30 14:35:58 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@5__:result:transition-AgenticAI-*************'
2025-06-30 14:35:58 - RedisEventListener - DEBUG - Decoded channel: __keyspace@5__:result:transition-AgenticAI-*************
2025-06-30 14:35:58 - RedisEventListener - DEBUG - Extracted key: result:transition-AgenticAI-*************
2025-06-30 14:35:58 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-06-30 14:35:58 - RedisEventListener - DEBUG - Decoded event: expired
2025-06-30 14:35:58 - RedisEventListener - INFO - Detected expired event for result of transition: transition-AgenticAI-*************
2025-06-30 14:35:58 - RedisEventListener - INFO - Archiving result for transition: transition-AgenticAI-*************
2025-06-30 14:35:58 - StateManager - DEBUG - Attempting to archive result for transition transition-AgenticAI-*************
2025-06-30 14:35:59 - StateManager - DEBUG - Provided result: False
2025-06-30 14:35:59 - StateManager - DEBUG - Trying to get result from Redis for transition transition-AgenticAI-*************
2025-06-30 14:36:00 - StateManager - DEBUG - No result found in Redis for transition transition-AgenticAI-*************
2025-06-30 14:36:00 - StateManager - DEBUG - Trying to get result from memory for transition transition-AgenticAI-*************
2025-06-30 14:36:00 - StateManager - DEBUG - Found result in memory for transition transition-AgenticAI-*************
2025-06-30 14:36:00 - StateManager - DEBUG - Archiving result to PostgreSQL for transition transition-AgenticAI-*************
2025-06-30 14:36:00 - PostgresManager - DEBUG - Attempting to store transition result for transition-AgenticAI-************* in correlation 973e3432-58ac-433a-a97a-97514ea48579
2025-06-30 14:36:00 - PostgresManager - DEBUG - Result data type: <class 'dict'>
2025-06-30 14:36:00 - PostgresManager - DEBUG - Result data: {'AgenticAI': {'transition_id': 'transition-AgenticAI-*************', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'result': {'result': '0'}, 'status': 'completed', 'timestamp': 1751274054.0113}}
2025-06-30 14:36:03 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-30 14:36:03 - PostgresManager - DEBUG - Successfully serialized result data to JSON string
2025-06-30 14:36:03 - PostgresManager - DEBUG - Converted to psycopg2 Json object
2025-06-30 14:36:03 - PostgresManager - DEBUG - Inserting new record for transition transition-AgenticAI-*************
2025-06-30 14:36:04 - PostgresManager - DEBUG - Inserted new result for transition transition-AgenticAI-************* in correlation 973e3432-58ac-433a-a97a-97514ea48579
2025-06-30 14:36:04 - PostgresManager - DEBUG - Successfully stored transition result for transition-AgenticAI-*************
2025-06-30 14:36:04 - StateManager - INFO - Archived result for transition transition-AgenticAI-************* to PostgreSQL
2025-06-30 14:36:04 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-30 14:36:04 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pmessage', b'__keyspace@5__:*', b'__keyspace@5__:result:backup_transition-LoopNode-*************_iteration_0', b'expired']
2025-06-30 14:36:06 - RedisEventListener - WARNING - Failed to send keep-alive PING to Redis: Bad response from PING health check
2025-06-30 14:36:06 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=False
2025-06-30 14:36:06 - RedisEventListener - DEBUG - Raw results DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@5__:*', 'channel': b'__keyspace@5__:result:backup_transition-LoopNode-*************_iteration_1', 'data': b'expired'}
2025-06-30 14:36:06 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@5__:result:backup_transition-LoopNode-*************_iteration_1'
2025-06-30 14:36:06 - RedisEventListener - DEBUG - Decoded channel: __keyspace@5__:result:backup_transition-LoopNode-*************_iteration_1
2025-06-30 14:36:06 - RedisEventListener - DEBUG - Extracted key: result:backup_transition-LoopNode-*************_iteration_1
2025-06-30 14:36:06 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-06-30 14:36:06 - RedisEventListener - DEBUG - Decoded event: expired
2025-06-30 14:36:06 - RedisEventListener - INFO - Detected expired event for result of transition: backup_transition-LoopNode-*************_iteration_1
2025-06-30 14:36:06 - RedisEventListener - INFO - Archiving result for transition: backup_transition-LoopNode-*************_iteration_1
2025-06-30 14:36:06 - StateManager - DEBUG - Attempting to archive result for transition backup_transition-LoopNode-*************_iteration_1
2025-06-30 14:36:07 - StateManager - DEBUG - Provided result: False
2025-06-30 14:36:07 - StateManager - DEBUG - Trying to get result from Redis for transition backup_transition-LoopNode-*************_iteration_1
2025-06-30 14:36:07 - StateManager - DEBUG - No result found in Redis for transition backup_transition-LoopNode-*************_iteration_1
2025-06-30 14:36:07 - StateManager - DEBUG - Trying to get result from memory for transition backup_transition-LoopNode-*************_iteration_1
2025-06-30 14:36:07 - StateManager - DEBUG - Found result in memory for transition backup_transition-LoopNode-*************_iteration_1
2025-06-30 14:36:07 - StateManager - DEBUG - Archiving result to PostgreSQL for transition backup_transition-LoopNode-*************_iteration_1
2025-06-30 14:36:07 - PostgresManager - DEBUG - Attempting to store transition result for backup_transition-LoopNode-*************_iteration_1 in correlation 973e3432-58ac-433a-a97a-97514ea48579
2025-06-30 14:36:07 - PostgresManager - DEBUG - Result data type: <class 'dict'>
2025-06-30 14:36:07 - PostgresManager - DEBUG - Result data: {'current_item': 2, 'iteration_index': 1, 'iteration_metadata': {'timestamp': 311149.024439916, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-06-30 14:36:11 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-30 14:36:11 - PostgresManager - DEBUG - Successfully serialized result data to JSON string
2025-06-30 14:36:11 - PostgresManager - DEBUG - Converted to psycopg2 Json object
2025-06-30 14:36:11 - PostgresManager - DEBUG - Inserting new record for transition backup_transition-LoopNode-*************_iteration_1
2025-06-30 14:36:12 - PostgresManager - DEBUG - Inserted new result for transition backup_transition-LoopNode-*************_iteration_1 in correlation 973e3432-58ac-433a-a97a-97514ea48579
2025-06-30 14:36:12 - PostgresManager - DEBUG - Successfully stored transition result for backup_transition-LoopNode-*************_iteration_1
2025-06-30 14:36:12 - StateManager - INFO - Archived result for transition backup_transition-LoopNode-*************_iteration_1 to PostgreSQL
2025-06-30 14:36:13 - RedisEventListener - DEBUG - Raw results DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@5__:*', 'channel': b'__keyspace@5__:result:backup_transition-LoopNode-*************_iteration_2', 'data': b'expired'}
2025-06-30 14:36:13 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@5__:result:backup_transition-LoopNode-*************_iteration_2'
2025-06-30 14:36:13 - RedisEventListener - DEBUG - Decoded channel: __keyspace@5__:result:backup_transition-LoopNode-*************_iteration_2
2025-06-30 14:36:13 - RedisEventListener - DEBUG - Extracted key: result:backup_transition-LoopNode-*************_iteration_2
2025-06-30 14:36:13 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-06-30 14:36:13 - RedisEventListener - DEBUG - Decoded event: expired
2025-06-30 14:36:13 - RedisEventListener - INFO - Detected expired event for result of transition: backup_transition-LoopNode-*************_iteration_2
2025-06-30 14:36:13 - RedisEventListener - INFO - Archiving result for transition: backup_transition-LoopNode-*************_iteration_2
2025-06-30 14:36:13 - StateManager - DEBUG - Attempting to archive result for transition backup_transition-LoopNode-*************_iteration_2
2025-06-30 14:36:14 - StateManager - DEBUG - Provided result: False
2025-06-30 14:36:14 - StateManager - DEBUG - Trying to get result from Redis for transition backup_transition-LoopNode-*************_iteration_2
2025-06-30 14:36:15 - StateManager - DEBUG - No result found in Redis for transition backup_transition-LoopNode-*************_iteration_2
2025-06-30 14:36:15 - StateManager - DEBUG - Trying to get result from memory for transition backup_transition-LoopNode-*************_iteration_2
2025-06-30 14:36:15 - StateManager - DEBUG - Found result in memory for transition backup_transition-LoopNode-*************_iteration_2
2025-06-30 14:36:15 - StateManager - DEBUG - Archiving result to PostgreSQL for transition backup_transition-LoopNode-*************_iteration_2
2025-06-30 14:36:15 - PostgresManager - DEBUG - Attempting to store transition result for backup_transition-LoopNode-*************_iteration_2 in correlation 973e3432-58ac-433a-a97a-97514ea48579
2025-06-30 14:36:15 - PostgresManager - DEBUG - Result data type: <class 'dict'>
2025-06-30 14:36:15 - PostgresManager - DEBUG - Result data: {'current_item': 3, 'iteration_index': 2, 'iteration_metadata': {'timestamp': 311152.061019, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-06-30 14:36:18 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-30 14:36:19 - PostgresManager - DEBUG - Successfully serialized result data to JSON string
2025-06-30 14:36:19 - PostgresManager - DEBUG - Converted to psycopg2 Json object
2025-06-30 14:36:19 - PostgresManager - DEBUG - Inserting new record for transition backup_transition-LoopNode-*************_iteration_2
2025-06-30 14:36:19 - PostgresManager - DEBUG - Inserted new result for transition backup_transition-LoopNode-*************_iteration_2 in correlation 973e3432-58ac-433a-a97a-97514ea48579
2025-06-30 14:36:20 - PostgresManager - DEBUG - Successfully stored transition result for backup_transition-LoopNode-*************_iteration_2
2025-06-30 14:36:20 - StateManager - INFO - Archived result for transition backup_transition-LoopNode-*************_iteration_2 to PostgreSQL
2025-06-30 14:36:20 - RedisEventListener - DEBUG - Raw results DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@5__:*', 'channel': b'__keyspace@5__:result:backup_transition-LoopNode-*************_iteration_3', 'data': b'expired'}
2025-06-30 14:36:20 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@5__:result:backup_transition-LoopNode-*************_iteration_3'
2025-06-30 14:36:20 - RedisEventListener - DEBUG - Decoded channel: __keyspace@5__:result:backup_transition-LoopNode-*************_iteration_3
2025-06-30 14:36:20 - RedisEventListener - DEBUG - Extracted key: result:backup_transition-LoopNode-*************_iteration_3
2025-06-30 14:36:20 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-06-30 14:36:20 - RedisEventListener - DEBUG - Decoded event: expired
2025-06-30 14:36:20 - RedisEventListener - INFO - Detected expired event for result of transition: backup_transition-LoopNode-*************_iteration_3
2025-06-30 14:36:20 - RedisEventListener - INFO - Archiving result for transition: backup_transition-LoopNode-*************_iteration_3
2025-06-30 14:36:20 - StateManager - DEBUG - Attempting to archive result for transition backup_transition-LoopNode-*************_iteration_3
2025-06-30 14:36:20 - StateManager - DEBUG - Provided result: False
2025-06-30 14:36:21 - StateManager - DEBUG - Trying to get result from Redis for transition backup_transition-LoopNode-*************_iteration_3
2025-06-30 14:36:21 - StateManager - DEBUG - No result found in Redis for transition backup_transition-LoopNode-*************_iteration_3
2025-06-30 14:36:21 - StateManager - DEBUG - Trying to get result from memory for transition backup_transition-LoopNode-*************_iteration_3
2025-06-30 14:36:21 - StateManager - DEBUG - Found result in memory for transition backup_transition-LoopNode-*************_iteration_3
2025-06-30 14:36:21 - StateManager - DEBUG - Archiving result to PostgreSQL for transition backup_transition-LoopNode-*************_iteration_3
2025-06-30 14:36:21 - PostgresManager - DEBUG - Attempting to store transition result for backup_transition-LoopNode-*************_iteration_3 in correlation 973e3432-58ac-433a-a97a-97514ea48579
2025-06-30 14:36:21 - PostgresManager - DEBUG - Result data type: <class 'dict'>
2025-06-30 14:36:21 - PostgresManager - DEBUG - Result data: {'current_item': 4, 'iteration_index': 3, 'iteration_metadata': {'timestamp': 311155.097497, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-06-30 14:36:25 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-30 14:36:26 - PostgresManager - DEBUG - Successfully serialized result data to JSON string
2025-06-30 14:36:26 - PostgresManager - DEBUG - Converted to psycopg2 Json object
2025-06-30 14:36:26 - PostgresManager - DEBUG - Inserting new record for transition backup_transition-LoopNode-*************_iteration_3
2025-06-30 14:36:26 - PostgresManager - DEBUG - Inserted new result for transition backup_transition-LoopNode-*************_iteration_3 in correlation 973e3432-58ac-433a-a97a-97514ea48579
2025-06-30 14:36:26 - PostgresManager - DEBUG - Successfully stored transition result for backup_transition-LoopNode-*************_iteration_3
2025-06-30 14:36:26 - StateManager - INFO - Archived result for transition backup_transition-LoopNode-*************_iteration_3 to PostgreSQL
2025-06-30 14:36:26 - RedisEventListener - DEBUG - Raw results DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@5__:*', 'channel': b'__keyspace@5__:result:backup_transition-LoopNode-*************_iteration_4', 'data': b'expired'}
2025-06-30 14:36:26 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@5__:result:backup_transition-LoopNode-*************_iteration_4'
2025-06-30 14:36:26 - RedisEventListener - DEBUG - Decoded channel: __keyspace@5__:result:backup_transition-LoopNode-*************_iteration_4
2025-06-30 14:36:26 - RedisEventListener - DEBUG - Extracted key: result:backup_transition-LoopNode-*************_iteration_4
2025-06-30 14:36:26 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-06-30 14:36:26 - RedisEventListener - DEBUG - Decoded event: expired
2025-06-30 14:36:26 - RedisEventListener - INFO - Detected expired event for result of transition: backup_transition-LoopNode-*************_iteration_4
2025-06-30 14:36:26 - RedisEventListener - INFO - Archiving result for transition: backup_transition-LoopNode-*************_iteration_4
2025-06-30 14:36:26 - StateManager - DEBUG - Attempting to archive result for transition backup_transition-LoopNode-*************_iteration_4
2025-06-30 14:36:27 - StateManager - DEBUG - Provided result: False
2025-06-30 14:36:27 - StateManager - DEBUG - Trying to get result from Redis for transition backup_transition-LoopNode-*************_iteration_4
2025-06-30 14:36:28 - StateManager - DEBUG - No result found in Redis for transition backup_transition-LoopNode-*************_iteration_4
2025-06-30 14:36:28 - StateManager - DEBUG - Trying to get result from memory for transition backup_transition-LoopNode-*************_iteration_4
2025-06-30 14:36:28 - StateManager - DEBUG - Found result in memory for transition backup_transition-LoopNode-*************_iteration_4
2025-06-30 14:36:28 - StateManager - DEBUG - Archiving result to PostgreSQL for transition backup_transition-LoopNode-*************_iteration_4
2025-06-30 14:36:28 - PostgresManager - DEBUG - Attempting to store transition result for backup_transition-LoopNode-*************_iteration_4 in correlation 973e3432-58ac-433a-a97a-97514ea48579
2025-06-30 14:36:28 - PostgresManager - DEBUG - Result data type: <class 'dict'>
2025-06-30 14:36:28 - PostgresManager - DEBUG - Result data: {'current_item': 5, 'iteration_index': 4, 'iteration_metadata': {'timestamp': 311158.118851291, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-06-30 14:36:31 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-30 14:36:32 - PostgresManager - DEBUG - Successfully serialized result data to JSON string
2025-06-30 14:36:32 - PostgresManager - DEBUG - Converted to psycopg2 Json object
2025-06-30 14:36:32 - PostgresManager - DEBUG - Inserting new record for transition backup_transition-LoopNode-*************_iteration_4
2025-06-30 14:36:32 - PostgresManager - DEBUG - Inserted new result for transition backup_transition-LoopNode-*************_iteration_4 in correlation 973e3432-58ac-433a-a97a-97514ea48579
2025-06-30 14:36:33 - PostgresManager - DEBUG - Successfully stored transition result for backup_transition-LoopNode-*************_iteration_4
2025-06-30 14:36:33 - StateManager - INFO - Archived result for transition backup_transition-LoopNode-*************_iteration_4 to PostgreSQL
2025-06-30 14:36:33 - RedisEventListener - DEBUG - Raw results DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@5__:*', 'channel': b'__keyspace@5__:result:backup_transition-LoopNode-*************_iteration_5', 'data': b'expired'}
2025-06-30 14:36:33 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@5__:result:backup_transition-LoopNode-*************_iteration_5'
2025-06-30 14:36:33 - RedisEventListener - DEBUG - Decoded channel: __keyspace@5__:result:backup_transition-LoopNode-*************_iteration_5
2025-06-30 14:36:33 - RedisEventListener - DEBUG - Extracted key: result:backup_transition-LoopNode-*************_iteration_5
2025-06-30 14:36:33 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-06-30 14:36:33 - RedisEventListener - DEBUG - Decoded event: expired
2025-06-30 14:36:33 - RedisEventListener - INFO - Detected expired event for result of transition: backup_transition-LoopNode-*************_iteration_5
2025-06-30 14:36:33 - RedisEventListener - INFO - Archiving result for transition: backup_transition-LoopNode-*************_iteration_5
2025-06-30 14:36:33 - StateManager - DEBUG - Attempting to archive result for transition backup_transition-LoopNode-*************_iteration_5
2025-06-30 14:36:34 - StateManager - DEBUG - Provided result: False
2025-06-30 14:36:34 - StateManager - DEBUG - Trying to get result from Redis for transition backup_transition-LoopNode-*************_iteration_5
2025-06-30 14:36:34 - StateManager - DEBUG - No result found in Redis for transition backup_transition-LoopNode-*************_iteration_5
2025-06-30 14:36:34 - StateManager - DEBUG - Trying to get result from memory for transition backup_transition-LoopNode-*************_iteration_5
2025-06-30 14:36:34 - StateManager - DEBUG - Found result in memory for transition backup_transition-LoopNode-*************_iteration_5
2025-06-30 14:36:34 - StateManager - DEBUG - Archiving result to PostgreSQL for transition backup_transition-LoopNode-*************_iteration_5
2025-06-30 14:36:34 - PostgresManager - DEBUG - Attempting to store transition result for backup_transition-LoopNode-*************_iteration_5 in correlation 973e3432-58ac-433a-a97a-97514ea48579
2025-06-30 14:36:34 - PostgresManager - DEBUG - Result data type: <class 'dict'>
2025-06-30 14:36:34 - PostgresManager - DEBUG - Result data: {'current_item': 6, 'iteration_index': 5, 'iteration_metadata': {'timestamp': 311161.157600083, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-06-30 14:36:38 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-30 14:36:38 - PostgresManager - DEBUG - Successfully serialized result data to JSON string
2025-06-30 14:36:38 - PostgresManager - DEBUG - Converted to psycopg2 Json object
2025-06-30 14:36:38 - PostgresManager - DEBUG - Inserting new record for transition backup_transition-LoopNode-*************_iteration_5
2025-06-30 14:36:39 - PostgresManager - DEBUG - Inserted new result for transition backup_transition-LoopNode-*************_iteration_5 in correlation 973e3432-58ac-433a-a97a-97514ea48579
2025-06-30 14:36:39 - PostgresManager - DEBUG - Successfully stored transition result for backup_transition-LoopNode-*************_iteration_5
2025-06-30 14:36:39 - StateManager - INFO - Archived result for transition backup_transition-LoopNode-*************_iteration_5 to PostgreSQL
2025-06-30 14:36:39 - RedisEventListener - DEBUG - Raw results DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@5__:*', 'channel': b'__keyspace@5__:result:transition-LoopNode-*************', 'data': b'expired'}
2025-06-30 14:36:39 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@5__:result:transition-LoopNode-*************'
2025-06-30 14:36:39 - RedisEventListener - DEBUG - Decoded channel: __keyspace@5__:result:transition-LoopNode-*************
2025-06-30 14:36:39 - RedisEventListener - DEBUG - Extracted key: result:transition-LoopNode-*************
2025-06-30 14:36:39 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-06-30 14:36:39 - RedisEventListener - DEBUG - Decoded event: expired
2025-06-30 14:36:39 - RedisEventListener - INFO - Detected expired event for result of transition: transition-LoopNode-*************
2025-06-30 14:36:39 - RedisEventListener - INFO - Archiving result for transition: transition-LoopNode-*************
2025-06-30 14:36:39 - StateManager - DEBUG - Attempting to archive result for transition transition-LoopNode-*************
2025-06-30 14:36:40 - StateManager - DEBUG - Provided result: False
2025-06-30 14:36:40 - StateManager - DEBUG - Trying to get result from Redis for transition transition-LoopNode-*************
2025-06-30 14:36:41 - StateManager - DEBUG - No result found in Redis for transition transition-LoopNode-*************
2025-06-30 14:36:41 - StateManager - DEBUG - Trying to get result from memory for transition transition-LoopNode-*************
2025-06-30 14:36:41 - StateManager - DEBUG - Found result in memory for transition transition-LoopNode-*************
2025-06-30 14:36:41 - StateManager - DEBUG - Archiving result to PostgreSQL for transition transition-LoopNode-*************
2025-06-30 14:36:41 - PostgresManager - DEBUG - Attempting to store transition result for transition-LoopNode-************* in correlation 973e3432-58ac-433a-a97a-97514ea48579
2025-06-30 14:36:41 - PostgresManager - DEBUG - Result data type: <class 'dict'>
2025-06-30 14:36:41 - PostgresManager - DEBUG - Result data: {'final_results': []}
2025-06-30 14:36:44 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-30 14:36:45 - PostgresManager - DEBUG - Successfully serialized result data to JSON string
2025-06-30 14:36:45 - PostgresManager - DEBUG - Converted to psycopg2 Json object
2025-06-30 14:36:45 - PostgresManager - DEBUG - Inserting new record for transition transition-LoopNode-*************
2025-06-30 14:36:45 - PostgresManager - DEBUG - Inserted new result for transition transition-LoopNode-************* in correlation 973e3432-58ac-433a-a97a-97514ea48579
2025-06-30 14:36:45 - PostgresManager - DEBUG - Successfully stored transition result for transition-LoopNode-*************
2025-06-30 14:36:45 - StateManager - INFO - Archived result for transition transition-LoopNode-************* to PostgreSQL
2025-06-30 14:36:45 - RedisEventListener - DEBUG - Raw results DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@5__:*', 'channel': b'__keyspace@5__:result:transition-MergeDataComponent-*************', 'data': b'expired'}
2025-06-30 14:36:45 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@5__:result:transition-MergeDataComponent-*************'
2025-06-30 14:36:45 - RedisEventListener - DEBUG - Decoded channel: __keyspace@5__:result:transition-MergeDataComponent-*************
2025-06-30 14:36:45 - RedisEventListener - DEBUG - Extracted key: result:transition-MergeDataComponent-*************
2025-06-30 14:36:45 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-06-30 14:36:45 - RedisEventListener - DEBUG - Decoded event: expired
2025-06-30 14:36:45 - RedisEventListener - INFO - Detected expired event for result of transition: transition-MergeDataComponent-*************
2025-06-30 14:36:45 - RedisEventListener - INFO - Archiving result for transition: transition-MergeDataComponent-*************
2025-06-30 14:36:45 - StateManager - DEBUG - Attempting to archive result for transition transition-MergeDataComponent-*************
2025-06-30 14:36:46 - StateManager - DEBUG - Provided result: False
2025-06-30 14:36:46 - StateManager - DEBUG - Trying to get result from Redis for transition transition-MergeDataComponent-*************
2025-06-30 14:36:47 - StateManager - DEBUG - No result found in Redis for transition transition-MergeDataComponent-*************
2025-06-30 14:36:47 - StateManager - DEBUG - Trying to get result from memory for transition transition-MergeDataComponent-*************
2025-06-30 14:36:47 - StateManager - DEBUG - Found result in memory for transition transition-MergeDataComponent-*************
2025-06-30 14:36:47 - StateManager - DEBUG - Archiving result to PostgreSQL for transition transition-MergeDataComponent-*************
2025-06-30 14:36:47 - PostgresManager - DEBUG - Attempting to store transition result for transition-MergeDataComponent-************* in correlation 973e3432-58ac-433a-a97a-97514ea48579
2025-06-30 14:36:47 - PostgresManager - DEBUG - Result data type: <class 'dict'>
2025-06-30 14:36:47 - PostgresManager - DEBUG - Result data: {'MergeDataComponent': {'transition_id': 'transition-MergeDataComponent-*************', 'node_id': 'MergeDataComponent', 'tool_name': 'MergeDataComponent', 'result': {'result': []}, 'status': 'completed', 'timestamp': 1751274084.6260521}}
2025-06-30 14:36:50 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-30 14:36:51 - PostgresManager - DEBUG - Successfully serialized result data to JSON string
2025-06-30 14:36:51 - PostgresManager - DEBUG - Converted to psycopg2 Json object
2025-06-30 14:36:51 - PostgresManager - DEBUG - Inserting new record for transition transition-MergeDataComponent-*************
2025-06-30 14:36:51 - PostgresManager - DEBUG - Inserted new result for transition transition-MergeDataComponent-************* in correlation 973e3432-58ac-433a-a97a-97514ea48579
2025-06-30 14:36:51 - PostgresManager - DEBUG - Successfully stored transition result for transition-MergeDataComponent-*************
2025-06-30 14:36:51 - StateManager - INFO - Archived result for transition transition-MergeDataComponent-************* to PostgreSQL
2025-06-30 14:37:04 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-30 14:37:04 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 14:37:05 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-30 14:37:05 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-30 14:37:14 - KafkaWorkflowConsumer - INFO - Consumer task cancelled.
2025-06-30 14:37:14 - MCPToolExecutor - INFO - Result consumer loop cancelled.
2025-06-30 14:37:14 - AgentExecutor - INFO - Result consumer loop cancelled.
2025-06-30 14:37:14 - NodeExecutor - INFO - Result consumer loop cancelled.
2025-06-30 14:37:14 - Main - ERROR - Shutting down due to keyboard interrupt...
