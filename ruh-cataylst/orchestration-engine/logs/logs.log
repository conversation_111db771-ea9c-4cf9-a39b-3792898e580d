2025-07-01 10:07:35 - Main - INFO - Starting Server
2025-07-01 10:07:35 - Main - INFO - Connection at: **************:9092
2025-07-01 10:07:35 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-07-01 10:07:35 - NodeExecutor - INFO - NodeExecutor initialized.
2025-07-01 10:07:35 - AgentExecutor - INFO - AgentExecutor initialized.
2025-07-01 10:07:35 - KafkaWorkflowConsumer - INFO - Initializing database connections...
2025-07-01 10:07:35 - Red<PERSON><PERSON>anager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-07-01 10:07:37 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-07-01 10:07:37 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-07-01 10:07:38 - <PERSON><PERSON><PERSON><PERSON><PERSON> - INFO - Successfully connected to Redis on DB index: 6!
