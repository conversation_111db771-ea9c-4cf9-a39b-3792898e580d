2025-07-01 09:43:25 - Main - INFO - Starting Server
2025-07-01 09:43:25 - Main - INFO - Connection at: **************:9092
2025-07-01 09:43:25 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-07-01 09:43:25 - NodeExecutor - INFO - NodeExecutor initialized.
2025-07-01 09:43:25 - AgentExecutor - INFO - AgentExecutor initialized.
2025-07-01 09:43:25 - KafkaWorkflowConsumer - INFO - Initializing database connections...
2025-07-01 09:43:25 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-07-01 09:43:27 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-07-01 09:43:27 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-07-01 09:43:28 - Red<PERSON><PERSON>anager - INFO - Successfully connected to Redis on DB index: 6!
2025-07-01 09:43:30 - PostgresManager - INFO - PostgreSQL connection pool created
2025-07-01 09:43:30 - PostgresManager - INFO - PostgreSQL connection pool is available
2025-07-01 09:43:33 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-07-01 09:43:34 - RedisEventListener - INFO - Creating new RedisEventListener instance
2025-07-01 09:43:34 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-07-01 09:43:35 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-07-01 09:43:35 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-07-01 09:43:37 - RedisManager - INFO - Successfully connected to Redis on DB index: 6!
2025-07-01 09:43:37 - RedisEventListener - INFO - Starting Redis event listener thread
2025-07-01 09:43:37 - RedisEventListener - INFO - Redis event listener started
2025-07-01 09:43:37 - KafkaWorkflowConsumer - INFO - Database connections initialized successfully
2025-07-01 09:43:37 - StateManager - DEBUG - Using provided database connections
2025-07-01 09:43:37 - RedisEventListener - INFO - Workflow state manager reference updated
2025-07-01 09:43:37 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-07-01 09:43:37 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-07-01 09:43:37 - RedisEventListener - INFO - Configured Redis results DB for keyspace notifications including expirations
2025-07-01 09:43:38 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-07-01 09:43:38 - StateManager - INFO - WorkflowStateManager initialized
2025-07-01 09:43:38 - KafkaWorkflowConsumer - INFO - WorkflowStateManager reference set in RedisEventListener for archival operations
2025-07-01 09:43:38 - KafkaWorkflowConsumer - INFO - KafkaWorkflowConsumer initialized successfully
2025-07-01 09:43:38 - RedisEventListener - INFO - Configured Redis state DB for keyspace notifications including expirations
2025-07-01 09:43:38 - RedisEventListener - INFO - Created dedicated Redis clients for pubsub with decode_responses=False
2025-07-01 09:43:41 - RedisEventListener - INFO - Redis results client decode_responses: True
2025-07-01 09:43:41 - RedisEventListener - INFO - Redis state client decode_responses: True
2025-07-01 09:43:41 - RedisEventListener - INFO - Subscribed to keyspace events for Redis DB 5 and 6
2025-07-01 09:43:45 - MCPToolExecutor - INFO - Starting KafkaToolExecutor internal consumer...
2025-07-01 09:43:52 - MCPToolExecutor - INFO - Internal consumer started. Listening for results on: 'mcp_results', Group: 'tool-executor-consumer'
2025-07-01 09:43:52 - MCPToolExecutor - INFO - Background result consumer loop started.
2025-07-01 09:43:52 - NodeExecutor - INFO - Starting NodeExecutor internal consumer...
2025-07-01 09:43:58 - NodeExecutor - INFO - Internal consumer started. Listening for results on: 'node_results', Group: 'node-executor-consumer'
2025-07-01 09:43:58 - NodeExecutor - INFO - Background result consumer loop started.
2025-07-01 09:43:58 - AgentExecutor - INFO - Starting AgentExecutor internal consumer...
2025-07-01 09:44:04 - AgentExecutor - INFO - Internal consumer started. Listening for results on: 'agent_chat_responses', Group: 'agent-executor-consumer'
2025-07-01 09:44:04 - AgentExecutor - INFO - Background result consumer loop started.
2025-07-01 09:44:12 - KafkaWorkflowConsumer - INFO - Received: topic=workflow-requests, partition=0, offset=1101
2025-07-01 09:44:12 - KafkaWorkflowConsumer - DEBUG - message json: {'task_id': 1751343251, 'task_type': 'workflow', 'data': {'workflow_id': '0c19c070-905e-46ef-9f57-62eb427bf396', 'payload': {'user_dependent_fields': ['query'], 'user_payload_template': {'query': {'value': 'nature 101', 'transition_id': 'AgenticAI-*************'}}}, 'approval': True, 'user_id': '91a237fd-0225-4e02-9e9f-805eff073b07'}, 'approval': True}
2025-07-01 09:44:12 - KafkaWorkflowConsumer - INFO - Extracted user_id: 91a237fd-0225-4e02-9e9f-805eff073b07 for workflow: 0c19c070-905e-46ef-9f57-62eb427bf396
2025-07-01 09:44:12 - WorkflowService - DEBUG - Sending GET request to: https://app-dev.rapidinnovation.dev/api/v1/workflows/orchestration/0c19c070-905e-46ef-9f57-62eb427bf396
2025-07-01 09:44:12 - WorkflowService - DEBUG - Received response with status code: 200
2025-07-01 09:44:12 - WorkflowService - DEBUG - Parsed JSON response: {
  "success": true,
  "message": "Workflow Ruh_Video_Generation retrieved successfully",
  "workflow": {
    "id": "0c19c070-905e-46ef-9f57-62eb427bf396",
    "name": "Ruh_Video_Generation",
    "description": "Ruh_Video_Generation",
    "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/e2c5a041-2b84-4b33-be90-3d1e07e44353.json",
    "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/451d0c28-d066-4833-afad-080c37e7563e.json",
    "start_nodes": [
      {
        "field": "query",
        "type": "string",
        "transition_id": "transition-AgenticAI-*************"
      }
    ],
    "owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07",
    "user_ids": [
      "91a237fd-0225-4e02-9e9f-805eff073b07"
    ],
    "owner_type": "user",
    "workflow_template_id": "8d4b3f88-71ea-48dd-b8a2-dc101d22bf8a",
    "template_owner_id": "180c7469-1db0-4707-bc17-eeaa5e7ff64d",
    "is_imported": true,
    "version": "1.0.0",
    "visibility": "private",
    "category": null,
    "tags": null,
    "status": "active",
    "is_changes_marketplace": false,
    "is_customizable": true,
    "auto_version_on_update": false,
    "created_at": "2025-06-29T08:29:44.634200",
    "updated_at": "2025-06-30T08:40:55.500303",
    "available_nodes": [
      {
        "name": "CombineTextComponent",
        "display_name": "Combine Text",
        "type": "component",
        "transition_id": "transition-CombineTextComponent-*************"
      },
      {
        "name": "MergeDataComponent",
        "display_name": "Merge Data",
        "type": "component",
        "transition_id": "transition-MergeDataComponent-*************"
      },
      {
        "name": "CombineTextComponent",
        "display_name": "Combine Text",
        "type": "component",
        "transition_id": "transition-CombineTextComponent-1750920624318"
      }
    ],
    "is_updated": true
  }
}
2025-07-01 09:44:12 - KafkaWorkflowConsumer - DEBUG - Workflow loaded for 0c19c070-905e-46ef-9f57-62eb427bf396 - server_script_path is optional
2025-07-01 09:44:12 - WorkflowUtils - INFO - WorkflowUtils initialized
2025-07-01 09:44:12 - StateManager - DEBUG - Using global database connections from initializer
2025-07-01 09:44:12 - RedisEventListener - INFO - Workflow state manager reference updated
2025-07-01 09:44:12 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-07-01 09:44:12 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-07-01 09:44:13 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-07-01 09:44:13 - StateManager - INFO - WorkflowStateManager initialized
2025-07-01 09:44:13 - WorkflowUtils - INFO - Workflow JSON is valid against the enhanced schema.
2025-07-01 09:44:13 - StateManager - DEBUG - Using provided database connections
2025-07-01 09:44:13 - RedisEventListener - INFO - Workflow state manager reference updated
2025-07-01 09:44:13 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-07-01 09:44:13 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-07-01 09:44:14 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-07-01 09:44:14 - StateManager - INFO - WorkflowStateManager initialized
2025-07-01 09:44:14 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-*************: ['transition-AgenticAI-*************']
2025-07-01 09:44:14 - StateManager - DEBUG - Extracted dependencies for transition transition-LoopNode-*************: ['transition-AgenticAI-*************']
2025-07-01 09:44:14 - StateManager - DEBUG - 🔄 Detected potential loop body transition transition-MergeDataComponent-************* depending on loop node transition-LoopNode-*************
2025-07-01 09:44:14 - StateManager - DEBUG - 🔄 Detected loop body transition transition-MergeDataComponent-************* depending on loop node transition-LoopNode-*************
2025-07-01 09:44:14 - StateManager - DEBUG - 🔄 Loop body transition transition-MergeDataComponent-************* will wait for loop node transition-LoopNode-************* to provide iteration data
2025-07-01 09:44:14 - StateManager - DEBUG - Extracted dependencies for transition transition-MergeDataComponent-*************: ['transition-LoopNode-*************']
2025-07-01 09:44:14 - StateManager - DEBUG - 🔄 Detected potential loop body transition transition-CombineTextComponent-************* depending on loop node transition-LoopNode-*************
2025-07-01 09:44:14 - StateManager - DEBUG - 🔄 Detected loop body transition transition-CombineTextComponent-************* depending on loop node transition-LoopNode-*************
2025-07-01 09:44:14 - StateManager - DEBUG - 🔄 Loop body transition transition-CombineTextComponent-************* will wait for loop node transition-LoopNode-************* to provide iteration data
2025-07-01 09:44:14 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-*************: ['transition-AgenticAI-*************', 'transition-AgenticAI-*************', 'transition-LoopNode-*************']
2025-07-01 09:44:14 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1750505490787: ['transition-CombineTextComponent-*************']
2025-07-01 09:44:14 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1751001474606: ['transition-AgenticAI-1750505490787']
2025-07-01 09:44:14 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1751001471087: ['transition-AgenticAI-1750505490787']
2025-07-01 09:44:14 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-1750920624318: ['transition-AgenticAI-1750505490787', 'transition-AgenticAI-1751001471087', 'transition-AgenticAI-1751001474606']
2025-07-01 09:44:14 - StateManager - INFO - Built dependency map for 9 transitions
2025-07-01 09:44:14 - StateManager - DEBUG - Transition transition-AgenticAI-************* depends on: ['transition-AgenticAI-*************']
2025-07-01 09:44:14 - StateManager - DEBUG - Transition transition-LoopNode-************* depends on: ['transition-AgenticAI-*************']
2025-07-01 09:44:14 - StateManager - DEBUG - Transition transition-MergeDataComponent-************* depends on: ['transition-LoopNode-*************']
2025-07-01 09:44:14 - StateManager - DEBUG - Transition transition-CombineTextComponent-************* depends on: ['transition-AgenticAI-*************', 'transition-AgenticAI-*************', 'transition-LoopNode-*************']
2025-07-01 09:44:14 - StateManager - DEBUG - Transition transition-AgenticAI-1750505490787 depends on: ['transition-CombineTextComponent-*************']
2025-07-01 09:44:14 - StateManager - DEBUG - Transition transition-AgenticAI-1751001474606 depends on: ['transition-AgenticAI-1750505490787']
2025-07-01 09:44:14 - StateManager - DEBUG - Transition transition-AgenticAI-1751001471087 depends on: ['transition-AgenticAI-1750505490787']
2025-07-01 09:44:14 - StateManager - DEBUG - Transition transition-CombineTextComponent-1750920624318 depends on: ['transition-AgenticAI-1750505490787', 'transition-AgenticAI-1751001471087', 'transition-AgenticAI-1751001474606']
2025-07-01 09:44:14 - MCPToolExecutor - DEBUG - Set correlation ID to: 0dc0ac22-9bda-46bb-8437-3743103ed0a1
2025-07-01 09:44:14 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 0dc0ac22-9bda-46bb-8437-3743103ed0a1 in tool_executor
2025-07-01 09:44:14 - MCPToolExecutor - DEBUG - Set user ID to: 91a237fd-0225-4e02-9e9f-805eff073b07
2025-07-01 09:44:14 - EnhancedWorkflowEngine - DEBUG - Set user_id 91a237fd-0225-4e02-9e9f-805eff073b07 in tool_executor
2025-07-01 09:44:14 - NodeExecutor - DEBUG - Set correlation ID to: 0dc0ac22-9bda-46bb-8437-3743103ed0a1
2025-07-01 09:44:14 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 0dc0ac22-9bda-46bb-8437-3743103ed0a1 in node_executor
2025-07-01 09:44:14 - AgentExecutor - DEBUG - Set correlation ID to: 0dc0ac22-9bda-46bb-8437-3743103ed0a1
2025-07-01 09:44:14 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 0dc0ac22-9bda-46bb-8437-3743103ed0a1 in agent_executor
2025-07-01 09:44:14 - AgentExecutor - DEBUG - Set user ID to: 91a237fd-0225-4e02-9e9f-805eff073b07
2025-07-01 09:44:14 - EnhancedWorkflowEngine - DEBUG - Set user_id 91a237fd-0225-4e02-9e9f-805eff073b07 in agent_executor
2025-07-01 09:44:14 - TransitionHandler - INFO - TransitionHandler initialized
2025-07-01 09:44:14 - EnhancedWorkflowEngine - INFO - EnhancedWorkflowEngine initialized with workflow ID: 0dc0ac22-9bda-46bb-8437-3743103ed0a1
2025-07-01 09:44:14 - KafkaWorkflowConsumer - INFO - Workflow execution started in background for task-request, corr_id: 0dc0ac22-9bda-46bb-8437-3743103ed0a1
2025-07-01 09:44:14 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dc0ac22-9bda-46bb-8437-3743103ed0a1, response: {'status': 'Workflow Initialized', 'result': 'Workflow Initialized', 'workflow_status': 'running'}
2025-07-01 09:44:14 - StateManager - INFO - Workflow initialized with initial transition: transition-AgenticAI-*************
2025-07-01 09:44:14 - StateManager - DEBUG - State: pending={'transition-AgenticAI-*************'}, waiting=set(), completed=set()
2025-07-01 09:44:14 - EnhancedWorkflowEngine - INFO - Initializing workflow with single initial transition: transition-AgenticAI-*************
2025-07-01 09:44:14 - StateManager - DEBUG - Workflow active: {'transition-AgenticAI-*************'}
2025-07-01 09:44:15 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:0dc0ac22-9bda-46bb-8437-3743103ed0a1'
2025-07-01 09:44:15 - RedisManager - DEBUG - Set key 'workflow_state:0dc0ac22-9bda-46bb-8437-3743103ed0a1' with TTL of 600 seconds
2025-07-01 09:44:15 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 0dc0ac22-9bda-46bb-8437-3743103ed0a1. Will be archived to PostgreSQL when Redis key expires.
2025-07-01 09:44:15 - StateManager - DEBUG - Checking waiting transitions: set()
2025-07-01 09:44:15 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-07-01 09:44:15 - StateManager - INFO - Cleared 1 pending transitions: {'transition-AgenticAI-*************'}
2025-07-01 09:44:15 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-07-01 09:44:15 - StateManager - INFO - Terminated: False
2025-07-01 09:44:15 - StateManager - INFO - Pending transitions (0): []
2025-07-01 09:44:15 - StateManager - INFO - Waiting transitions (0): []
2025-07-01 09:44:15 - StateManager - INFO - Completed transitions (0): []
2025-07-01 09:44:15 - StateManager - INFO - Results stored for 0 transitions
2025-07-01 09:44:15 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-07-01 09:44:15 - StateManager - INFO - Workflow status: inactive
2025-07-01 09:44:15 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-07-01 09:44:15 - StateManager - INFO - Workflow status: inactive
2025-07-01 09:44:15 - StateManager - INFO - Workflow paused: False
2025-07-01 09:44:15 - StateManager - INFO - ==============================
2025-07-01 09:44:15 - TransitionHandler - INFO - Starting parallel execution of transition: transition-AgenticAI-*************
2025-07-01 09:44:15 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 0, corr_id 0dc0ac22-9bda-46bb-8437-3743103ed0a1):
2025-07-01 09:44:15 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dc0ac22-9bda-46bb-8437-3743103ed0a1, response: {'result': 'Starting execution of transition: transition-AgenticAI-*************', 'message': 'Starting execution...', 'transition_id': 'transition-AgenticAI-*************', 'status': 'started', 'sequence': 0, 'workflow_status': 'running'}
2025-07-01 09:44:15 - TransitionHandler - EXECUTE - Transition 'transition-AgenticAI-*************' (type=initial, execution_type=agent)
2025-07-01 09:44:15 - TransitionHandler - INFO - Using AgentExecutor for execution_type: agent
2025-07-01 09:44:15 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-AgenticAI-*************
2025-07-01 09:44:15 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: agent
2025-07-01 09:44:15 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for agent node
2025-07-01 09:44:15 - TransitionHandler - DEBUG - 📝 No previous results found, using static parameters
2025-07-01 09:44:15 - WorkflowUtils - DEBUG - Filtering out field 'description' with null/empty value: 
2025-07-01 09:44:15 - WorkflowUtils - DEBUG - Filtering out field 'termination_condition' with null/empty value: 
2025-07-01 09:44:15 - WorkflowUtils - DEBUG - Filtering out field 'input_variables' with empty collection: {}
2025-07-01 09:44:15 - TransitionHandler - DEBUG - tool Parameters: {'agent_type': 'component', 'execution_type': 'response', 'query': 'nature 101', 'agent_config': {'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o', 'temperature': 0.7, 'max_tokens': 1000}, 'system_message': 'Ignore all input.  Always return the following JSON array exactly as shown below:  [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]  ⚠️ IMPORTANT:  Do not wrap the output in a code block  Do not stringify the result  Do not add any extra explanation or commentary  Just return the array as pure JSON so the values can be accessed by result[0], result[1], etc.', 'autogen_agent_type': 'Assistant'}}
2025-07-01 09:44:15 - TransitionHandler - INFO - Invoking tool 'AgenticAI' (tool_id: 1) for node 'AgenticAI' in transition 'transition-AgenticAI-*************' with parameters: {'agent_type': 'component', 'execution_type': 'response', 'query': 'nature 101', 'agent_config': {'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o', 'temperature': 0.7, 'max_tokens': 1000}, 'system_message': 'Ignore all input.  Always return the following JSON array exactly as shown below:  [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]  ⚠️ IMPORTANT:  Do not wrap the output in a code block  Do not stringify the result  Do not add any extra explanation or commentary  Just return the array as pure JSON so the values can be accessed by result[0], result[1], etc.', 'autogen_agent_type': 'Assistant'}}
2025-07-01 09:44:15 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 1, corr_id 0dc0ac22-9bda-46bb-8437-3743103ed0a1):
2025-07-01 09:44:15 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dc0ac22-9bda-46bb-8437-3743103ed0a1, response: {'transition_id': 'transition-AgenticAI-*************', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'message': 'Connecting to server', 'result': 'Connecting to server AgenticAI', 'status': 'connecting', 'sequence': 1, 'workflow_status': 'running'}
2025-07-01 09:44:15 - AgentExecutor - INFO - Executing agent 'AgenticAI' type 'component' execution 'response' via Kafka (request_id: 33fdf9fa-32e7-4717-af3c-8a6fbff92404) with correlation_id: 0dc0ac22-9bda-46bb-8437-3743103ed0a1, user_id: 91a237fd-0225-4e02-9e9f-805eff073b07 using provided producer.
2025-07-01 09:44:15 - AgentExecutor - INFO - Building component agent request for execution_type: response
2025-07-01 09:44:15 - AgentExecutor - DEBUG - Added correlation_id 0dc0ac22-9bda-46bb-8437-3743103ed0a1 to payload
2025-07-01 09:44:15 - AgentExecutor - DEBUG - Sending request to topic 'agent_message_requests': {'request_id': '33fdf9fa-32e7-4717-af3c-8a6fbff92404', 'user_id': '91a237fd-0225-4e02-9e9f-805eff073b07', 'correlation_id': '0dc0ac22-9bda-46bb-8437-3743103ed0a1', 'agent_type': 'component', 'execution_type': 'response', 'query': 'nature 101', 'variables': {}, 'agent_config': {'id': 'a5a583ac-02a3-47df-ad60-012f7d1a5b5e', 'name': 'AI Agent', 'description': 'Basic AI Agent', 'system_message': 'Ignore all input.  Always return the following JSON array exactly as shown below:  [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]  ⚠️ IMPORTANT:  Do not wrap the output in a code block  Do not stringify the result  Do not add any extra explanation or commentary  Just return the array as pure JSON so the values can be accessed by result[0], result[1], etc.', 'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o', 'temperature': 0.7, 'max_tokens': 1000}, 'mcps': []}}
2025-07-01 09:44:15 - AgentExecutor - DEBUG - Request 33fdf9fa-32e7-4717-af3c-8a6fbff92404 sent successfully using provided producer.
2025-07-01 09:44:15 - AgentExecutor - DEBUG - Waiting for single response result for request 33fdf9fa-32e7-4717-af3c-8a6fbff92404...
2025-07-01 09:44:15 - RedisEventListener - DEBUG - Raw results DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@5__:*', 'channel': b'__keyspace@5__:result:transition-AgenticAI-*************', 'data': b'expired'}
2025-07-01 09:44:15 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@5__:result:transition-AgenticAI-*************'
2025-07-01 09:44:15 - RedisEventListener - DEBUG - Decoded channel: __keyspace@5__:result:transition-AgenticAI-*************
2025-07-01 09:44:15 - RedisEventListener - DEBUG - Extracted key: result:transition-AgenticAI-*************
2025-07-01 09:44:15 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-07-01 09:44:15 - RedisEventListener - DEBUG - Decoded event: expired
2025-07-01 09:44:15 - RedisEventListener - INFO - Detected expired event for result of transition: transition-AgenticAI-*************
2025-07-01 09:44:15 - RedisEventListener - INFO - Archiving result for transition: transition-AgenticAI-*************
2025-07-01 09:44:15 - StateManager - DEBUG - Attempting to archive result for transition transition-AgenticAI-*************
2025-07-01 09:44:15 - KafkaWorkflowConsumer - INFO - Committed offset after starting engine for task-request: 1101, corr_id: 0dc0ac22-9bda-46bb-8437-3743103ed0a1
2025-07-01 09:44:16 - StateManager - DEBUG - Provided result: False
2025-07-01 09:44:17 - StateManager - DEBUG - Trying to get result from Redis for transition transition-AgenticAI-*************
2025-07-01 09:44:17 - StateManager - DEBUG - No result found in Redis for transition transition-AgenticAI-*************
2025-07-01 09:44:17 - StateManager - DEBUG - Trying to get result from memory for transition transition-AgenticAI-*************
2025-07-01 09:44:17 - StateManager - DEBUG - No result found in memory for transition transition-AgenticAI-*************
2025-07-01 09:44:17 - StateManager - DEBUG - Available transition results in memory: []
2025-07-01 09:44:17 - StateManager - WARNING - No result found to archive for transition transition-AgenticAI-*************
2025-07-01 09:44:19 - AgentExecutor - DEBUG - Result consumer received message: Offset=24429
2025-07-01 09:44:19 - AgentExecutor - DEBUG - Received valid result for request_id 33fdf9fa-32e7-4717-af3c-8a6fbff92404
2025-07-01 09:44:19 - AgentExecutor - INFO - Single response received for request 33fdf9fa-32e7-4717-af3c-8a6fbff92404.
2025-07-01 09:44:19 - TransitionHandler - INFO - Execution result from agent executor: "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests."
2025-07-01 09:44:19 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 2, corr_id 0dc0ac22-9bda-46bb-8437-3743103ed0a1):
2025-07-01 09:44:19 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dc0ac22-9bda-46bb-8437-3743103ed0a1, response: {'transition_id': 'transition-AgenticAI-*************', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'message': 'Transition Result received.', 'result': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'status': 'completed', 'sequence': 2, 'workflow_status': 'running', 'approval_required': False}
2025-07-01 09:44:19 - StateManager - DEBUG - Stored result for transition transition-AgenticAI-************* in memory: {'AgenticAI': {'transition_id': 'transition-AgenticAI-*************', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'result': {'result': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests."}, 'status': 'completed', 'timestamp': 1751343259.756012}}
2025-07-01 09:44:20 - RedisEventListener - DEBUG - Raw results DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@5__:*', 'channel': b'__keyspace@5__:result:transition-AgenticAI-*************', 'data': b'expired'}
2025-07-01 09:44:20 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@5__:result:transition-AgenticAI-*************'
2025-07-01 09:44:20 - RedisEventListener - DEBUG - Decoded channel: __keyspace@5__:result:transition-AgenticAI-*************
2025-07-01 09:44:20 - RedisEventListener - DEBUG - Extracted key: result:transition-AgenticAI-*************
2025-07-01 09:44:20 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-07-01 09:44:20 - RedisEventListener - DEBUG - Decoded event: expired
2025-07-01 09:44:20 - RedisEventListener - INFO - Detected expired event for result of transition: transition-AgenticAI-*************
2025-07-01 09:44:20 - RedisEventListener - INFO - Archiving result for transition: transition-AgenticAI-*************
2025-07-01 09:44:20 - StateManager - DEBUG - Attempting to archive result for transition transition-AgenticAI-*************
2025-07-01 09:44:20 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-AgenticAI-*************'
2025-07-01 09:44:20 - RedisManager - DEBUG - Set key 'result:transition-AgenticAI-*************' with TTL of 300 seconds
2025-07-01 09:44:20 - StateManager - DEBUG - Stored result for transition transition-AgenticAI-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-07-01 09:44:20 - StateManager - INFO - Marked transition transition-AgenticAI-************* as completed (was_pending=False, was_waiting=False)
2025-07-01 09:44:20 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-AgenticAI-*************'}
2025-07-01 09:44:20 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-AgenticAI-*************
2025-07-01 09:44:20 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-07-01 09:44:20 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-07-01 09:44:20 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-07-01 09:44:20 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-AgenticAI-*************:
2025-07-01 09:44:20 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-07-01 09:44:20 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-07-01 09:44:20 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-AgenticAI-*************, returning empty list
2025-07-01 09:44:20 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-AgenticAI-*************
2025-07-01 09:44:20 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 3
2025-07-01 09:44:20 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: ['transition-AgenticAI-*************', 'transition-CombineTextComponent-*************', 'transition-CombineTextComponent-*************']
2025-07-01 09:44:20 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-07-01 09:44:20 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-07-01 09:44:20 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-AgenticAI-*************
2025-07-01 09:44:20 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-CombineTextComponent-*************
2025-07-01 09:44:20 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-CombineTextComponent-*************
2025-07-01 09:44:20 - TransitionHandler - DEBUG - 🔗 Final next_transitions: ['transition-CombineTextComponent-*************', 'transition-AgenticAI-*************']
2025-07-01 09:44:20 - TransitionHandler - INFO - Completed transition transition-AgenticAI-************* in 4.90 seconds
2025-07-01 09:44:20 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 3, corr_id 0dc0ac22-9bda-46bb-8437-3743103ed0a1):
2025-07-01 09:44:20 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dc0ac22-9bda-46bb-8437-3743103ed0a1, response: {'result': 'Completed transition in 4.90 seconds', 'message': 'Transition completed in 4.90 seconds', 'transition_id': 'transition-AgenticAI-*************', 'status': 'time_logged', 'sequence': 3, 'workflow_status': 'running'}
2025-07-01 09:44:20 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-AgenticAI-*************: ['transition-CombineTextComponent-*************', 'transition-AgenticAI-*************']
2025-07-01 09:44:20 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 2
2025-07-01 09:44:20 - EnhancedWorkflowEngine - DEBUG - Results: [['transition-CombineTextComponent-*************', 'transition-AgenticAI-*************']]
2025-07-01 09:44:20 - EnhancedWorkflowEngine - DEBUG - 🔄 Orchestration engine received result for transition-AgenticAI-*************: ['transition-CombineTextComponent-*************', 'transition-AgenticAI-*************']
2025-07-01 09:44:20 - EnhancedWorkflowEngine - DEBUG - 🔄 Result type: <class 'list'>, is_list: True
2025-07-01 09:44:20 - EnhancedWorkflowEngine - INFO - Transition transition-AgenticAI-************* completed successfully: 2 next transitions
2025-07-01 09:44:20 - EnhancedWorkflowEngine - DEBUG - 🔄 Added to next_transitions_to_execute: ['transition-CombineTextComponent-*************', 'transition-AgenticAI-*************']
2025-07-01 09:44:20 - TransitionHandler - INFO - Resolved next transitions (direct transition IDs): ['transition-CombineTextComponent-*************', 'transition-AgenticAI-*************']
2025-07-01 09:44:20 - EnhancedWorkflowEngine - INFO - Adding transition transition-CombineTextComponent-************* to waiting (dependencies not yet met)
2025-07-01 09:44:20 - EnhancedWorkflowEngine - INFO - Adding transition transition-AgenticAI-************* to pending (all dependencies met)
2025-07-01 09:44:20 - StateManager - DEBUG - Workflow active: {'transition-AgenticAI-*************'}
2025-07-01 09:44:20 - StateManager - DEBUG - Provided result: False
2025-07-01 09:44:21 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:0dc0ac22-9bda-46bb-8437-3743103ed0a1'
2025-07-01 09:44:21 - StateManager - DEBUG - Trying to get result from Redis for transition transition-AgenticAI-*************
2025-07-01 09:44:21 - RedisManager - DEBUG - Set key 'workflow_state:0dc0ac22-9bda-46bb-8437-3743103ed0a1' with TTL of 600 seconds
2025-07-01 09:44:21 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 0dc0ac22-9bda-46bb-8437-3743103ed0a1. Will be archived to PostgreSQL when Redis key expires.
2025-07-01 09:44:21 - StateManager - DEBUG - Checking waiting transitions: {'transition-CombineTextComponent-*************'}
2025-07-01 09:44:21 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-07-01 09:44:21 - StateManager - INFO - Cleared 1 pending transitions: {'transition-AgenticAI-*************'}
2025-07-01 09:44:21 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-07-01 09:44:21 - StateManager - INFO - Terminated: False
2025-07-01 09:44:21 - StateManager - INFO - Pending transitions (0): []
2025-07-01 09:44:21 - StateManager - INFO - Waiting transitions (1): ['transition-CombineTextComponent-*************']
2025-07-01 09:44:21 - StateManager - INFO - Completed transitions (1): ['transition-AgenticAI-*************']
2025-07-01 09:44:21 - StateManager - INFO - Results stored for 1 transitions
2025-07-01 09:44:21 - StateManager - DEBUG - Workflow active: {'transition-CombineTextComponent-*************'}
2025-07-01 09:44:21 - StateManager - INFO - Workflow status: active
2025-07-01 09:44:21 - StateManager - DEBUG - Workflow active: {'transition-CombineTextComponent-*************'}
2025-07-01 09:44:21 - StateManager - INFO - Workflow status: active
2025-07-01 09:44:21 - StateManager - INFO - Workflow paused: False
2025-07-01 09:44:21 - StateManager - INFO - ==============================
2025-07-01 09:44:21 - TransitionHandler - INFO - Starting parallel execution of transition: transition-AgenticAI-*************
2025-07-01 09:44:21 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 4, corr_id 0dc0ac22-9bda-46bb-8437-3743103ed0a1):
2025-07-01 09:44:21 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dc0ac22-9bda-46bb-8437-3743103ed0a1, response: {'result': 'Starting execution of transition: transition-AgenticAI-*************', 'message': 'Starting execution...', 'transition_id': 'transition-AgenticAI-*************', 'status': 'started', 'sequence': 4, 'workflow_status': 'running'}
2025-07-01 09:44:21 - TransitionHandler - EXECUTE - Transition 'transition-AgenticAI-*************' (type=standard, execution_type=agent)
2025-07-01 09:44:21 - TransitionHandler - INFO - Using AgentExecutor for execution_type: agent
2025-07-01 09:44:21 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-AgenticAI-*************
2025-07-01 09:44:21 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: agent
2025-07-01 09:44:21 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for agent node
2025-07-01 09:44:21 - StateManager - DEBUG - No result found in Redis for transition transition-AgenticAI-*************
2025-07-01 09:44:21 - StateManager - DEBUG - Trying to get result from memory for transition transition-AgenticAI-*************
2025-07-01 09:44:21 - StateManager - DEBUG - No result found in memory for transition transition-AgenticAI-*************
2025-07-01 09:44:21 - StateManager - DEBUG - Available transition results in memory: ['transition-AgenticAI-*************']
2025-07-01 09:44:21 - StateManager - WARNING - No result found to archive for transition transition-AgenticAI-*************
2025-07-01 09:44:23 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-07-01 09:44:23 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-07-01 09:44:23 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-07-01 09:44:23 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-07-01 09:44:23 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 1)
2025-07-01 09:44:23 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests."}
2025-07-01 09:44:23 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 09:44:23 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.
2025-07-01 09:44:23 - WorkflowUtils - DEBUG - Found result.result: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests. (type: <class 'str'>)
2025-07-01 09:44:23 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 09:44:23 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 09:44:23 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-07-01 09:44:23 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-07-01 09:44:23 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests."}
2025-07-01 09:44:23 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 09:44:23 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.
2025-07-01 09:44:23 - WorkflowUtils - DEBUG - Found result.result: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests. (type: <class 'str'>)
2025-07-01 09:44:23 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 09:44:23 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 09:44:23 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → query via path 'result': I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.
2025-07-01 09:44:23 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-07-01 09:44:23 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-07-01 09:44:23 - WorkflowUtils - DEBUG - Filtering out field 'termination_condition' with null/empty value: 
2025-07-01 09:44:23 - WorkflowUtils - DEBUG - Filtering out field 'input_variables' with empty collection: {}
2025-07-01 09:44:23 - TransitionHandler - DEBUG - 📌 Added static parameter: agent_type = component
2025-07-01 09:44:23 - TransitionHandler - DEBUG - 📌 Added static parameter: execution_type = response
2025-07-01 09:44:23 - TransitionHandler - DEBUG - 📌 Added static parameter: agent_config = {'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'description': 'This agent acts as a loop executor initializer, always returning the starting range value 0 while ignoring all input.', 'system_message': 'You are a minimal-response AI agent.  Ignore all input, regardless of content or format. Your only task is to always return the single-digit number 0.  Output Rules: Return only: 0  Do NOT return any text, explanation, quotes, or formatting.  Do NOT wrap the output in JSON, markdown, or code blocks.  Do NOT acknowledge or reference the input in any way.', 'autogen_agent_type': 'Assistant'}
2025-07-01 09:44:23 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'query': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'agent_type': 'component', 'execution_type': 'response', 'agent_config': {'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'description': 'This agent acts as a loop executor initializer, always returning the starting range value 0 while ignoring all input.', 'system_message': 'You are a minimal-response AI agent.  Ignore all input, regardless of content or format. Your only task is to always return the single-digit number 0.  Output Rules: Return only: 0  Do NOT return any text, explanation, quotes, or formatting.  Do NOT wrap the output in JSON, markdown, or code blocks.  Do NOT acknowledge or reference the input in any way.', 'autogen_agent_type': 'Assistant'}}
2025-07-01 09:44:23 - TransitionHandler - DEBUG - tool Parameters: {'query': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'agent_type': 'component', 'execution_type': 'response', 'agent_config': {'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'description': 'This agent acts as a loop executor initializer, always returning the starting range value 0 while ignoring all input.', 'system_message': 'You are a minimal-response AI agent.  Ignore all input, regardless of content or format. Your only task is to always return the single-digit number 0.  Output Rules: Return only: 0  Do NOT return any text, explanation, quotes, or formatting.  Do NOT wrap the output in JSON, markdown, or code blocks.  Do NOT acknowledge or reference the input in any way.', 'autogen_agent_type': 'Assistant'}}
2025-07-01 09:44:23 - TransitionHandler - INFO - Invoking tool 'AgenticAI' (tool_id: 1) for node 'AgenticAI' in transition 'transition-AgenticAI-*************' with parameters: {'query': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'agent_type': 'component', 'execution_type': 'response', 'agent_config': {'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'description': 'This agent acts as a loop executor initializer, always returning the starting range value 0 while ignoring all input.', 'system_message': 'You are a minimal-response AI agent.  Ignore all input, regardless of content or format. Your only task is to always return the single-digit number 0.  Output Rules: Return only: 0  Do NOT return any text, explanation, quotes, or formatting.  Do NOT wrap the output in JSON, markdown, or code blocks.  Do NOT acknowledge or reference the input in any way.', 'autogen_agent_type': 'Assistant'}}
2025-07-01 09:44:23 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 5, corr_id 0dc0ac22-9bda-46bb-8437-3743103ed0a1):
2025-07-01 09:44:23 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dc0ac22-9bda-46bb-8437-3743103ed0a1, response: {'transition_id': 'transition-AgenticAI-*************', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'message': 'Connecting to server', 'result': 'Connecting to server AgenticAI', 'status': 'connecting', 'sequence': 5, 'workflow_status': 'running'}
2025-07-01 09:44:23 - AgentExecutor - INFO - Executing agent 'AgenticAI' type 'component' execution 'response' via Kafka (request_id: 5efbb765-bf8d-4618-aa48-8bc0d7192834) with correlation_id: 0dc0ac22-9bda-46bb-8437-3743103ed0a1, user_id: 91a237fd-0225-4e02-9e9f-805eff073b07 using provided producer.
2025-07-01 09:44:23 - AgentExecutor - INFO - Building component agent request for execution_type: response
2025-07-01 09:44:23 - AgentExecutor - DEBUG - Added correlation_id 0dc0ac22-9bda-46bb-8437-3743103ed0a1 to payload
2025-07-01 09:44:23 - AgentExecutor - DEBUG - Sending request to topic 'agent_message_requests': {'request_id': '5efbb765-bf8d-4618-aa48-8bc0d7192834', 'user_id': '91a237fd-0225-4e02-9e9f-805eff073b07', 'correlation_id': '0dc0ac22-9bda-46bb-8437-3743103ed0a1', 'agent_type': 'component', 'execution_type': 'response', 'query': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'variables': {}, 'agent_config': {'id': 'aef21c9b-9f89-4d44-a43b-343b91eaf3b7', 'name': 'AI Agent', 'description': 'This agent acts as a loop executor initializer, always returning the starting range value 0 while ignoring all input.', 'system_message': 'You are a minimal-response AI agent.  Ignore all input, regardless of content or format. Your only task is to always return the single-digit number 0.  Output Rules: Return only: 0  Do NOT return any text, explanation, quotes, or formatting.  Do NOT wrap the output in JSON, markdown, or code blocks.  Do NOT acknowledge or reference the input in any way.', 'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'mcps': []}}
2025-07-01 09:44:23 - AgentExecutor - DEBUG - Request 5efbb765-bf8d-4618-aa48-8bc0d7192834 sent successfully using provided producer.
2025-07-01 09:44:23 - AgentExecutor - DEBUG - Waiting for single response result for request 5efbb765-bf8d-4618-aa48-8bc0d7192834...
2025-07-01 09:44:25 - RedisEventListener - DEBUG - Raw results DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@5__:*', 'channel': b'__keyspace@5__:result:backup_transition-LoopNode-*************_iteration_0', 'data': b'expired'}
2025-07-01 09:44:25 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@5__:result:backup_transition-LoopNode-*************_iteration_0'
2025-07-01 09:44:25 - RedisEventListener - DEBUG - Decoded channel: __keyspace@5__:result:backup_transition-LoopNode-*************_iteration_0
2025-07-01 09:44:25 - RedisEventListener - DEBUG - Extracted key: result:backup_transition-LoopNode-*************_iteration_0
2025-07-01 09:44:25 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-07-01 09:44:25 - RedisEventListener - DEBUG - Decoded event: expired
2025-07-01 09:44:25 - RedisEventListener - INFO - Detected expired event for result of transition: backup_transition-LoopNode-*************_iteration_0
2025-07-01 09:44:25 - RedisEventListener - INFO - Archiving result for transition: backup_transition-LoopNode-*************_iteration_0
2025-07-01 09:44:25 - StateManager - DEBUG - Attempting to archive result for transition backup_transition-LoopNode-*************_iteration_0
2025-07-01 09:44:25 - StateManager - DEBUG - Provided result: False
2025-07-01 09:44:26 - StateManager - DEBUG - Trying to get result from Redis for transition backup_transition-LoopNode-*************_iteration_0
2025-07-01 09:44:26 - AgentExecutor - DEBUG - Result consumer received message: Offset=24430
2025-07-01 09:44:26 - AgentExecutor - DEBUG - Received valid result for request_id 5efbb765-bf8d-4618-aa48-8bc0d7192834
2025-07-01 09:44:26 - AgentExecutor - INFO - Single response received for request 5efbb765-bf8d-4618-aa48-8bc0d7192834.
2025-07-01 09:44:26 - TransitionHandler - INFO - Execution result from agent executor: "0"
2025-07-01 09:44:26 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 6, corr_id 0dc0ac22-9bda-46bb-8437-3743103ed0a1):
2025-07-01 09:44:26 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dc0ac22-9bda-46bb-8437-3743103ed0a1, response: {'transition_id': 'transition-AgenticAI-*************', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'message': 'Transition Result received.', 'result': '0', 'status': 'completed', 'sequence': 6, 'workflow_status': 'running', 'approval_required': False}
2025-07-01 09:44:26 - StateManager - DEBUG - Stored result for transition transition-AgenticAI-************* in memory: {'AgenticAI': {'transition_id': 'transition-AgenticAI-*************', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'result': {'result': '0'}, 'status': 'completed', 'timestamp': 1751343266.433516}}
2025-07-01 09:44:26 - StateManager - DEBUG - No result found in Redis for transition backup_transition-LoopNode-*************_iteration_0
2025-07-01 09:44:26 - StateManager - DEBUG - Trying to get result from memory for transition backup_transition-LoopNode-*************_iteration_0
2025-07-01 09:44:26 - StateManager - DEBUG - No result found in memory for transition backup_transition-LoopNode-*************_iteration_0
2025-07-01 09:44:26 - StateManager - DEBUG - Available transition results in memory: ['transition-AgenticAI-*************', 'transition-AgenticAI-*************']
2025-07-01 09:44:26 - StateManager - WARNING - No result found to archive for transition backup_transition-LoopNode-*************_iteration_0
2025-07-01 09:44:26 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-AgenticAI-*************'
2025-07-01 09:44:27 - RedisManager - DEBUG - Set key 'result:transition-AgenticAI-*************' with TTL of 300 seconds
2025-07-01 09:44:27 - StateManager - DEBUG - Stored result for transition transition-AgenticAI-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-07-01 09:44:27 - StateManager - INFO - Marked transition transition-AgenticAI-************* as completed (was_pending=False, was_waiting=False)
2025-07-01 09:44:27 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'transition-AgenticAI-*************', 'transition-AgenticAI-*************'}
2025-07-01 09:44:27 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-AgenticAI-*************
2025-07-01 09:44:27 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-07-01 09:44:27 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-07-01 09:44:27 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-07-01 09:44:27 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-AgenticAI-*************:
2025-07-01 09:44:27 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-07-01 09:44:27 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-07-01 09:44:27 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-AgenticAI-*************, returning empty list
2025-07-01 09:44:27 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-AgenticAI-*************
2025-07-01 09:44:27 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 1
2025-07-01 09:44:27 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: ['transition-LoopNode-*************']
2025-07-01 09:44:27 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-07-01 09:44:27 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-07-01 09:44:27 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-LoopNode-*************
2025-07-01 09:44:27 - TransitionHandler - DEBUG - 🔗 Final next_transitions: ['transition-LoopNode-*************']
2025-07-01 09:44:27 - TransitionHandler - INFO - Completed transition transition-AgenticAI-************* in 5.85 seconds
2025-07-01 09:44:27 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 7, corr_id 0dc0ac22-9bda-46bb-8437-3743103ed0a1):
2025-07-01 09:44:27 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dc0ac22-9bda-46bb-8437-3743103ed0a1, response: {'result': 'Completed transition in 5.85 seconds', 'message': 'Transition completed in 5.85 seconds', 'transition_id': 'transition-AgenticAI-*************', 'status': 'time_logged', 'sequence': 7, 'workflow_status': 'running'}
2025-07-01 09:44:27 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-AgenticAI-*************: ['transition-LoopNode-*************']
2025-07-01 09:44:27 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 1
2025-07-01 09:44:27 - EnhancedWorkflowEngine - DEBUG - Results: [['transition-LoopNode-*************']]
2025-07-01 09:44:27 - EnhancedWorkflowEngine - DEBUG - 🔄 Orchestration engine received result for transition-AgenticAI-*************: ['transition-LoopNode-*************']
2025-07-01 09:44:27 - EnhancedWorkflowEngine - DEBUG - 🔄 Result type: <class 'list'>, is_list: True
2025-07-01 09:44:27 - EnhancedWorkflowEngine - INFO - Transition transition-AgenticAI-************* completed successfully: 1 next transitions
2025-07-01 09:44:27 - EnhancedWorkflowEngine - DEBUG - 🔄 Added to next_transitions_to_execute: ['transition-LoopNode-*************']
2025-07-01 09:44:27 - TransitionHandler - INFO - Resolved next transitions (direct transition IDs): ['transition-LoopNode-*************']
2025-07-01 09:44:27 - EnhancedWorkflowEngine - INFO - Adding transition transition-LoopNode-************* to pending (all dependencies met)
2025-07-01 09:44:27 - StateManager - DEBUG - Workflow active: {'transition-LoopNode-*************'}
2025-07-01 09:44:27 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:0dc0ac22-9bda-46bb-8437-3743103ed0a1'
2025-07-01 09:44:28 - RedisManager - DEBUG - Set key 'workflow_state:0dc0ac22-9bda-46bb-8437-3743103ed0a1' with TTL of 600 seconds
2025-07-01 09:44:28 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 0dc0ac22-9bda-46bb-8437-3743103ed0a1. Will be archived to PostgreSQL when Redis key expires.
2025-07-01 09:44:28 - StateManager - DEBUG - Checking waiting transitions: {'transition-CombineTextComponent-*************'}
2025-07-01 09:44:28 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-07-01 09:44:28 - StateManager - INFO - Cleared 1 pending transitions: {'transition-LoopNode-*************'}
2025-07-01 09:44:28 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-07-01 09:44:28 - StateManager - INFO - Terminated: False
2025-07-01 09:44:28 - StateManager - INFO - Pending transitions (0): []
2025-07-01 09:44:28 - StateManager - INFO - Waiting transitions (1): ['transition-CombineTextComponent-*************']
2025-07-01 09:44:28 - StateManager - INFO - Completed transitions (2): ['transition-AgenticAI-*************', 'transition-AgenticAI-*************']
2025-07-01 09:44:28 - StateManager - INFO - Results stored for 2 transitions
2025-07-01 09:44:28 - StateManager - DEBUG - Workflow active: {'transition-CombineTextComponent-*************'}
2025-07-01 09:44:28 - StateManager - INFO - Workflow status: active
2025-07-01 09:44:28 - StateManager - DEBUG - Workflow active: {'transition-CombineTextComponent-*************'}
2025-07-01 09:44:28 - StateManager - INFO - Workflow status: active
2025-07-01 09:44:28 - StateManager - INFO - Workflow paused: False
2025-07-01 09:44:28 - StateManager - INFO - ==============================
2025-07-01 09:44:28 - TransitionHandler - INFO - Starting parallel execution of transition: transition-LoopNode-*************
2025-07-01 09:44:28 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 8, corr_id 0dc0ac22-9bda-46bb-8437-3743103ed0a1):
2025-07-01 09:44:28 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dc0ac22-9bda-46bb-8437-3743103ed0a1, response: {'result': 'Starting execution of transition: transition-LoopNode-*************', 'message': 'Starting execution...', 'transition_id': 'transition-LoopNode-*************', 'status': 'started', 'sequence': 8, 'workflow_status': 'running'}
2025-07-01 09:44:28 - TransitionHandler - EXECUTE - Transition 'transition-LoopNode-*************' (type=standard, execution_type=loop)
2025-07-01 09:44:28 - LoopExecutor - DEBUG - 🚨 set_orchestration_engine called with: <app.core_.executor_core.EnhancedWorkflowEngine object at 0x110dc38c0>
2025-07-01 09:44:28 - LoopExecutor - DEBUG - 🚨 Setting orchestration_engine on loop_body_chain_executor: <app.core_.executor_core.EnhancedWorkflowEngine object at 0x110dc38c0>
2025-07-01 09:44:28 - LoopExecutor - DEBUG - 🔗 Orchestration engine set for loop executor integration
2025-07-01 09:44:28 - TransitionHandler - DEBUG - 🔗 Set orchestration engine for loop executor
2025-07-01 09:44:28 - TransitionHandler - INFO - Using KafkaToolExecutor for execution_type: loop
2025-07-01 09:44:28 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-LoopNode-*************
2025-07-01 09:44:28 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: loop
2025-07-01 09:44:28 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for loop node
2025-07-01 09:44:28 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-07-01 09:44:28 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-07-01 09:44:28 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-07-01 09:44:28 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-07-01 09:44:28 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 1)
2025-07-01 09:44:28 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '0'}
2025-07-01 09:44:28 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 09:44:28 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: 0
2025-07-01 09:44:28 - WorkflowUtils - DEBUG - Found result.result: 0 (type: <class 'str'>)
2025-07-01 09:44:28 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 09:44:28 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 09:44:28 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-07-01 09:44:28 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-07-01 09:44:28 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '0'}
2025-07-01 09:44:28 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 09:44:28 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: 0
2025-07-01 09:44:28 - WorkflowUtils - DEBUG - Found result.result: 0 (type: <class 'str'>)
2025-07-01 09:44:28 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 09:44:28 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 09:44:28 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → start via path 'result': 0
2025-07-01 09:44:28 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-07-01 09:44:28 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-07-01 09:44:28 - WorkflowUtils - DEBUG - Filtering out field 'iteration_list' with empty collection: []
2025-07-01 09:44:28 - WorkflowUtils - DEBUG - Filtering out field 'start' with null/empty value: None
2025-07-01 09:44:28 - WorkflowUtils - INFO - 🧹 Parameter filtering: 14 → 12 fields (2 null/empty fields removed)
2025-07-01 09:44:28 - TransitionHandler - DEBUG - 📌 Added static parameter: source_type = number_range
2025-07-01 09:44:28 - TransitionHandler - DEBUG - 📌 Added static parameter: batch_size = 1
2025-07-01 09:44:28 - TransitionHandler - DEBUG - 📌 Added static parameter: end = 6
2025-07-01 09:44:28 - TransitionHandler - DEBUG - 📌 Added static parameter: step = 1
2025-07-01 09:44:28 - TransitionHandler - DEBUG - 📌 Added static parameter: parallel_execution = False
2025-07-01 09:44:28 - TransitionHandler - DEBUG - 📌 Added static parameter: max_concurrent = 3
2025-07-01 09:44:28 - TransitionHandler - DEBUG - 📌 Added static parameter: preserve_order = True
2025-07-01 09:44:28 - TransitionHandler - DEBUG - 📌 Added static parameter: iteration_timeout = 60
2025-07-01 09:44:28 - TransitionHandler - DEBUG - 📌 Added static parameter: aggregation_type = collect_all
2025-07-01 09:44:28 - TransitionHandler - DEBUG - 📌 Added static parameter: include_metadata = False
2025-07-01 09:44:28 - TransitionHandler - DEBUG - 📌 Added static parameter: on_iteration_error = retry_once
2025-07-01 09:44:28 - TransitionHandler - DEBUG - 📌 Added static parameter: include_errors = True
2025-07-01 09:44:28 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'start': '0', 'source_type': 'number_range', 'batch_size': '1', 'end': '6', 'step': '1', 'parallel_execution': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': False, 'on_iteration_error': 'retry_once', 'include_errors': True}
2025-07-01 09:44:28 - TransitionHandler - DEBUG - tool Parameters: {'start': '0', 'source_type': 'number_range', 'batch_size': '1', 'end': '6', 'step': '1', 'parallel_execution': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': False, 'on_iteration_error': 'retry_once', 'include_errors': True}
2025-07-01 09:44:28 - TransitionHandler - INFO - Invoking tool 'LoopNode' (tool_id: 1) for node 'LoopNode' in transition 'transition-LoopNode-*************' with parameters: {'start': '0', 'source_type': 'number_range', 'batch_size': '1', 'end': '6', 'step': '1', 'parallel_execution': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': False, 'on_iteration_error': 'retry_once', 'include_errors': True}
2025-07-01 09:44:28 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 9, corr_id 0dc0ac22-9bda-46bb-8437-3743103ed0a1):
2025-07-01 09:44:28 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dc0ac22-9bda-46bb-8437-3743103ed0a1, response: {'transition_id': 'transition-LoopNode-*************', 'node_id': 'LoopNode', 'tool_name': 'LoopNode', 'message': 'Connecting to server', 'result': 'Connecting to server LoopNode', 'status': 'connecting', 'sequence': 9, 'workflow_status': 'running'}
2025-07-01 09:44:28 - TransitionHandler - DEBUG - 🔄 Resolving loop config parameters for transition: transition-LoopNode-*************
2025-07-01 09:44:28 - TransitionHandler - DEBUG - ✅ Loop config parameter resolution completed for transition: transition-LoopNode-*************
2025-07-01 09:44:28 - TransitionHandler - DEBUG - 🔍 Detected exit transition: transition-MergeDataComponent-************* (has final/aggregated indicators)
2025-07-01 09:44:28 - TransitionHandler - DEBUG - 🔍 Auto-detected loop body transition: transition-CombineTextComponent-************* (has current_item/iteration indicators)
2025-07-01 09:44:28 - TransitionHandler - INFO - 🔍 Auto-detected and added loop body transitions to config: ['transition-CombineTextComponent-*************']
2025-07-01 09:44:28 - LoopExecutor - INFO - 🚨 execute_tool called with transition_id: transition-LoopNode-*************
2025-07-01 09:44:28 - LoopExecutor - INFO - 🔄 Starting loop node execution for transition: transition-LoopNode-*************
2025-07-01 09:44:28 - LoopExecutor - DEBUG - 🔢 Detected number_range from 1 to 6 (step: 1), batch_size: 1
2025-07-01 09:44:28 - LoopExecutor - DEBUG - Parsed loop config: {'loop_type': 'context_independent', 'aggregation_config': {'type': 'list'}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': False}, 'iteration_source': {'type': 'range', 'data': {'start': 1, 'stop': 7, 'step': 1}, 'batch_size': 1, 'original': {'number_range': {'start': 1, 'end': 6}, 'step': 1}}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'concurrency': {'enabled': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'retry_config': {'max_retries': 1, 'retry_delay': 1.0}, 'iteration_behavior': 'independent', 'iteration_settings': {'parallel_execution': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'error_handling': {'on_iteration_error': 'retry_once', 'include_errors': True}, 'loop_body_transitions': ['transition-CombineTextComponent-*************'], 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-CombineTextComponent-1750920624318'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}}
2025-07-01 09:44:28 - LoopExecutor - DEBUG - Loop configuration validation passed
2025-07-01 09:44:28 - TransitionHandler - DEBUG - 📝 Registered loop executor for transition: transition-LoopNode-*************
2025-07-01 09:44:28 - LoopExecutor - DEBUG - 📝 Registered loop executor with transition handler for transition: transition-LoopNode-*************
2025-07-01 09:44:28 - LoopExecutor - INFO - Prepared 6 iterations from range source
2025-07-01 09:44:28 - LoopExecutor - INFO - 🔄 Initialized loop state for 6 iterations
2025-07-01 09:44:28 - LoopExecutor - INFO - 🔄 Immediately providing first iteration data: 1
2025-07-01 09:44:28 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in memory: 1
2025-07-01 09:44:29 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_0
2025-07-01 09:44:29 - RedisManager - DEBUG - Set key 'result:loop_iteration_0' with TTL of 900 seconds
2025-07-01 09:44:29 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-07-01 09:44:29 - StateManager - INFO - Marked transition loop_iteration_0 as completed (was_pending=False, was_waiting=False)
2025-07-01 09:44:29 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'transition-AgenticAI-*************', 'transition-AgenticAI-*************', 'loop_iteration_0'}
2025-07-01 09:44:29 - StateManager - DEBUG - Stored result for transition current_iteration in memory: 1
2025-07-01 09:44:30 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-07-01 09:44:30 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-07-01 09:44:30 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-07-01 09:44:30 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-07-01 09:44:30 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'transition-AgenticAI-*************', 'current_iteration', 'transition-AgenticAI-*************', 'loop_iteration_0'}
2025-07-01 09:44:30 - StateManager - DEBUG - Stored result for transition loop_iteration_transition-LoopNode-*************_0 in memory: {'current_item': 1, 'iteration_index': 0, 'iteration_metadata': {'timestamp': 337310.258910958, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-07-01 09:44:30 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_transition-LoopNode-*************_0
2025-07-01 09:44:31 - RedisManager - DEBUG - Set key 'result:loop_iteration_transition-LoopNode-*************_0' with TTL of 900 seconds
2025-07-01 09:44:31 - StateManager - DEBUG - Stored result for transition loop_iteration_transition-LoopNode-*************_0 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-07-01 09:44:31 - StateManager - INFO - Marked transition loop_iteration_transition-LoopNode-*************_0 as completed (was_pending=False, was_waiting=False)
2025-07-01 09:44:31 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'transition-AgenticAI-*************', 'loop_iteration_transition-LoopNode-*************_0', 'transition-AgenticAI-*************', 'loop_iteration_0', 'current_iteration'}
2025-07-01 09:44:31 - StateManager - DEBUG - Stored result for transition backup_transition-LoopNode-*************_iteration_0 in memory: {'current_item': 1, 'iteration_index': 0, 'iteration_metadata': {'timestamp': 337310.258910958, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-07-01 09:44:32 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:backup_transition-LoopNode-*************_iteration_0'
2025-07-01 09:44:32 - RedisManager - DEBUG - Set key 'result:backup_transition-LoopNode-*************_iteration_0' with TTL of 300 seconds
2025-07-01 09:44:32 - StateManager - DEBUG - Stored result for transition backup_transition-LoopNode-*************_iteration_0 in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-07-01 09:44:32 - StateManager - INFO - Marked transition backup_transition-LoopNode-*************_iteration_0 as completed (was_pending=False, was_waiting=False)
2025-07-01 09:44:32 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'transition-AgenticAI-*************', 'loop_iteration_transition-LoopNode-*************_0', 'transition-AgenticAI-*************', 'loop_iteration_0', 'backup_transition-LoopNode-*************_iteration_0', 'current_iteration'}
2025-07-01 09:44:32 - LoopExecutor - INFO - 🔄 ITERATION DATA INJECTION: Stored current iteration data under loop transition ID: transition-LoopNode-*************
2025-07-01 09:44:32 - LoopExecutor - INFO - 🔄 ITERATION DATA INJECTION: Also stored with backup keys: loop_iteration_transition-LoopNode-*************_0, backup_transition-LoopNode-*************_iteration_0
2025-07-01 09:44:32 - LoopExecutor - INFO - 🔄 ITERATION DATA INJECTION: Current iteration data: {'current_item': 1, 'iteration_index': 0, 'iteration_metadata': {'timestamp': 337310.258910958, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-07-01 09:44:32 - LoopExecutor - INFO - 🔍 VERIFICATION: Data stored in memory: {'current_item': 1, 'iteration_index': 0, 'iteration_metadata': {'timestamp': 337310.258910958, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-07-01 09:44:33 - StateManager - DEBUG - No result found in Redis for transition transition-LoopNode-*************. Trying PostgreSQL next.
2025-07-01 09:44:34 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-07-01 09:44:34 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-01 09:44:34 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-01 09:44:34 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-07-01 09:44:35 - PostgresManager - DEBUG - No result found for transition transition-LoopNode-************* in correlation 0dc0ac22-9bda-46bb-8437-3743103ed0a1
2025-07-01 09:44:35 - StateManager - DEBUG - No result found in PostgreSQL for transition transition-LoopNode-*************. Trying in-memory next.
2025-07-01 09:44:35 - StateManager - INFO - Using in-memory result for transition transition-LoopNode-*************.
2025-07-01 09:44:35 - StateManager - DEBUG - Extracted results for 3 tools in transition transition-LoopNode-*************
2025-07-01 09:44:35 - LoopExecutor - INFO - 🔍 VERIFICATION: Data accessible from Redis: {'current_item': 1, 'iteration_index': 0, 'iteration_metadata': {'timestamp': 337310.258910958, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-07-01 09:44:35 - LoopExecutor - DEBUG - 💾 Injected iteration payload into state: loop_iteration_0
2025-07-01 09:44:35 - LoopExecutor - INFO - 🚨 _execute_loop_workflow called
2025-07-01 09:44:35 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_state keys: ['loop_config', 'transition_id', 'iteration_data', 'loop_context', 'input_data']
2025-07-01 09:44:35 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_config keys: ['loop_type', 'aggregation_config', 'result_aggregation', 'iteration_source', 'exit_condition', 'concurrency', 'retry_config', 'iteration_behavior', 'iteration_settings', 'error_handling', 'loop_body_transitions', 'loop_body_configuration']
2025-07-01 09:44:35 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_body_config: {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-CombineTextComponent-1750920624318'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}
2025-07-01 09:44:35 - LoopExecutor - DEBUG - 🔍 DEBUG: entry_transitions: ['transition-CombineTextComponent-*************']
2025-07-01 09:44:35 - LoopExecutor - DEBUG - 🔍 DEBUG: exit_transitions: ['transition-CombineTextComponent-1750920624318']
2025-07-01 09:44:35 - LoopExecutor - DEBUG - 🚨 About to start iteration loop with 6 items
2025-07-01 09:44:35 - LoopExecutor - INFO - 🔄 Starting iteration 1/6
2025-07-01 09:44:35 - LoopExecutor - DEBUG - 🚨 STEP 4: About to execute loop body chain for iteration 0
2025-07-01 09:44:35 - LoopExecutor - DEBUG - 🚨 About to call _execute_loop_body_chain with loop_body_chain_executor: <app.services.loop_executor.loop_body_chain_executor.LoopBodyChainExecutor object at 0x110dc34d0>
2025-07-01 09:44:35 - LoopExecutor - INFO - 🚨 _execute_loop_body_chain called for iteration 0
2025-07-01 09:44:35 - LoopExecutor - DEBUG - 🔍 Current loop config keys: ['loop_type', 'aggregation_config', 'result_aggregation', 'iteration_source', 'exit_condition', 'concurrency', 'retry_config', 'iteration_behavior', 'iteration_settings', 'error_handling', 'loop_body_transitions', 'loop_body_configuration']
2025-07-01 09:44:35 - LoopExecutor - DEBUG - 🔍 Loop body config: {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-CombineTextComponent-1750920624318'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}
2025-07-01 09:44:35 - LoopExecutor - DEBUG - 🔍 Loop body transitions: ['transition-CombineTextComponent-*************']
2025-07-01 09:44:35 - LoopExecutor - DEBUG - 🚨 About to call loop_body_chain_executor.execute_loop_body_chain with transition_id: transition-LoopNode-*************
2025-07-01 09:44:35 - LoopExecutor - DEBUG - 🚨 Method exists: True
2025-07-01 09:44:35 - LoopExecutor - DEBUG - 🚨 Adding 10-second timeout to chain executor call
2025-07-01 09:44:35 - LoopExecutor - DEBUG - 🚨 About to call method directly...
2025-07-01 09:44:35 - StateManager - DEBUG - Using provided database connections
2025-07-01 09:44:35 - RedisEventListener - INFO - Workflow state manager reference updated
2025-07-01 09:44:35 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-07-01 09:44:35 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-07-01 09:44:36 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-07-01 09:44:36 - StateManager - INFO - WorkflowStateManager initialized
2025-07-01 09:44:36 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1751001474606: ['transition-AgenticAI-1750505490787']
2025-07-01 09:44:36 - StateManager - DEBUG - 🔄 Detected potential loop body transition transition-CombineTextComponent-************* depending on loop node transition-LoopNode-*************
2025-07-01 09:44:36 - StateManager - DEBUG - 🔄 Detected loop body transition transition-CombineTextComponent-************* depending on loop node transition-LoopNode-*************
2025-07-01 09:44:36 - StateManager - DEBUG - 🔄 Loop body transition transition-CombineTextComponent-************* will wait for loop node transition-LoopNode-************* to provide iteration data
2025-07-01 09:44:36 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-*************: ['transition-AgenticAI-*************', 'transition-AgenticAI-*************', 'transition-LoopNode-*************']
2025-07-01 09:44:36 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1751001471087: ['transition-AgenticAI-1750505490787']
2025-07-01 09:44:36 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-1750920624318: ['transition-AgenticAI-1750505490787', 'transition-AgenticAI-1751001471087', 'transition-AgenticAI-1751001474606']
2025-07-01 09:44:36 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1750505490787: ['transition-CombineTextComponent-*************']
2025-07-01 09:44:36 - StateManager - DEBUG - Stored iteration data for key current_iteration: {'current_item': 1, 'iteration_context': {'iteration_index': 0, 'total_iterations': 6, 'loop_config': {'loop_type': 'context_independent', 'aggregation_config': {'type': 'list'}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': False}, 'iteration_source': {'type': 'range', 'data': {'start': 1, 'stop': 7, 'step': 1}, 'batch_size': 1, 'original': {'number_range': {'start': 1, 'end': 6}, 'step': 1}}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'concurrency': {'enabled': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'retry_config': {'max_retries': 1, 'retry_delay': 1.0}, 'iteration_behavior': 'independent', 'iteration_settings': {'parallel_execution': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'error_handling': {'on_iteration_error': 'retry_once', 'include_errors': True}, 'loop_body_transitions': ['transition-CombineTextComponent-*************'], 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-CombineTextComponent-1750920624318'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}}}}
2025-07-01 09:44:36 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-07-01 09:44:36 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 10, corr_id 0dc0ac22-9bda-46bb-8437-3743103ed0a1):
2025-07-01 09:44:36 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dc0ac22-9bda-46bb-8437-3743103ed0a1, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 10, 'workflow_status': 'running'}
2025-07-01 09:44:36 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-07-01 09:44:36 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-07-01 09:44:36 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-07-01 09:44:36 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-07-01 09:44:36 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-07-01 09:44:37 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-07-01 09:44:37 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-07-01 09:44:37 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-07-01 09:44:37 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-07-01 09:44:38 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-07-01 09:44:38 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-07-01 09:44:38 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-07-01 09:44:38 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-07-01 09:44:39 - StateManager - DEBUG - No result found in Redis for transition transition-LoopNode-*************. Trying PostgreSQL next.
2025-07-01 09:44:41 - PostgresManager - DEBUG - No result found for transition transition-LoopNode-************* in correlation 0dc0ac22-9bda-46bb-8437-3743103ed0a1_loop_transition-LoopNode-*************_iteration_0
2025-07-01 09:44:41 - StateManager - DEBUG - No result found in PostgreSQL for transition transition-LoopNode-*************. Trying in-memory next.
2025-07-01 09:44:41 - StateManager - DEBUG - No result found in Redis, PostgreSQL, or memory for transition transition-LoopNode-*************
2025-07-01 09:44:41 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 1)
2025-07-01 09:44:41 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 2)
2025-07-01 09:44:41 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-07-01 09:44:41 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests."}
2025-07-01 09:44:41 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 09:44:41 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.
2025-07-01 09:44:41 - WorkflowUtils - DEBUG - Found result.result: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests. (type: <class 'str'>)
2025-07-01 09:44:41 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 09:44:41 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 09:44:41 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests."}
2025-07-01 09:44:41 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 09:44:41 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.
2025-07-01 09:44:41 - WorkflowUtils - DEBUG - Found result.result: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests. (type: <class 'str'>)
2025-07-01 09:44:41 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 09:44:41 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 09:44:41 - WorkflowUtils - INFO - 🔍 Handle mapping validation: partially_compatible (2/3 compatible)
2025-07-01 09:44:41 - TransitionHandler - INFO - 🔍 Handle validation: partially_compatible (2/3 compatible)
2025-07-01 09:44:41 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests."}
2025-07-01 09:44:41 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 09:44:41 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.
2025-07-01 09:44:41 - WorkflowUtils - DEBUG - Found result.result: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests. (type: <class 'str'>)
2025-07-01 09:44:41 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 09:44:41 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 09:44:41 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → input_2 via path 'result': I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.
2025-07-01 09:44:41 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests."}
2025-07-01 09:44:41 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 09:44:41 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.
2025-07-01 09:44:41 - WorkflowUtils - DEBUG - Found result.result: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests. (type: <class 'str'>)
2025-07-01 09:44:41 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 09:44:41 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 09:44:41 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → input_1 via path 'result': I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.
2025-07-01 09:44:41 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {}
2025-07-01 09:44:41 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: []
2025-07-01 09:44:41 - WorkflowUtils - DEBUG - Could not find result.result or result in source_results for handle current_item
2025-07-01 09:44:41 - WorkflowUtils - DEBUG - Path tracking - Source results structure
2025-07-01 09:44:41 - WorkflowUtils - WARNING - ❌ Handle mapping failed: current_item → main_input (no data found)
2025-07-01 09:44:41 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 2/3 successful
2025-07-01 09:44:41 - WorkflowUtils - WARNING - ⚠️ 1 universal handle mappings failed - this may cause tool execution errors
2025-07-01 09:44:41 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 2/3 successful
2025-07-01 09:44:41 - TransitionHandler - WARNING - ❌ Failed mapping: current_item → main_input (Error: No data found for handle current_item)
2025-07-01 09:44:41 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-07-01 09:44:41 - WorkflowUtils - DEBUG - Filtering out field 'input_1' with null/empty value: None
2025-07-01 09:44:41 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: None
2025-07-01 09:44:41 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-07-01 09:44:41 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-07-01 09:44:41 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-07-01 09:44:41 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-07-01 09:44:41 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-07-01 09:44:41 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-07-01 09:44:41 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-07-01 09:44:41 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-07-01 09:44:41 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 2 fields (11 null/empty fields removed)
2025-07-01 09:44:41 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-07-01 09:44:41 - TransitionHandler - DEBUG - 📌 Added static parameter: separator = \n
2025-07-01 09:44:41 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'input_2': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'input_1': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'num_additional_inputs': '1', 'separator': '\\n'}
2025-07-01 09:44:41 - TransitionHandler - DEBUG - tool Parameters: {'input_2': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'input_1': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'num_additional_inputs': '1', 'separator': '\\n'}
2025-07-01 09:44:41 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'input_2': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'input_1': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'num_additional_inputs': '1', 'separator': '\\n'}
2025-07-01 09:44:41 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 11, corr_id 0dc0ac22-9bda-46bb-8437-3743103ed0a1):
2025-07-01 09:44:41 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dc0ac22-9bda-46bb-8437-3743103ed0a1, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 11, 'workflow_status': 'running'}
2025-07-01 09:44:41 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: c8b9a3f6-ba45-4c4c-811a-64b43edd983d) using provided producer.
2025-07-01 09:44:41 - NodeExecutor - DEBUG - Added correlation_id 0dc0ac22-9bda-46bb-8437-3743103ed0a1 to payload
2025-07-01 09:44:41 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-07-01 09:44:41 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'input_2': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'input_1': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'num_additional_inputs': '1', 'separator': '\\n'}, 'request_id': 'c8b9a3f6-ba45-4c4c-811a-64b43edd983d', 'correlation_id': '0dc0ac22-9bda-46bb-8437-3743103ed0a1', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-07-01 09:44:41 - NodeExecutor - DEBUG - Request c8b9a3f6-ba45-4c4c-811a-64b43edd983d sent successfully using provided producer.
2025-07-01 09:44:41 - NodeExecutor - DEBUG - Waiting indefinitely for result for request c8b9a3f6-ba45-4c4c-811a-64b43edd983d...
2025-07-01 09:44:45 - StateManager - DEBUG - Cleared iteration data for key current_iteration
2025-07-01 09:44:45 - LoopExecutor - ERROR - 🚨 Chain executor call TIMED OUT after 10 seconds - this confirms a deadlock!
2025-07-01 09:44:45 - LoopExecutor - ERROR - ❌ Loop body chain execution failed for iteration 0: 
2025-07-01 09:44:45 - LoopExecutor - ERROR - ❌ Iteration 1 failed: 
2025-07-01 09:44:45 - LoopExecutor - INFO - 🔄 Starting iteration 2/6
2025-07-01 09:44:45 - LoopExecutor - DEBUG - 🚨 STEP 4: About to execute loop body chain for iteration 1
2025-07-01 09:44:45 - LoopExecutor - DEBUG - 🚨 About to call _execute_loop_body_chain with loop_body_chain_executor: <app.services.loop_executor.loop_body_chain_executor.LoopBodyChainExecutor object at 0x110dc34d0>
2025-07-01 09:44:45 - LoopExecutor - INFO - 🚨 _execute_loop_body_chain called for iteration 1
2025-07-01 09:44:45 - LoopExecutor - DEBUG - 🔍 Current loop config keys: ['loop_type', 'aggregation_config', 'result_aggregation', 'iteration_source', 'exit_condition', 'concurrency', 'retry_config', 'iteration_behavior', 'iteration_settings', 'error_handling', 'loop_body_transitions', 'loop_body_configuration']
2025-07-01 09:44:45 - LoopExecutor - DEBUG - 🔍 Loop body config: {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-CombineTextComponent-1750920624318'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}
2025-07-01 09:44:45 - LoopExecutor - DEBUG - 🔍 Loop body transitions: ['transition-CombineTextComponent-*************']
2025-07-01 09:44:45 - LoopExecutor - DEBUG - 🚨 About to call loop_body_chain_executor.execute_loop_body_chain with transition_id: transition-LoopNode-*************
2025-07-01 09:44:45 - LoopExecutor - DEBUG - 🚨 Method exists: True
2025-07-01 09:44:45 - LoopExecutor - DEBUG - 🚨 Adding 10-second timeout to chain executor call
2025-07-01 09:44:45 - LoopExecutor - DEBUG - 🚨 About to call method directly...
2025-07-01 09:44:45 - StateManager - DEBUG - Using provided database connections
2025-07-01 09:44:45 - RedisEventListener - INFO - Workflow state manager reference updated
2025-07-01 09:44:45 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-07-01 09:44:45 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-07-01 09:44:46 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-07-01 09:44:46 - StateManager - INFO - WorkflowStateManager initialized
2025-07-01 09:44:46 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1751001474606: ['transition-AgenticAI-1750505490787']
2025-07-01 09:44:46 - StateManager - DEBUG - 🔄 Detected potential loop body transition transition-CombineTextComponent-************* depending on loop node transition-LoopNode-*************
2025-07-01 09:44:46 - StateManager - DEBUG - 🔄 Detected loop body transition transition-CombineTextComponent-************* depending on loop node transition-LoopNode-*************
2025-07-01 09:44:46 - StateManager - DEBUG - 🔄 Loop body transition transition-CombineTextComponent-************* will wait for loop node transition-LoopNode-************* to provide iteration data
2025-07-01 09:44:46 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-*************: ['transition-AgenticAI-*************', 'transition-AgenticAI-*************', 'transition-LoopNode-*************']
2025-07-01 09:44:46 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1751001471087: ['transition-AgenticAI-1750505490787']
2025-07-01 09:44:46 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-1750920624318: ['transition-AgenticAI-1750505490787', 'transition-AgenticAI-1751001471087', 'transition-AgenticAI-1751001474606']
2025-07-01 09:44:46 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1750505490787: ['transition-CombineTextComponent-*************']
2025-07-01 09:44:46 - StateManager - DEBUG - Stored iteration data for key current_iteration: {'current_item': 2, 'iteration_context': {'iteration_index': 1, 'total_iterations': 6, 'loop_config': {'loop_type': 'context_independent', 'aggregation_config': {'type': 'list'}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': False}, 'iteration_source': {'type': 'range', 'data': {'start': 1, 'stop': 7, 'step': 1}, 'batch_size': 1, 'original': {'number_range': {'start': 1, 'end': 6}, 'step': 1}}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'concurrency': {'enabled': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'retry_config': {'max_retries': 1, 'retry_delay': 1.0}, 'iteration_behavior': 'independent', 'iteration_settings': {'parallel_execution': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'error_handling': {'on_iteration_error': 'retry_once', 'include_errors': True}, 'loop_body_transitions': ['transition-CombineTextComponent-*************'], 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-CombineTextComponent-1750920624318'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}}}}
2025-07-01 09:44:46 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-07-01 09:44:46 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 12, corr_id 0dc0ac22-9bda-46bb-8437-3743103ed0a1):
2025-07-01 09:44:46 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dc0ac22-9bda-46bb-8437-3743103ed0a1, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 12, 'workflow_status': 'running'}
2025-07-01 09:44:46 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-07-01 09:44:46 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-07-01 09:44:46 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-07-01 09:44:46 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-07-01 09:44:46 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-07-01 09:44:47 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-07-01 09:44:47 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-07-01 09:44:47 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-07-01 09:44:47 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-07-01 09:44:48 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-07-01 09:44:48 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-07-01 09:44:48 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-07-01 09:44:48 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-07-01 09:44:49 - StateManager - DEBUG - No result found in Redis for transition transition-LoopNode-*************. Trying PostgreSQL next.
2025-07-01 09:44:51 - PostgresManager - DEBUG - No result found for transition transition-LoopNode-************* in correlation 0dc0ac22-9bda-46bb-8437-3743103ed0a1_loop_transition-LoopNode-*************_iteration_1
2025-07-01 09:44:51 - StateManager - DEBUG - No result found in PostgreSQL for transition transition-LoopNode-*************. Trying in-memory next.
2025-07-01 09:44:51 - StateManager - DEBUG - No result found in Redis, PostgreSQL, or memory for transition transition-LoopNode-*************
2025-07-01 09:44:51 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 1)
2025-07-01 09:44:51 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 2)
2025-07-01 09:44:51 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-07-01 09:44:51 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests."}
2025-07-01 09:44:51 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 09:44:51 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.
2025-07-01 09:44:51 - WorkflowUtils - DEBUG - Found result.result: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests. (type: <class 'str'>)
2025-07-01 09:44:51 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 09:44:51 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 09:44:51 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests."}
2025-07-01 09:44:51 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 09:44:51 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.
2025-07-01 09:44:51 - WorkflowUtils - DEBUG - Found result.result: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests. (type: <class 'str'>)
2025-07-01 09:44:51 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 09:44:51 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 09:44:51 - WorkflowUtils - INFO - 🔍 Handle mapping validation: partially_compatible (2/3 compatible)
2025-07-01 09:44:51 - TransitionHandler - INFO - 🔍 Handle validation: partially_compatible (2/3 compatible)
2025-07-01 09:44:51 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests."}
2025-07-01 09:44:51 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 09:44:51 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.
2025-07-01 09:44:51 - WorkflowUtils - DEBUG - Found result.result: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests. (type: <class 'str'>)
2025-07-01 09:44:51 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 09:44:51 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 09:44:51 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → input_2 via path 'result': I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.
2025-07-01 09:44:51 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests."}
2025-07-01 09:44:51 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 09:44:51 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.
2025-07-01 09:44:51 - WorkflowUtils - DEBUG - Found result.result: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests. (type: <class 'str'>)
2025-07-01 09:44:51 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 09:44:51 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 09:44:51 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → input_1 via path 'result': I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.
2025-07-01 09:44:51 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {}
2025-07-01 09:44:51 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: []
2025-07-01 09:44:51 - WorkflowUtils - DEBUG - Could not find result.result or result in source_results for handle current_item
2025-07-01 09:44:51 - WorkflowUtils - DEBUG - Path tracking - Source results structure
2025-07-01 09:44:51 - WorkflowUtils - WARNING - ❌ Handle mapping failed: current_item → main_input (no data found)
2025-07-01 09:44:51 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 2/3 successful
2025-07-01 09:44:51 - WorkflowUtils - WARNING - ⚠️ 1 universal handle mappings failed - this may cause tool execution errors
2025-07-01 09:44:51 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 2/3 successful
2025-07-01 09:44:51 - TransitionHandler - WARNING - ❌ Failed mapping: current_item → main_input (Error: No data found for handle current_item)
2025-07-01 09:44:51 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-07-01 09:44:51 - WorkflowUtils - DEBUG - Filtering out field 'input_1' with null/empty value: None
2025-07-01 09:44:51 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: None
2025-07-01 09:44:51 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-07-01 09:44:51 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-07-01 09:44:51 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-07-01 09:44:51 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-07-01 09:44:51 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-07-01 09:44:51 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-07-01 09:44:51 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-07-01 09:44:51 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-07-01 09:44:51 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 2 fields (11 null/empty fields removed)
2025-07-01 09:44:51 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-07-01 09:44:51 - TransitionHandler - DEBUG - 📌 Added static parameter: separator = \n
2025-07-01 09:44:51 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'input_2': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'input_1': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'num_additional_inputs': '1', 'separator': '\\n'}
2025-07-01 09:44:51 - TransitionHandler - DEBUG - tool Parameters: {'input_2': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'input_1': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'num_additional_inputs': '1', 'separator': '\\n'}
2025-07-01 09:44:51 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'input_2': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'input_1': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'num_additional_inputs': '1', 'separator': '\\n'}
2025-07-01 09:44:51 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 13, corr_id 0dc0ac22-9bda-46bb-8437-3743103ed0a1):
2025-07-01 09:44:51 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dc0ac22-9bda-46bb-8437-3743103ed0a1, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 13, 'workflow_status': 'running'}
2025-07-01 09:44:51 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: d4dc3a66-cf2c-4433-8275-a2f70107303d) using provided producer.
2025-07-01 09:44:51 - NodeExecutor - DEBUG - Added correlation_id 0dc0ac22-9bda-46bb-8437-3743103ed0a1 to payload
2025-07-01 09:44:51 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-07-01 09:44:51 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'input_2': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'input_1': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'num_additional_inputs': '1', 'separator': '\\n'}, 'request_id': 'd4dc3a66-cf2c-4433-8275-a2f70107303d', 'correlation_id': '0dc0ac22-9bda-46bb-8437-3743103ed0a1', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-07-01 09:44:51 - NodeExecutor - DEBUG - Request d4dc3a66-cf2c-4433-8275-a2f70107303d sent successfully using provided producer.
2025-07-01 09:44:51 - NodeExecutor - DEBUG - Waiting indefinitely for result for request d4dc3a66-cf2c-4433-8275-a2f70107303d...
2025-07-01 09:44:52 - NodeExecutor - DEBUG - Result consumer received message: Offset=983
2025-07-01 09:44:52 - NodeExecutor - WARNING - Received result for already completed/cancelled request_id c8b9a3f6-ba45-4c4c-811a-64b43edd983d
2025-07-01 09:44:52 - NodeExecutor - DEBUG - Result consumer received message: Offset=984
2025-07-01 09:44:52 - NodeExecutor - WARNING - Received error response for request_id d4dc3a66-cf2c-4433-8275-a2f70107303d: Error combining text for request_id d4dc3a66-cf2c-4433-8275-a2f70107303d: "Required field 'main_input' not found in parameters"
2025-07-01 09:44:52 - NodeExecutor - ERROR - Error during node execution d4dc3a66-cf2c-4433-8275-a2f70107303d: Node execution failed: Error combining text for request_id d4dc3a66-cf2c-4433-8275-a2f70107303d: "Required field 'main_input' not found in parameters"
Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id d4dc3a66-cf2c-4433-8275-a2f70107303d: "Required field 'main_input' not found in parameters"
2025-07-01 09:44:52 - TransitionHandler - ERROR - Tool execution failed for tool 'CombineTextComponent' (tool_id: 1) in node 'CombineTextComponent' of transition 'transition-CombineTextComponent-*************': Node execution failed: Error combining text for request_id d4dc3a66-cf2c-4433-8275-a2f70107303d: "Required field 'main_input' not found in parameters"Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 492, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 361, in execute_tool
    raise e
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id d4dc3a66-cf2c-4433-8275-a2f70107303d: "Required field 'main_input' not found in parameters"

2025-07-01 09:44:52 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 14, corr_id 0dc0ac22-9bda-46bb-8437-3743103ed0a1):
2025-07-01 09:44:52 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dc0ac22-9bda-46bb-8437-3743103ed0a1, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition faced an error during execution.', 'result': '[ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id d4dc3a66-cf2c-4433-8275-a2f70107303d: "Required field \'main_input\' not found in parameters"', 'status': 'failed', 'sequence': 14, 'workflow_status': 'running'}
2025-07-01 09:44:52 - TransitionHandler - ERROR - Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id d4dc3a66-cf2c-4433-8275-a2f70107303d: "Required field 'main_input' not found in parameters"
2025-07-01 09:44:52 - StateManager - DEBUG - Cleared iteration data for key current_iteration
2025-07-01 09:44:52 - LoopExecutor - ERROR - 🚨 Chain executor call failed with exception: LoopBodyChainExecutor._check_waiting_transitions_ready() takes 2 positional arguments but 4 were given
2025-07-01 09:44:52 - LoopExecutor - ERROR - 🚨 Full traceback: Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_executor.py", line 1890, in _execute_loop_body_chain
    result = await asyncio.wait_for(
             ^^^^^^^^^^^^^^^^^^^^^^^
    ...<8 lines>...
    )
    ^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/tasks.py", line 507, in wait_for
    return await fut
           ^^^^^^^^^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_body_chain_executor.py", line 122, in execute_loop_body_chain
    result = await self._execute_chain_with_isolated_state(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        isolated_state_manager, chain_id, entry_transitions, exit_transitions
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_body_chain_executor.py", line 306, in _execute_chain_with_isolated_state
    newly_ready = self._check_waiting_transitions_ready(
        waiting_transitions, completed_transitions, chain_id
    )
TypeError: LoopBodyChainExecutor._check_waiting_transitions_ready() takes 2 positional arguments but 4 were given

2025-07-01 09:44:52 - LoopExecutor - ERROR - ❌ Loop body chain execution failed for iteration 1: LoopBodyChainExecutor._check_waiting_transitions_ready() takes 2 positional arguments but 4 were given
2025-07-01 09:44:52 - LoopExecutor - ERROR - ❌ Iteration 2 failed: LoopBodyChainExecutor._check_waiting_transitions_ready() takes 2 positional arguments but 4 were given
2025-07-01 09:44:52 - LoopExecutor - INFO - 🔄 Starting iteration 3/6
2025-07-01 09:44:52 - LoopExecutor - DEBUG - 🚨 STEP 4: About to execute loop body chain for iteration 2
2025-07-01 09:44:52 - LoopExecutor - DEBUG - 🚨 About to call _execute_loop_body_chain with loop_body_chain_executor: <app.services.loop_executor.loop_body_chain_executor.LoopBodyChainExecutor object at 0x110dc34d0>
2025-07-01 09:44:52 - LoopExecutor - INFO - 🚨 _execute_loop_body_chain called for iteration 2
2025-07-01 09:44:52 - LoopExecutor - DEBUG - 🔍 Current loop config keys: ['loop_type', 'aggregation_config', 'result_aggregation', 'iteration_source', 'exit_condition', 'concurrency', 'retry_config', 'iteration_behavior', 'iteration_settings', 'error_handling', 'loop_body_transitions', 'loop_body_configuration']
2025-07-01 09:44:52 - LoopExecutor - DEBUG - 🔍 Loop body config: {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-CombineTextComponent-1750920624318'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}
2025-07-01 09:44:52 - LoopExecutor - DEBUG - 🔍 Loop body transitions: ['transition-CombineTextComponent-*************']
2025-07-01 09:44:52 - LoopExecutor - DEBUG - 🚨 About to call loop_body_chain_executor.execute_loop_body_chain with transition_id: transition-LoopNode-*************
2025-07-01 09:44:52 - LoopExecutor - DEBUG - 🚨 Method exists: True
2025-07-01 09:44:52 - LoopExecutor - DEBUG - 🚨 Adding 10-second timeout to chain executor call
2025-07-01 09:44:52 - LoopExecutor - DEBUG - 🚨 About to call method directly...
2025-07-01 09:44:52 - StateManager - DEBUG - Using provided database connections
2025-07-01 09:44:52 - RedisEventListener - INFO - Workflow state manager reference updated
2025-07-01 09:44:52 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-07-01 09:44:52 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-07-01 09:44:53 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-07-01 09:44:53 - StateManager - INFO - WorkflowStateManager initialized
2025-07-01 09:44:53 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1751001474606: ['transition-AgenticAI-1750505490787']
2025-07-01 09:44:53 - StateManager - DEBUG - 🔄 Detected potential loop body transition transition-CombineTextComponent-************* depending on loop node transition-LoopNode-*************
2025-07-01 09:44:53 - StateManager - DEBUG - 🔄 Detected loop body transition transition-CombineTextComponent-************* depending on loop node transition-LoopNode-*************
2025-07-01 09:44:53 - StateManager - DEBUG - 🔄 Loop body transition transition-CombineTextComponent-************* will wait for loop node transition-LoopNode-************* to provide iteration data
2025-07-01 09:44:53 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-*************: ['transition-AgenticAI-*************', 'transition-AgenticAI-*************', 'transition-LoopNode-*************']
2025-07-01 09:44:53 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1751001471087: ['transition-AgenticAI-1750505490787']
2025-07-01 09:44:53 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-1750920624318: ['transition-AgenticAI-1750505490787', 'transition-AgenticAI-1751001471087', 'transition-AgenticAI-1751001474606']
2025-07-01 09:44:53 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1750505490787: ['transition-CombineTextComponent-*************']
2025-07-01 09:44:53 - StateManager - DEBUG - Stored iteration data for key current_iteration: {'current_item': 3, 'iteration_context': {'iteration_index': 2, 'total_iterations': 6, 'loop_config': {'loop_type': 'context_independent', 'aggregation_config': {'type': 'list'}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': False}, 'iteration_source': {'type': 'range', 'data': {'start': 1, 'stop': 7, 'step': 1}, 'batch_size': 1, 'original': {'number_range': {'start': 1, 'end': 6}, 'step': 1}}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'concurrency': {'enabled': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'retry_config': {'max_retries': 1, 'retry_delay': 1.0}, 'iteration_behavior': 'independent', 'iteration_settings': {'parallel_execution': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'error_handling': {'on_iteration_error': 'retry_once', 'include_errors': True}, 'loop_body_transitions': ['transition-CombineTextComponent-*************'], 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-CombineTextComponent-1750920624318'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}}}}
2025-07-01 09:44:53 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-07-01 09:44:53 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 15, corr_id 0dc0ac22-9bda-46bb-8437-3743103ed0a1):
2025-07-01 09:44:53 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dc0ac22-9bda-46bb-8437-3743103ed0a1, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 15, 'workflow_status': 'running'}
2025-07-01 09:44:53 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-07-01 09:44:53 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-07-01 09:44:53 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-07-01 09:44:53 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-07-01 09:44:53 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-07-01 09:44:54 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-07-01 09:44:54 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-07-01 09:44:54 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-07-01 09:44:54 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-07-01 09:44:55 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-07-01 09:44:55 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-07-01 09:44:55 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-07-01 09:44:55 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-07-01 09:44:55 - StateManager - DEBUG - No result found in Redis for transition transition-LoopNode-*************. Trying PostgreSQL next.
2025-07-01 09:44:58 - PostgresManager - DEBUG - No result found for transition transition-LoopNode-************* in correlation 0dc0ac22-9bda-46bb-8437-3743103ed0a1_loop_transition-LoopNode-*************_iteration_2
2025-07-01 09:44:58 - StateManager - DEBUG - No result found in PostgreSQL for transition transition-LoopNode-*************. Trying in-memory next.
2025-07-01 09:44:58 - StateManager - DEBUG - No result found in Redis, PostgreSQL, or memory for transition transition-LoopNode-*************
2025-07-01 09:44:58 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 1)
2025-07-01 09:44:58 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 2)
2025-07-01 09:44:58 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-07-01 09:44:58 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests."}
2025-07-01 09:44:58 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 09:44:58 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.
2025-07-01 09:44:58 - WorkflowUtils - DEBUG - Found result.result: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests. (type: <class 'str'>)
2025-07-01 09:44:58 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 09:44:58 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 09:44:58 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests."}
2025-07-01 09:44:58 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 09:44:58 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.
2025-07-01 09:44:58 - WorkflowUtils - DEBUG - Found result.result: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests. (type: <class 'str'>)
2025-07-01 09:44:58 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 09:44:58 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 09:44:58 - WorkflowUtils - INFO - 🔍 Handle mapping validation: partially_compatible (2/3 compatible)
2025-07-01 09:44:58 - TransitionHandler - INFO - 🔍 Handle validation: partially_compatible (2/3 compatible)
2025-07-01 09:44:58 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests."}
2025-07-01 09:44:58 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 09:44:58 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.
2025-07-01 09:44:58 - WorkflowUtils - DEBUG - Found result.result: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests. (type: <class 'str'>)
2025-07-01 09:44:58 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 09:44:58 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 09:44:58 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → input_2 via path 'result': I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.
2025-07-01 09:44:58 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests."}
2025-07-01 09:44:58 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 09:44:58 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.
2025-07-01 09:44:58 - WorkflowUtils - DEBUG - Found result.result: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests. (type: <class 'str'>)
2025-07-01 09:44:58 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 09:44:58 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 09:44:58 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → input_1 via path 'result': I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.
2025-07-01 09:44:58 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {}
2025-07-01 09:44:58 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: []
2025-07-01 09:44:58 - WorkflowUtils - DEBUG - Could not find result.result or result in source_results for handle current_item
2025-07-01 09:44:58 - WorkflowUtils - DEBUG - Path tracking - Source results structure
2025-07-01 09:44:58 - WorkflowUtils - WARNING - ❌ Handle mapping failed: current_item → main_input (no data found)
2025-07-01 09:44:58 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 2/3 successful
2025-07-01 09:44:58 - WorkflowUtils - WARNING - ⚠️ 1 universal handle mappings failed - this may cause tool execution errors
2025-07-01 09:44:58 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 2/3 successful
2025-07-01 09:44:58 - TransitionHandler - WARNING - ❌ Failed mapping: current_item → main_input (Error: No data found for handle current_item)
2025-07-01 09:44:58 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-07-01 09:44:58 - WorkflowUtils - DEBUG - Filtering out field 'input_1' with null/empty value: None
2025-07-01 09:44:58 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: None
2025-07-01 09:44:58 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-07-01 09:44:58 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-07-01 09:44:58 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-07-01 09:44:58 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-07-01 09:44:58 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-07-01 09:44:58 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-07-01 09:44:58 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-07-01 09:44:58 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-07-01 09:44:58 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 2 fields (11 null/empty fields removed)
2025-07-01 09:44:58 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-07-01 09:44:58 - TransitionHandler - DEBUG - 📌 Added static parameter: separator = \n
2025-07-01 09:44:58 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'input_2': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'input_1': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'num_additional_inputs': '1', 'separator': '\\n'}
2025-07-01 09:44:58 - TransitionHandler - DEBUG - tool Parameters: {'input_2': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'input_1': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'num_additional_inputs': '1', 'separator': '\\n'}
2025-07-01 09:44:58 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'input_2': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'input_1': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'num_additional_inputs': '1', 'separator': '\\n'}
2025-07-01 09:44:58 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 16, corr_id 0dc0ac22-9bda-46bb-8437-3743103ed0a1):
2025-07-01 09:44:58 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dc0ac22-9bda-46bb-8437-3743103ed0a1, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 16, 'workflow_status': 'running'}
2025-07-01 09:44:58 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 7f0aca52-41e0-405c-8326-d974a90a4914) using provided producer.
2025-07-01 09:44:58 - NodeExecutor - DEBUG - Added correlation_id 0dc0ac22-9bda-46bb-8437-3743103ed0a1 to payload
2025-07-01 09:44:58 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-07-01 09:44:58 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'input_2': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'input_1': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'num_additional_inputs': '1', 'separator': '\\n'}, 'request_id': '7f0aca52-41e0-405c-8326-d974a90a4914', 'correlation_id': '0dc0ac22-9bda-46bb-8437-3743103ed0a1', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-07-01 09:44:58 - NodeExecutor - DEBUG - Request 7f0aca52-41e0-405c-8326-d974a90a4914 sent successfully using provided producer.
2025-07-01 09:44:58 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 7f0aca52-41e0-405c-8326-d974a90a4914...
2025-07-01 09:45:02 - StateManager - DEBUG - Cleared iteration data for key current_iteration
2025-07-01 09:45:02 - LoopExecutor - ERROR - 🚨 Chain executor call TIMED OUT after 10 seconds - this confirms a deadlock!
2025-07-01 09:45:02 - LoopExecutor - ERROR - ❌ Loop body chain execution failed for iteration 2: 
2025-07-01 09:45:02 - LoopExecutor - ERROR - ❌ Iteration 3 failed: 
2025-07-01 09:45:02 - LoopExecutor - INFO - 🔄 Starting iteration 4/6
2025-07-01 09:45:02 - LoopExecutor - DEBUG - 🚨 STEP 4: About to execute loop body chain for iteration 3
2025-07-01 09:45:02 - LoopExecutor - DEBUG - 🚨 About to call _execute_loop_body_chain with loop_body_chain_executor: <app.services.loop_executor.loop_body_chain_executor.LoopBodyChainExecutor object at 0x110dc34d0>
2025-07-01 09:45:02 - LoopExecutor - INFO - 🚨 _execute_loop_body_chain called for iteration 3
2025-07-01 09:45:02 - LoopExecutor - DEBUG - 🔍 Current loop config keys: ['loop_type', 'aggregation_config', 'result_aggregation', 'iteration_source', 'exit_condition', 'concurrency', 'retry_config', 'iteration_behavior', 'iteration_settings', 'error_handling', 'loop_body_transitions', 'loop_body_configuration']
2025-07-01 09:45:02 - LoopExecutor - DEBUG - 🔍 Loop body config: {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-CombineTextComponent-1750920624318'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}
2025-07-01 09:45:02 - LoopExecutor - DEBUG - 🔍 Loop body transitions: ['transition-CombineTextComponent-*************']
2025-07-01 09:45:02 - LoopExecutor - DEBUG - 🚨 About to call loop_body_chain_executor.execute_loop_body_chain with transition_id: transition-LoopNode-*************
2025-07-01 09:45:02 - LoopExecutor - DEBUG - 🚨 Method exists: True
2025-07-01 09:45:02 - LoopExecutor - DEBUG - 🚨 Adding 10-second timeout to chain executor call
2025-07-01 09:45:02 - LoopExecutor - DEBUG - 🚨 About to call method directly...
2025-07-01 09:45:02 - StateManager - DEBUG - Using provided database connections
2025-07-01 09:45:02 - RedisEventListener - INFO - Workflow state manager reference updated
2025-07-01 09:45:02 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-07-01 09:45:02 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-07-01 09:45:03 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-07-01 09:45:03 - StateManager - INFO - WorkflowStateManager initialized
2025-07-01 09:45:03 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1751001474606: ['transition-AgenticAI-1750505490787']
2025-07-01 09:45:03 - StateManager - DEBUG - 🔄 Detected potential loop body transition transition-CombineTextComponent-************* depending on loop node transition-LoopNode-*************
2025-07-01 09:45:03 - StateManager - DEBUG - 🔄 Detected loop body transition transition-CombineTextComponent-************* depending on loop node transition-LoopNode-*************
2025-07-01 09:45:03 - StateManager - DEBUG - 🔄 Loop body transition transition-CombineTextComponent-************* will wait for loop node transition-LoopNode-************* to provide iteration data
2025-07-01 09:45:03 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-*************: ['transition-AgenticAI-*************', 'transition-AgenticAI-*************', 'transition-LoopNode-*************']
2025-07-01 09:45:03 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1751001471087: ['transition-AgenticAI-1750505490787']
2025-07-01 09:45:03 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-1750920624318: ['transition-AgenticAI-1750505490787', 'transition-AgenticAI-1751001471087', 'transition-AgenticAI-1751001474606']
2025-07-01 09:45:03 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1750505490787: ['transition-CombineTextComponent-*************']
2025-07-01 09:45:03 - StateManager - DEBUG - Stored iteration data for key current_iteration: {'current_item': 4, 'iteration_context': {'iteration_index': 3, 'total_iterations': 6, 'loop_config': {'loop_type': 'context_independent', 'aggregation_config': {'type': 'list'}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': False}, 'iteration_source': {'type': 'range', 'data': {'start': 1, 'stop': 7, 'step': 1}, 'batch_size': 1, 'original': {'number_range': {'start': 1, 'end': 6}, 'step': 1}}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'concurrency': {'enabled': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'retry_config': {'max_retries': 1, 'retry_delay': 1.0}, 'iteration_behavior': 'independent', 'iteration_settings': {'parallel_execution': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'error_handling': {'on_iteration_error': 'retry_once', 'include_errors': True}, 'loop_body_transitions': ['transition-CombineTextComponent-*************'], 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-CombineTextComponent-1750920624318'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}}}}
2025-07-01 09:45:03 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-07-01 09:45:03 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 17, corr_id 0dc0ac22-9bda-46bb-8437-3743103ed0a1):
2025-07-01 09:45:03 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dc0ac22-9bda-46bb-8437-3743103ed0a1, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 17, 'workflow_status': 'running'}
2025-07-01 09:45:03 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-07-01 09:45:03 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-07-01 09:45:03 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-07-01 09:45:03 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-07-01 09:45:03 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-07-01 09:45:04 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-07-01 09:45:04 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-07-01 09:45:04 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-07-01 09:45:04 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-07-01 09:45:04 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-07-01 09:45:04 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-07-01 09:45:04 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-07-01 09:45:04 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-07-01 09:45:05 - StateManager - DEBUG - No result found in Redis for transition transition-LoopNode-*************. Trying PostgreSQL next.
2025-07-01 09:45:07 - PostgresManager - DEBUG - No result found for transition transition-LoopNode-************* in correlation 0dc0ac22-9bda-46bb-8437-3743103ed0a1_loop_transition-LoopNode-*************_iteration_3
2025-07-01 09:45:08 - StateManager - DEBUG - No result found in PostgreSQL for transition transition-LoopNode-*************. Trying in-memory next.
2025-07-01 09:45:08 - StateManager - DEBUG - No result found in Redis, PostgreSQL, or memory for transition transition-LoopNode-*************
2025-07-01 09:45:08 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 1)
2025-07-01 09:45:08 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 2)
2025-07-01 09:45:08 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-07-01 09:45:08 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests."}
2025-07-01 09:45:08 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 09:45:08 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.
2025-07-01 09:45:08 - WorkflowUtils - DEBUG - Found result.result: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests. (type: <class 'str'>)
2025-07-01 09:45:08 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 09:45:08 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 09:45:08 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests."}
2025-07-01 09:45:08 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 09:45:08 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.
2025-07-01 09:45:08 - WorkflowUtils - DEBUG - Found result.result: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests. (type: <class 'str'>)
2025-07-01 09:45:08 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 09:45:08 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 09:45:08 - WorkflowUtils - INFO - 🔍 Handle mapping validation: partially_compatible (2/3 compatible)
2025-07-01 09:45:08 - TransitionHandler - INFO - 🔍 Handle validation: partially_compatible (2/3 compatible)
2025-07-01 09:45:08 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests."}
2025-07-01 09:45:08 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 09:45:08 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.
2025-07-01 09:45:08 - WorkflowUtils - DEBUG - Found result.result: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests. (type: <class 'str'>)
2025-07-01 09:45:08 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 09:45:08 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 09:45:08 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → input_2 via path 'result': I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.
2025-07-01 09:45:08 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests."}
2025-07-01 09:45:08 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 09:45:08 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.
2025-07-01 09:45:08 - WorkflowUtils - DEBUG - Found result.result: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests. (type: <class 'str'>)
2025-07-01 09:45:08 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 09:45:08 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 09:45:08 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → input_1 via path 'result': I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.
2025-07-01 09:45:08 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {}
2025-07-01 09:45:08 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: []
2025-07-01 09:45:08 - WorkflowUtils - DEBUG - Could not find result.result or result in source_results for handle current_item
2025-07-01 09:45:08 - WorkflowUtils - DEBUG - Path tracking - Source results structure
2025-07-01 09:45:08 - WorkflowUtils - WARNING - ❌ Handle mapping failed: current_item → main_input (no data found)
2025-07-01 09:45:08 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 2/3 successful
2025-07-01 09:45:08 - WorkflowUtils - WARNING - ⚠️ 1 universal handle mappings failed - this may cause tool execution errors
2025-07-01 09:45:08 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 2/3 successful
2025-07-01 09:45:08 - TransitionHandler - WARNING - ❌ Failed mapping: current_item → main_input (Error: No data found for handle current_item)
2025-07-01 09:45:08 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-07-01 09:45:08 - WorkflowUtils - DEBUG - Filtering out field 'input_1' with null/empty value: None
2025-07-01 09:45:08 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: None
2025-07-01 09:45:08 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-07-01 09:45:08 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-07-01 09:45:08 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-07-01 09:45:08 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-07-01 09:45:08 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-07-01 09:45:08 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-07-01 09:45:08 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-07-01 09:45:08 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-07-01 09:45:08 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 2 fields (11 null/empty fields removed)
2025-07-01 09:45:08 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-07-01 09:45:08 - TransitionHandler - DEBUG - 📌 Added static parameter: separator = \n
2025-07-01 09:45:08 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'input_2': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'input_1': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'num_additional_inputs': '1', 'separator': '\\n'}
2025-07-01 09:45:08 - TransitionHandler - DEBUG - tool Parameters: {'input_2': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'input_1': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'num_additional_inputs': '1', 'separator': '\\n'}
2025-07-01 09:45:08 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'input_2': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'input_1': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'num_additional_inputs': '1', 'separator': '\\n'}
2025-07-01 09:45:08 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 18, corr_id 0dc0ac22-9bda-46bb-8437-3743103ed0a1):
2025-07-01 09:45:08 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dc0ac22-9bda-46bb-8437-3743103ed0a1, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 18, 'workflow_status': 'running'}
2025-07-01 09:45:08 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 6f96d075-9f3a-4dc7-a157-4ad617e0c0db) using provided producer.
2025-07-01 09:45:08 - NodeExecutor - DEBUG - Added correlation_id 0dc0ac22-9bda-46bb-8437-3743103ed0a1 to payload
2025-07-01 09:45:08 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-07-01 09:45:08 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'input_2': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'input_1': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'num_additional_inputs': '1', 'separator': '\\n'}, 'request_id': '6f96d075-9f3a-4dc7-a157-4ad617e0c0db', 'correlation_id': '0dc0ac22-9bda-46bb-8437-3743103ed0a1', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-07-01 09:45:08 - NodeExecutor - DEBUG - Request 6f96d075-9f3a-4dc7-a157-4ad617e0c0db sent successfully using provided producer.
2025-07-01 09:45:08 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 6f96d075-9f3a-4dc7-a157-4ad617e0c0db...
2025-07-01 09:45:09 - NodeExecutor - DEBUG - Result consumer received message: Offset=985
2025-07-01 09:45:09 - NodeExecutor - WARNING - Received result for already completed/cancelled request_id 7f0aca52-41e0-405c-8326-d974a90a4914
2025-07-01 09:45:09 - NodeExecutor - DEBUG - Result consumer received message: Offset=986
2025-07-01 09:45:09 - NodeExecutor - WARNING - Received error response for request_id 6f96d075-9f3a-4dc7-a157-4ad617e0c0db: Error combining text for request_id 6f96d075-9f3a-4dc7-a157-4ad617e0c0db: "Required field 'main_input' not found in parameters"
2025-07-01 09:45:09 - NodeExecutor - ERROR - Error during node execution 6f96d075-9f3a-4dc7-a157-4ad617e0c0db: Node execution failed: Error combining text for request_id 6f96d075-9f3a-4dc7-a157-4ad617e0c0db: "Required field 'main_input' not found in parameters"
Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id 6f96d075-9f3a-4dc7-a157-4ad617e0c0db: "Required field 'main_input' not found in parameters"
2025-07-01 09:45:09 - TransitionHandler - ERROR - Tool execution failed for tool 'CombineTextComponent' (tool_id: 1) in node 'CombineTextComponent' of transition 'transition-CombineTextComponent-*************': Node execution failed: Error combining text for request_id 6f96d075-9f3a-4dc7-a157-4ad617e0c0db: "Required field 'main_input' not found in parameters"Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 492, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 361, in execute_tool
    raise e
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id 6f96d075-9f3a-4dc7-a157-4ad617e0c0db: "Required field 'main_input' not found in parameters"

2025-07-01 09:45:09 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 19, corr_id 0dc0ac22-9bda-46bb-8437-3743103ed0a1):
2025-07-01 09:45:09 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dc0ac22-9bda-46bb-8437-3743103ed0a1, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition faced an error during execution.', 'result': '[ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 6f96d075-9f3a-4dc7-a157-4ad617e0c0db: "Required field \'main_input\' not found in parameters"', 'status': 'failed', 'sequence': 19, 'workflow_status': 'running'}
2025-07-01 09:45:09 - TransitionHandler - ERROR - Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 6f96d075-9f3a-4dc7-a157-4ad617e0c0db: "Required field 'main_input' not found in parameters"
2025-07-01 09:45:09 - StateManager - DEBUG - Cleared iteration data for key current_iteration
2025-07-01 09:45:09 - LoopExecutor - ERROR - 🚨 Chain executor call failed with exception: LoopBodyChainExecutor._check_waiting_transitions_ready() takes 2 positional arguments but 4 were given
2025-07-01 09:45:09 - LoopExecutor - ERROR - 🚨 Full traceback: Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_executor.py", line 1890, in _execute_loop_body_chain
    result = await asyncio.wait_for(
             ^^^^^^^^^^^^^^^^^^^^^^^
    ...<8 lines>...
    )
    ^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/tasks.py", line 507, in wait_for
    return await fut
           ^^^^^^^^^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_body_chain_executor.py", line 122, in execute_loop_body_chain
    result = await self._execute_chain_with_isolated_state(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        isolated_state_manager, chain_id, entry_transitions, exit_transitions
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_body_chain_executor.py", line 306, in _execute_chain_with_isolated_state
    newly_ready = self._check_waiting_transitions_ready(
        waiting_transitions, completed_transitions, chain_id
    )
TypeError: LoopBodyChainExecutor._check_waiting_transitions_ready() takes 2 positional arguments but 4 were given

2025-07-01 09:45:09 - LoopExecutor - ERROR - ❌ Loop body chain execution failed for iteration 3: LoopBodyChainExecutor._check_waiting_transitions_ready() takes 2 positional arguments but 4 were given
2025-07-01 09:45:09 - LoopExecutor - ERROR - ❌ Iteration 4 failed: LoopBodyChainExecutor._check_waiting_transitions_ready() takes 2 positional arguments but 4 were given
2025-07-01 09:45:09 - LoopExecutor - INFO - 🔄 Starting iteration 5/6
2025-07-01 09:45:09 - LoopExecutor - DEBUG - 🚨 STEP 4: About to execute loop body chain for iteration 4
2025-07-01 09:45:09 - LoopExecutor - DEBUG - 🚨 About to call _execute_loop_body_chain with loop_body_chain_executor: <app.services.loop_executor.loop_body_chain_executor.LoopBodyChainExecutor object at 0x110dc34d0>
2025-07-01 09:45:09 - LoopExecutor - INFO - 🚨 _execute_loop_body_chain called for iteration 4
2025-07-01 09:45:09 - LoopExecutor - DEBUG - 🔍 Current loop config keys: ['loop_type', 'aggregation_config', 'result_aggregation', 'iteration_source', 'exit_condition', 'concurrency', 'retry_config', 'iteration_behavior', 'iteration_settings', 'error_handling', 'loop_body_transitions', 'loop_body_configuration']
2025-07-01 09:45:09 - LoopExecutor - DEBUG - 🔍 Loop body config: {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-CombineTextComponent-1750920624318'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}
2025-07-01 09:45:09 - LoopExecutor - DEBUG - 🔍 Loop body transitions: ['transition-CombineTextComponent-*************']
2025-07-01 09:45:09 - LoopExecutor - DEBUG - 🚨 About to call loop_body_chain_executor.execute_loop_body_chain with transition_id: transition-LoopNode-*************
2025-07-01 09:45:09 - LoopExecutor - DEBUG - 🚨 Method exists: True
2025-07-01 09:45:09 - LoopExecutor - DEBUG - 🚨 Adding 10-second timeout to chain executor call
2025-07-01 09:45:09 - LoopExecutor - DEBUG - 🚨 About to call method directly...
2025-07-01 09:45:09 - StateManager - DEBUG - Using provided database connections
2025-07-01 09:45:09 - RedisEventListener - INFO - Workflow state manager reference updated
2025-07-01 09:45:09 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-07-01 09:45:09 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-07-01 09:45:10 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-07-01 09:45:10 - StateManager - INFO - WorkflowStateManager initialized
2025-07-01 09:45:10 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1751001474606: ['transition-AgenticAI-1750505490787']
2025-07-01 09:45:10 - StateManager - DEBUG - 🔄 Detected potential loop body transition transition-CombineTextComponent-************* depending on loop node transition-LoopNode-*************
2025-07-01 09:45:10 - StateManager - DEBUG - 🔄 Detected loop body transition transition-CombineTextComponent-************* depending on loop node transition-LoopNode-*************
2025-07-01 09:45:10 - StateManager - DEBUG - 🔄 Loop body transition transition-CombineTextComponent-************* will wait for loop node transition-LoopNode-************* to provide iteration data
2025-07-01 09:45:10 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-*************: ['transition-AgenticAI-*************', 'transition-AgenticAI-*************', 'transition-LoopNode-*************']
2025-07-01 09:45:10 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1751001471087: ['transition-AgenticAI-1750505490787']
2025-07-01 09:45:10 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-1750920624318: ['transition-AgenticAI-1750505490787', 'transition-AgenticAI-1751001471087', 'transition-AgenticAI-1751001474606']
2025-07-01 09:45:10 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1750505490787: ['transition-CombineTextComponent-*************']
2025-07-01 09:45:10 - StateManager - DEBUG - Stored iteration data for key current_iteration: {'current_item': 5, 'iteration_context': {'iteration_index': 4, 'total_iterations': 6, 'loop_config': {'loop_type': 'context_independent', 'aggregation_config': {'type': 'list'}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': False}, 'iteration_source': {'type': 'range', 'data': {'start': 1, 'stop': 7, 'step': 1}, 'batch_size': 1, 'original': {'number_range': {'start': 1, 'end': 6}, 'step': 1}}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'concurrency': {'enabled': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'retry_config': {'max_retries': 1, 'retry_delay': 1.0}, 'iteration_behavior': 'independent', 'iteration_settings': {'parallel_execution': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'error_handling': {'on_iteration_error': 'retry_once', 'include_errors': True}, 'loop_body_transitions': ['transition-CombineTextComponent-*************'], 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-CombineTextComponent-1750920624318'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}}}}
2025-07-01 09:45:10 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-07-01 09:45:10 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 20, corr_id 0dc0ac22-9bda-46bb-8437-3743103ed0a1):
2025-07-01 09:45:10 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dc0ac22-9bda-46bb-8437-3743103ed0a1, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 20, 'workflow_status': 'running'}
2025-07-01 09:45:10 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-07-01 09:45:10 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-07-01 09:45:10 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-07-01 09:45:10 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-07-01 09:45:10 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-07-01 09:45:10 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-07-01 09:45:10 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-07-01 09:45:10 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-07-01 09:45:10 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-07-01 09:45:11 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-07-01 09:45:11 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-07-01 09:45:11 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-07-01 09:45:11 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-07-01 09:45:12 - StateManager - DEBUG - No result found in Redis for transition transition-LoopNode-*************. Trying PostgreSQL next.
2025-07-01 09:45:14 - PostgresManager - DEBUG - No result found for transition transition-LoopNode-************* in correlation 0dc0ac22-9bda-46bb-8437-3743103ed0a1_loop_transition-LoopNode-*************_iteration_4
2025-07-01 09:45:15 - StateManager - DEBUG - No result found in PostgreSQL for transition transition-LoopNode-*************. Trying in-memory next.
2025-07-01 09:45:15 - StateManager - DEBUG - No result found in Redis, PostgreSQL, or memory for transition transition-LoopNode-*************
2025-07-01 09:45:15 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 1)
2025-07-01 09:45:15 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 2)
2025-07-01 09:45:15 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-07-01 09:45:15 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests."}
2025-07-01 09:45:15 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 09:45:15 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.
2025-07-01 09:45:15 - WorkflowUtils - DEBUG - Found result.result: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests. (type: <class 'str'>)
2025-07-01 09:45:15 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 09:45:15 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 09:45:15 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests."}
2025-07-01 09:45:15 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 09:45:15 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.
2025-07-01 09:45:15 - WorkflowUtils - DEBUG - Found result.result: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests. (type: <class 'str'>)
2025-07-01 09:45:15 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 09:45:15 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 09:45:15 - WorkflowUtils - INFO - 🔍 Handle mapping validation: partially_compatible (2/3 compatible)
2025-07-01 09:45:15 - TransitionHandler - INFO - 🔍 Handle validation: partially_compatible (2/3 compatible)
2025-07-01 09:45:15 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests."}
2025-07-01 09:45:15 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 09:45:15 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.
2025-07-01 09:45:15 - WorkflowUtils - DEBUG - Found result.result: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests. (type: <class 'str'>)
2025-07-01 09:45:15 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 09:45:15 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 09:45:15 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → input_2 via path 'result': I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.
2025-07-01 09:45:15 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests."}
2025-07-01 09:45:15 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 09:45:15 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.
2025-07-01 09:45:15 - WorkflowUtils - DEBUG - Found result.result: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests. (type: <class 'str'>)
2025-07-01 09:45:15 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 09:45:15 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 09:45:15 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → input_1 via path 'result': I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.
2025-07-01 09:45:15 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {}
2025-07-01 09:45:15 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: []
2025-07-01 09:45:15 - WorkflowUtils - DEBUG - Could not find result.result or result in source_results for handle current_item
2025-07-01 09:45:15 - WorkflowUtils - DEBUG - Path tracking - Source results structure
2025-07-01 09:45:15 - WorkflowUtils - WARNING - ❌ Handle mapping failed: current_item → main_input (no data found)
2025-07-01 09:45:15 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 2/3 successful
2025-07-01 09:45:15 - WorkflowUtils - WARNING - ⚠️ 1 universal handle mappings failed - this may cause tool execution errors
2025-07-01 09:45:15 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 2/3 successful
2025-07-01 09:45:15 - TransitionHandler - WARNING - ❌ Failed mapping: current_item → main_input (Error: No data found for handle current_item)
2025-07-01 09:45:15 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-07-01 09:45:15 - WorkflowUtils - DEBUG - Filtering out field 'input_1' with null/empty value: None
2025-07-01 09:45:15 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: None
2025-07-01 09:45:15 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-07-01 09:45:15 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-07-01 09:45:15 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-07-01 09:45:15 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-07-01 09:45:15 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-07-01 09:45:15 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-07-01 09:45:15 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-07-01 09:45:15 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-07-01 09:45:15 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 2 fields (11 null/empty fields removed)
2025-07-01 09:45:15 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-07-01 09:45:15 - TransitionHandler - DEBUG - 📌 Added static parameter: separator = \n
2025-07-01 09:45:15 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'input_2': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'input_1': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'num_additional_inputs': '1', 'separator': '\\n'}
2025-07-01 09:45:15 - TransitionHandler - DEBUG - tool Parameters: {'input_2': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'input_1': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'num_additional_inputs': '1', 'separator': '\\n'}
2025-07-01 09:45:15 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'input_2': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'input_1': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'num_additional_inputs': '1', 'separator': '\\n'}
2025-07-01 09:45:15 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 21, corr_id 0dc0ac22-9bda-46bb-8437-3743103ed0a1):
2025-07-01 09:45:15 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dc0ac22-9bda-46bb-8437-3743103ed0a1, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 21, 'workflow_status': 'running'}
2025-07-01 09:45:15 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 2c27d7fc-2c3f-4e1c-97d8-0b38f1832b8b) using provided producer.
2025-07-01 09:45:15 - NodeExecutor - DEBUG - Added correlation_id 0dc0ac22-9bda-46bb-8437-3743103ed0a1 to payload
2025-07-01 09:45:15 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-07-01 09:45:15 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'input_2': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'input_1': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'num_additional_inputs': '1', 'separator': '\\n'}, 'request_id': '2c27d7fc-2c3f-4e1c-97d8-0b38f1832b8b', 'correlation_id': '0dc0ac22-9bda-46bb-8437-3743103ed0a1', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-07-01 09:45:15 - NodeExecutor - DEBUG - Request 2c27d7fc-2c3f-4e1c-97d8-0b38f1832b8b sent successfully using provided producer.
2025-07-01 09:45:15 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 2c27d7fc-2c3f-4e1c-97d8-0b38f1832b8b...
2025-07-01 09:45:16 - NodeExecutor - DEBUG - Result consumer received message: Offset=987
2025-07-01 09:45:16 - NodeExecutor - WARNING - Received error response for request_id 2c27d7fc-2c3f-4e1c-97d8-0b38f1832b8b: Error combining text for request_id 2c27d7fc-2c3f-4e1c-97d8-0b38f1832b8b: "Required field 'main_input' not found in parameters"
2025-07-01 09:45:16 - NodeExecutor - ERROR - Error during node execution 2c27d7fc-2c3f-4e1c-97d8-0b38f1832b8b: Node execution failed: Error combining text for request_id 2c27d7fc-2c3f-4e1c-97d8-0b38f1832b8b: "Required field 'main_input' not found in parameters"
Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id 2c27d7fc-2c3f-4e1c-97d8-0b38f1832b8b: "Required field 'main_input' not found in parameters"
2025-07-01 09:45:16 - TransitionHandler - ERROR - Tool execution failed for tool 'CombineTextComponent' (tool_id: 1) in node 'CombineTextComponent' of transition 'transition-CombineTextComponent-*************': Node execution failed: Error combining text for request_id 2c27d7fc-2c3f-4e1c-97d8-0b38f1832b8b: "Required field 'main_input' not found in parameters"Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 492, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 361, in execute_tool
    raise e
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id 2c27d7fc-2c3f-4e1c-97d8-0b38f1832b8b: "Required field 'main_input' not found in parameters"

2025-07-01 09:45:16 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 22, corr_id 0dc0ac22-9bda-46bb-8437-3743103ed0a1):
2025-07-01 09:45:16 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dc0ac22-9bda-46bb-8437-3743103ed0a1, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition faced an error during execution.', 'result': '[ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 2c27d7fc-2c3f-4e1c-97d8-0b38f1832b8b: "Required field \'main_input\' not found in parameters"', 'status': 'failed', 'sequence': 22, 'workflow_status': 'running'}
2025-07-01 09:45:16 - TransitionHandler - ERROR - Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 2c27d7fc-2c3f-4e1c-97d8-0b38f1832b8b: "Required field 'main_input' not found in parameters"
2025-07-01 09:45:16 - StateManager - DEBUG - Cleared iteration data for key current_iteration
2025-07-01 09:45:16 - LoopExecutor - ERROR - 🚨 Chain executor call failed with exception: LoopBodyChainExecutor._check_waiting_transitions_ready() takes 2 positional arguments but 4 were given
2025-07-01 09:45:16 - LoopExecutor - ERROR - 🚨 Full traceback: Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_executor.py", line 1890, in _execute_loop_body_chain
    result = await asyncio.wait_for(
             ^^^^^^^^^^^^^^^^^^^^^^^
    ...<8 lines>...
    )
    ^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/tasks.py", line 507, in wait_for
    return await fut
           ^^^^^^^^^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_body_chain_executor.py", line 122, in execute_loop_body_chain
    result = await self._execute_chain_with_isolated_state(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        isolated_state_manager, chain_id, entry_transitions, exit_transitions
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_body_chain_executor.py", line 306, in _execute_chain_with_isolated_state
    newly_ready = self._check_waiting_transitions_ready(
        waiting_transitions, completed_transitions, chain_id
    )
TypeError: LoopBodyChainExecutor._check_waiting_transitions_ready() takes 2 positional arguments but 4 were given

2025-07-01 09:45:16 - LoopExecutor - ERROR - ❌ Loop body chain execution failed for iteration 4: LoopBodyChainExecutor._check_waiting_transitions_ready() takes 2 positional arguments but 4 were given
2025-07-01 09:45:16 - LoopExecutor - ERROR - ❌ Iteration 5 failed: LoopBodyChainExecutor._check_waiting_transitions_ready() takes 2 positional arguments but 4 were given
2025-07-01 09:45:16 - LoopExecutor - INFO - 🔄 Starting iteration 6/6
2025-07-01 09:45:16 - LoopExecutor - DEBUG - 🚨 STEP 4: About to execute loop body chain for iteration 5
2025-07-01 09:45:16 - LoopExecutor - DEBUG - 🚨 About to call _execute_loop_body_chain with loop_body_chain_executor: <app.services.loop_executor.loop_body_chain_executor.LoopBodyChainExecutor object at 0x110dc34d0>
2025-07-01 09:45:16 - LoopExecutor - INFO - 🚨 _execute_loop_body_chain called for iteration 5
2025-07-01 09:45:16 - LoopExecutor - DEBUG - 🔍 Current loop config keys: ['loop_type', 'aggregation_config', 'result_aggregation', 'iteration_source', 'exit_condition', 'concurrency', 'retry_config', 'iteration_behavior', 'iteration_settings', 'error_handling', 'loop_body_transitions', 'loop_body_configuration']
2025-07-01 09:45:16 - LoopExecutor - DEBUG - 🔍 Loop body config: {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-CombineTextComponent-1750920624318'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}
2025-07-01 09:45:16 - LoopExecutor - DEBUG - 🔍 Loop body transitions: ['transition-CombineTextComponent-*************']
2025-07-01 09:45:16 - LoopExecutor - DEBUG - 🚨 About to call loop_body_chain_executor.execute_loop_body_chain with transition_id: transition-LoopNode-*************
2025-07-01 09:45:16 - LoopExecutor - DEBUG - 🚨 Method exists: True
2025-07-01 09:45:16 - LoopExecutor - DEBUG - 🚨 Adding 10-second timeout to chain executor call
2025-07-01 09:45:16 - LoopExecutor - DEBUG - 🚨 About to call method directly...
2025-07-01 09:45:16 - StateManager - DEBUG - Using provided database connections
2025-07-01 09:45:16 - RedisEventListener - INFO - Workflow state manager reference updated
2025-07-01 09:45:16 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-07-01 09:45:16 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-07-01 09:45:16 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-07-01 09:45:16 - StateManager - INFO - WorkflowStateManager initialized
2025-07-01 09:45:16 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1751001474606: ['transition-AgenticAI-1750505490787']
2025-07-01 09:45:16 - StateManager - DEBUG - 🔄 Detected potential loop body transition transition-CombineTextComponent-************* depending on loop node transition-LoopNode-*************
2025-07-01 09:45:16 - StateManager - DEBUG - 🔄 Detected loop body transition transition-CombineTextComponent-************* depending on loop node transition-LoopNode-*************
2025-07-01 09:45:16 - StateManager - DEBUG - 🔄 Loop body transition transition-CombineTextComponent-************* will wait for loop node transition-LoopNode-************* to provide iteration data
2025-07-01 09:45:16 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-*************: ['transition-AgenticAI-*************', 'transition-AgenticAI-*************', 'transition-LoopNode-*************']
2025-07-01 09:45:16 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1751001471087: ['transition-AgenticAI-1750505490787']
2025-07-01 09:45:16 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-1750920624318: ['transition-AgenticAI-1750505490787', 'transition-AgenticAI-1751001471087', 'transition-AgenticAI-1751001474606']
2025-07-01 09:45:16 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1750505490787: ['transition-CombineTextComponent-*************']
2025-07-01 09:45:16 - StateManager - DEBUG - Stored iteration data for key current_iteration: {'current_item': 6, 'iteration_context': {'iteration_index': 5, 'total_iterations': 6, 'loop_config': {'loop_type': 'context_independent', 'aggregation_config': {'type': 'list'}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': False}, 'iteration_source': {'type': 'range', 'data': {'start': 1, 'stop': 7, 'step': 1}, 'batch_size': 1, 'original': {'number_range': {'start': 1, 'end': 6}, 'step': 1}}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'concurrency': {'enabled': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'retry_config': {'max_retries': 1, 'retry_delay': 1.0}, 'iteration_behavior': 'independent', 'iteration_settings': {'parallel_execution': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'error_handling': {'on_iteration_error': 'retry_once', 'include_errors': True}, 'loop_body_transitions': ['transition-CombineTextComponent-*************'], 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-CombineTextComponent-1750920624318'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}}}}
2025-07-01 09:45:16 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-07-01 09:45:16 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 23, corr_id 0dc0ac22-9bda-46bb-8437-3743103ed0a1):
2025-07-01 09:45:16 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dc0ac22-9bda-46bb-8437-3743103ed0a1, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 23, 'workflow_status': 'running'}
2025-07-01 09:45:16 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-07-01 09:45:16 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-07-01 09:45:16 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-07-01 09:45:16 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-07-01 09:45:16 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-07-01 09:45:17 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-07-01 09:45:17 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-07-01 09:45:17 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-07-01 09:45:17 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-07-01 09:45:18 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-07-01 09:45:18 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-07-01 09:45:18 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-07-01 09:45:18 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-07-01 09:45:19 - StateManager - DEBUG - No result found in Redis for transition transition-LoopNode-*************. Trying PostgreSQL next.
2025-07-01 09:45:21 - PostgresManager - DEBUG - No result found for transition transition-LoopNode-************* in correlation 0dc0ac22-9bda-46bb-8437-3743103ed0a1_loop_transition-LoopNode-*************_iteration_5
2025-07-01 09:45:22 - StateManager - DEBUG - No result found in PostgreSQL for transition transition-LoopNode-*************. Trying in-memory next.
2025-07-01 09:45:22 - StateManager - DEBUG - No result found in Redis, PostgreSQL, or memory for transition transition-LoopNode-*************
2025-07-01 09:45:22 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 1)
2025-07-01 09:45:22 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 2)
2025-07-01 09:45:22 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-07-01 09:45:22 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests."}
2025-07-01 09:45:22 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 09:45:22 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.
2025-07-01 09:45:22 - WorkflowUtils - DEBUG - Found result.result: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests. (type: <class 'str'>)
2025-07-01 09:45:22 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 09:45:22 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 09:45:22 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests."}
2025-07-01 09:45:22 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 09:45:22 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.
2025-07-01 09:45:22 - WorkflowUtils - DEBUG - Found result.result: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests. (type: <class 'str'>)
2025-07-01 09:45:22 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 09:45:22 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 09:45:22 - WorkflowUtils - INFO - 🔍 Handle mapping validation: partially_compatible (2/3 compatible)
2025-07-01 09:45:22 - TransitionHandler - INFO - 🔍 Handle validation: partially_compatible (2/3 compatible)
2025-07-01 09:45:22 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests."}
2025-07-01 09:45:22 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 09:45:22 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.
2025-07-01 09:45:22 - WorkflowUtils - DEBUG - Found result.result: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests. (type: <class 'str'>)
2025-07-01 09:45:22 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 09:45:22 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 09:45:22 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → input_2 via path 'result': I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.
2025-07-01 09:45:22 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests."}
2025-07-01 09:45:22 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 09:45:22 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.
2025-07-01 09:45:22 - WorkflowUtils - DEBUG - Found result.result: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests. (type: <class 'str'>)
2025-07-01 09:45:22 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 09:45:22 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 09:45:22 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → input_1 via path 'result': I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.
2025-07-01 09:45:22 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {}
2025-07-01 09:45:22 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: []
2025-07-01 09:45:22 - WorkflowUtils - DEBUG - Could not find result.result or result in source_results for handle current_item
2025-07-01 09:45:22 - WorkflowUtils - DEBUG - Path tracking - Source results structure
2025-07-01 09:45:22 - WorkflowUtils - WARNING - ❌ Handle mapping failed: current_item → main_input (no data found)
2025-07-01 09:45:22 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 2/3 successful
2025-07-01 09:45:22 - WorkflowUtils - WARNING - ⚠️ 1 universal handle mappings failed - this may cause tool execution errors
2025-07-01 09:45:22 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 2/3 successful
2025-07-01 09:45:22 - TransitionHandler - WARNING - ❌ Failed mapping: current_item → main_input (Error: No data found for handle current_item)
2025-07-01 09:45:22 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-07-01 09:45:22 - WorkflowUtils - DEBUG - Filtering out field 'input_1' with null/empty value: None
2025-07-01 09:45:22 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: None
2025-07-01 09:45:22 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-07-01 09:45:22 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-07-01 09:45:22 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-07-01 09:45:22 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-07-01 09:45:22 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-07-01 09:45:22 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-07-01 09:45:22 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-07-01 09:45:22 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-07-01 09:45:22 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 2 fields (11 null/empty fields removed)
2025-07-01 09:45:22 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-07-01 09:45:22 - TransitionHandler - DEBUG - 📌 Added static parameter: separator = \n
2025-07-01 09:45:22 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'input_2': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'input_1': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'num_additional_inputs': '1', 'separator': '\\n'}
2025-07-01 09:45:22 - TransitionHandler - DEBUG - tool Parameters: {'input_2': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'input_1': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'num_additional_inputs': '1', 'separator': '\\n'}
2025-07-01 09:45:22 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'input_2': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'input_1': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'num_additional_inputs': '1', 'separator': '\\n'}
2025-07-01 09:45:22 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 24, corr_id 0dc0ac22-9bda-46bb-8437-3743103ed0a1):
2025-07-01 09:45:22 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dc0ac22-9bda-46bb-8437-3743103ed0a1, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 24, 'workflow_status': 'running'}
2025-07-01 09:45:22 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 99f99558-8ee0-4b46-9eb8-9cdbf971d6d3) using provided producer.
2025-07-01 09:45:22 - NodeExecutor - DEBUG - Added correlation_id 0dc0ac22-9bda-46bb-8437-3743103ed0a1 to payload
2025-07-01 09:45:22 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-07-01 09:45:22 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'input_2': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'input_1': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'num_additional_inputs': '1', 'separator': '\\n'}, 'request_id': '99f99558-8ee0-4b46-9eb8-9cdbf971d6d3', 'correlation_id': '0dc0ac22-9bda-46bb-8437-3743103ed0a1', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-07-01 09:45:22 - NodeExecutor - DEBUG - Request 99f99558-8ee0-4b46-9eb8-9cdbf971d6d3 sent successfully using provided producer.
2025-07-01 09:45:22 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 99f99558-8ee0-4b46-9eb8-9cdbf971d6d3...
2025-07-01 09:45:22 - NodeExecutor - DEBUG - Result consumer received message: Offset=988
2025-07-01 09:45:22 - NodeExecutor - WARNING - Received error response for request_id 99f99558-8ee0-4b46-9eb8-9cdbf971d6d3: Error combining text for request_id 99f99558-8ee0-4b46-9eb8-9cdbf971d6d3: "Required field 'main_input' not found in parameters"
2025-07-01 09:45:22 - NodeExecutor - ERROR - Error during node execution 99f99558-8ee0-4b46-9eb8-9cdbf971d6d3: Node execution failed: Error combining text for request_id 99f99558-8ee0-4b46-9eb8-9cdbf971d6d3: "Required field 'main_input' not found in parameters"
Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id 99f99558-8ee0-4b46-9eb8-9cdbf971d6d3: "Required field 'main_input' not found in parameters"
2025-07-01 09:45:22 - TransitionHandler - ERROR - Tool execution failed for tool 'CombineTextComponent' (tool_id: 1) in node 'CombineTextComponent' of transition 'transition-CombineTextComponent-*************': Node execution failed: Error combining text for request_id 99f99558-8ee0-4b46-9eb8-9cdbf971d6d3: "Required field 'main_input' not found in parameters"Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 492, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 361, in execute_tool
    raise e
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id 99f99558-8ee0-4b46-9eb8-9cdbf971d6d3: "Required field 'main_input' not found in parameters"

2025-07-01 09:45:22 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 25, corr_id 0dc0ac22-9bda-46bb-8437-3743103ed0a1):
2025-07-01 09:45:22 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dc0ac22-9bda-46bb-8437-3743103ed0a1, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition faced an error during execution.', 'result': '[ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 99f99558-8ee0-4b46-9eb8-9cdbf971d6d3: "Required field \'main_input\' not found in parameters"', 'status': 'failed', 'sequence': 25, 'workflow_status': 'running'}
2025-07-01 09:45:22 - TransitionHandler - ERROR - Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 99f99558-8ee0-4b46-9eb8-9cdbf971d6d3: "Required field 'main_input' not found in parameters"
2025-07-01 09:45:22 - StateManager - DEBUG - Cleared iteration data for key current_iteration
2025-07-01 09:45:22 - LoopExecutor - ERROR - 🚨 Chain executor call failed with exception: LoopBodyChainExecutor._check_waiting_transitions_ready() takes 2 positional arguments but 4 were given
2025-07-01 09:45:22 - LoopExecutor - ERROR - 🚨 Full traceback: Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_executor.py", line 1890, in _execute_loop_body_chain
    result = await asyncio.wait_for(
             ^^^^^^^^^^^^^^^^^^^^^^^
    ...<8 lines>...
    )
    ^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/tasks.py", line 507, in wait_for
    return await fut
           ^^^^^^^^^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_body_chain_executor.py", line 122, in execute_loop_body_chain
    result = await self._execute_chain_with_isolated_state(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        isolated_state_manager, chain_id, entry_transitions, exit_transitions
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_body_chain_executor.py", line 306, in _execute_chain_with_isolated_state
    newly_ready = self._check_waiting_transitions_ready(
        waiting_transitions, completed_transitions, chain_id
    )
TypeError: LoopBodyChainExecutor._check_waiting_transitions_ready() takes 2 positional arguments but 4 were given

2025-07-01 09:45:22 - LoopExecutor - ERROR - ❌ Loop body chain execution failed for iteration 5: LoopBodyChainExecutor._check_waiting_transitions_ready() takes 2 positional arguments but 4 were given
2025-07-01 09:45:22 - LoopExecutor - ERROR - ❌ Iteration 6 failed: LoopBodyChainExecutor._check_waiting_transitions_ready() takes 2 positional arguments but 4 were given
2025-07-01 09:45:22 - LoopExecutor - INFO - 🔄 CALLING NEW AGGREGATION METHOD with 0 iteration results
2025-07-01 09:45:22 - LoopExecutor - DEBUG - 🔄 Aggregating 0 iteration results using aggregation_type: collect_all
2025-07-01 09:45:22 - LoopExecutor - DEBUG - 🔍 Extracted 0 data items from iterations
2025-07-01 09:45:22 - LoopExecutor - DEBUG - 🔍 Sample extracted data: None
2025-07-01 09:45:22 - LoopExecutor - INFO - 🔄 NEW AGGREGATION METHOD RETURNED: []
2025-07-01 09:45:22 - LoopExecutor - INFO - 📊 Loop execution completed with metadata: {'success': True, 'final_results': [], 'iteration_count': 0, 'total_iterations': 6}
2025-07-01 09:45:22 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 26, corr_id 0dc0ac22-9bda-46bb-8437-3743103ed0a1):
2025-07-01 09:45:22 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dc0ac22-9bda-46bb-8437-3743103ed0a1, response: {'status': 'loop_completed', 'metadata': {'success': True, 'final_results': [], 'iteration_count': 0, 'total_iterations': 6}, 'transition_id': 'transition-LoopNode-*************', 'sequence': 26, 'workflow_status': 'running'}
2025-07-01 09:45:22 - LoopExecutor - INFO - ✅ Loop node execution completed for transition: transition-LoopNode-*************
2025-07-01 09:45:22 - TransitionHandler - DEBUG - 🗑️ Unregistered loop executor for transition: transition-LoopNode-*************
2025-07-01 09:45:22 - LoopExecutor - DEBUG - 🗑️ Unregistered loop executor from transition handler for transition: transition-LoopNode-*************
2025-07-01 09:45:22 - TransitionHandler - INFO - Execution result from loop executor: {
  "final_results": []
}
2025-07-01 09:45:22 - TransitionHandler - INFO - Checking execution result for errors: {
  "final_results": []
}
2025-07-01 09:45:22 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 27, corr_id 0dc0ac22-9bda-46bb-8437-3743103ed0a1):
2025-07-01 09:45:22 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dc0ac22-9bda-46bb-8437-3743103ed0a1, response: {'transition_id': 'transition-LoopNode-*************', 'node_id': 'LoopNode', 'tool_name': 'LoopNode', 'message': 'Transition Result received.', 'result': {'final_results': []}, 'status': 'completed', 'sequence': 27, 'workflow_status': 'running', 'approval_required': False}
2025-07-01 09:45:22 - TransitionHandler - INFO - 🔄 Handling loop completion for transition: transition-LoopNode-*************
2025-07-01 09:45:22 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in memory: {'final_results': []}
2025-07-01 09:45:23 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-LoopNode-*************'
2025-07-01 09:45:23 - RedisManager - DEBUG - Set key 'result:transition-LoopNode-*************' with TTL of 300 seconds
2025-07-01 09:45:23 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-07-01 09:45:23 - StateManager - INFO - Marked transition transition-LoopNode-************* as completed (was_pending=False, was_waiting=False)
2025-07-01 09:45:23 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-*************'}, completed={'transition-AgenticAI-*************', 'loop_iteration_transition-LoopNode-*************_0', 'transition-LoopNode-*************', 'transition-AgenticAI-*************', 'loop_iteration_0', 'backup_transition-LoopNode-*************_iteration_0', 'current_iteration'}
2025-07-01 09:45:23 - TransitionHandler - DEBUG - 🔍 Detected exit transition: transition-MergeDataComponent-************* (has final/aggregated indicators)
2025-07-01 09:45:23 - TransitionHandler - DEBUG - 🔍 Auto-detected loop body transition: transition-CombineTextComponent-************* (has current_item/iteration indicators)
2025-07-01 09:45:23 - TransitionHandler - INFO - 🔍 Auto-detected loop body transitions: ['transition-CombineTextComponent-*************']
2025-07-01 09:45:23 - TransitionHandler - DEBUG - 🔍 Loop completion filtering - Loop config: {'iteration_behavior': 'independent', 'iteration_source': {'number_range': {'start': 1, 'end': 6}, 'step': 1}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'iteration_settings': {'parallel_execution': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': False}, 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-CombineTextComponent-1750920624318'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}, 'error_handling': {'on_iteration_error': 'retry_once', 'include_errors': True}}
2025-07-01 09:45:23 - TransitionHandler - DEBUG - 🔍 Loop body transitions to filter: ['transition-CombineTextComponent-*************']
2025-07-01 09:45:23 - TransitionHandler - DEBUG - 🔍 Output data configs: [{'to_transition_id': 'transition-MergeDataComponent-*************', 'target_node_id': 'Merge Data', 'data_type': 'string', 'output_handle_registry': {'handle_mappings': [{'handle_id': 'final_results', 'result_path': 'final_results', 'edge_id': 'reactflow__edge-LoopNode-*************final_results-MergeDataComponent-*************main_input'}]}}, {'to_transition_id': 'transition-CombineTextComponent-*************', 'target_node_id': 'Combine Text', 'data_type': 'string', 'output_handle_registry': {'handle_mappings': [{'handle_id': 'current_item', 'result_path': 'current_item', 'edge_id': 'reactflow__edge-LoopNode-*************current_item-CombineTextComponent-*************main_input'}]}}]
2025-07-01 09:45:23 - TransitionHandler - DEBUG - ✅ Adding exit transition 'transition-MergeDataComponent-*************' to next transitions
2025-07-01 09:45:23 - TransitionHandler - DEBUG - 🚫 Skipping loop body transition 'transition-CombineTextComponent-*************' - should have been executed internally by loop executor
2025-07-01 09:45:23 - TransitionHandler - INFO - ✅ Loop completion handled for transition-LoopNode-*************. Next transitions: ['transition-MergeDataComponent-*************']
2025-07-01 09:45:23 - TransitionHandler - INFO - Completed transition transition-LoopNode-************* in 55.38 seconds
2025-07-01 09:45:23 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 28, corr_id 0dc0ac22-9bda-46bb-8437-3743103ed0a1):
2025-07-01 09:45:23 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dc0ac22-9bda-46bb-8437-3743103ed0a1, response: {'result': 'Completed transition in 55.38 seconds', 'message': 'Transition completed in 55.38 seconds', 'transition_id': 'transition-LoopNode-*************', 'status': 'time_logged', 'sequence': 28, 'workflow_status': 'running'}
2025-07-01 09:45:23 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-LoopNode-*************: ['transition-MergeDataComponent-*************']
2025-07-01 09:45:23 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 1
2025-07-01 09:45:23 - EnhancedWorkflowEngine - DEBUG - Results: [['transition-MergeDataComponent-*************']]
2025-07-01 09:45:23 - EnhancedWorkflowEngine - DEBUG - 🔄 Orchestration engine received result for transition-LoopNode-*************: ['transition-MergeDataComponent-*************']
2025-07-01 09:45:23 - EnhancedWorkflowEngine - DEBUG - 🔄 Result type: <class 'list'>, is_list: True
2025-07-01 09:45:23 - EnhancedWorkflowEngine - INFO - Transition transition-LoopNode-************* completed successfully: 1 next transitions
2025-07-01 09:45:23 - EnhancedWorkflowEngine - DEBUG - 🔄 Added to next_transitions_to_execute: ['transition-MergeDataComponent-*************']
2025-07-01 09:45:23 - TransitionHandler - INFO - Resolved next transitions (direct transition IDs): ['transition-MergeDataComponent-*************']
2025-07-01 09:45:23 - EnhancedWorkflowEngine - INFO - Adding transition transition-MergeDataComponent-************* to pending (all dependencies met)
2025-07-01 09:45:23 - StateManager - DEBUG - Workflow active: {'transition-MergeDataComponent-*************'}
2025-07-01 09:45:24 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:0dc0ac22-9bda-46bb-8437-3743103ed0a1'
2025-07-01 09:45:24 - RedisManager - DEBUG - Set key 'workflow_state:0dc0ac22-9bda-46bb-8437-3743103ed0a1' with TTL of 600 seconds
2025-07-01 09:45:24 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 0dc0ac22-9bda-46bb-8437-3743103ed0a1. Will be archived to PostgreSQL when Redis key expires.
2025-07-01 09:45:24 - StateManager - DEBUG - Checking waiting transitions: {'transition-CombineTextComponent-*************'}
2025-07-01 09:45:24 - StateManager - DEBUG - Transition transition-CombineTextComponent-************* is now ready (dependencies met: ['transition-AgenticAI-*************', 'transition-AgenticAI-*************', 'transition-LoopNode-*************'])
2025-07-01 09:45:24 - StateManager - INFO - Moved transitions from waiting to pending: {'transition-CombineTextComponent-*************'}
2025-07-01 09:45:24 - StateManager - DEBUG - Updated waiting=set(), pending={'transition-CombineTextComponent-*************', 'transition-MergeDataComponent-*************'}
2025-07-01 09:45:24 - StateManager - INFO - Cleared 2 pending transitions: {'transition-CombineTextComponent-*************', 'transition-MergeDataComponent-*************'}
2025-07-01 09:45:24 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-07-01 09:45:24 - StateManager - INFO - Terminated: False
2025-07-01 09:45:24 - StateManager - INFO - Pending transitions (0): []
2025-07-01 09:45:24 - StateManager - INFO - Waiting transitions (0): []
2025-07-01 09:45:24 - StateManager - INFO - Completed transitions (7): ['backup_transition-LoopNode-*************_iteration_0', 'current_iteration', 'loop_iteration_0', 'loop_iteration_transition-LoopNode-*************_0', 'transition-AgenticAI-*************', 'transition-AgenticAI-*************', 'transition-LoopNode-*************']
2025-07-01 09:45:24 - StateManager - INFO - Results stored for 7 transitions
2025-07-01 09:45:24 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-07-01 09:45:24 - StateManager - INFO - Workflow status: inactive
2025-07-01 09:45:24 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-07-01 09:45:24 - StateManager - INFO - Workflow status: inactive
2025-07-01 09:45:24 - StateManager - INFO - Workflow paused: False
2025-07-01 09:45:24 - StateManager - INFO - ==============================
2025-07-01 09:45:24 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-07-01 09:45:24 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 29, corr_id 0dc0ac22-9bda-46bb-8437-3743103ed0a1):
2025-07-01 09:45:24 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dc0ac22-9bda-46bb-8437-3743103ed0a1, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 29, 'workflow_status': 'running'}
2025-07-01 09:45:24 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-07-01 09:45:24 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-07-01 09:45:24 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-07-01 09:45:24 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-07-01 09:45:24 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-07-01 09:45:25 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-07-01 09:45:25 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-07-01 09:45:25 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-07-01 09:45:25 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-07-01 09:45:26 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-07-01 09:45:26 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-07-01 09:45:26 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-07-01 09:45:26 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-07-01 09:45:27 - StateManager - DEBUG - Retrieved result for transition transition-LoopNode-************* from Redis
2025-07-01 09:45:27 - StateManager - DEBUG - Extracted results for 1 tools in transition transition-LoopNode-*************
2025-07-01 09:45:27 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-07-01 09:45:27 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 1)
2025-07-01 09:45:27 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 2)
2025-07-01 09:45:27 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-07-01 09:45:27 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests."}
2025-07-01 09:45:27 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 09:45:27 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.
2025-07-01 09:45:27 - WorkflowUtils - DEBUG - Found result.result: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests. (type: <class 'str'>)
2025-07-01 09:45:27 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 09:45:27 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 09:45:27 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests."}
2025-07-01 09:45:27 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 09:45:27 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.
2025-07-01 09:45:27 - WorkflowUtils - DEBUG - Found result.result: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests. (type: <class 'str'>)
2025-07-01 09:45:27 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 09:45:27 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 09:45:27 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'final_results': []}
2025-07-01 09:45:27 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['final_results']
2025-07-01 09:45:27 - WorkflowUtils - DEBUG - Could not find result.result or result in source_results for handle current_item
2025-07-01 09:45:27 - WorkflowUtils - DEBUG - Path tracking - Source results structure
2025-07-01 09:45:27 - WorkflowUtils - INFO - 🔍 Handle mapping validation: partially_compatible (2/3 compatible)
2025-07-01 09:45:27 - TransitionHandler - INFO - 🔍 Handle validation: partially_compatible (2/3 compatible)
2025-07-01 09:45:27 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests."}
2025-07-01 09:45:27 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 09:45:27 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.
2025-07-01 09:45:27 - WorkflowUtils - DEBUG - Found result.result: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests. (type: <class 'str'>)
2025-07-01 09:45:27 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 09:45:27 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 09:45:27 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → input_2 via path 'result': I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.
2025-07-01 09:45:27 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests."}
2025-07-01 09:45:27 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 09:45:27 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.
2025-07-01 09:45:27 - WorkflowUtils - DEBUG - Found result.result: I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests. (type: <class 'str'>)
2025-07-01 09:45:27 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 09:45:27 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 09:45:27 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → input_1 via path 'result': I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.
2025-07-01 09:45:27 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'final_results': []}
2025-07-01 09:45:27 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['final_results']
2025-07-01 09:45:27 - WorkflowUtils - DEBUG - Could not find result.result or result in source_results for handle current_item
2025-07-01 09:45:27 - WorkflowUtils - DEBUG - Path tracking - Source results structure
2025-07-01 09:45:27 - WorkflowUtils - WARNING - ❌ Handle mapping failed: current_item → main_input (no data found)
2025-07-01 09:45:27 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 2/3 successful
2025-07-01 09:45:27 - WorkflowUtils - WARNING - ⚠️ 1 universal handle mappings failed - this may cause tool execution errors
2025-07-01 09:45:27 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 2/3 successful
2025-07-01 09:45:27 - TransitionHandler - WARNING - ❌ Failed mapping: current_item → main_input (Error: No data found for handle current_item)
2025-07-01 09:45:27 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-07-01 09:45:27 - WorkflowUtils - DEBUG - Filtering out field 'input_1' with null/empty value: None
2025-07-01 09:45:27 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: None
2025-07-01 09:45:27 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-07-01 09:45:27 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-07-01 09:45:27 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-07-01 09:45:27 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-07-01 09:45:27 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-07-01 09:45:27 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-07-01 09:45:27 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-07-01 09:45:27 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-07-01 09:45:27 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 2 fields (11 null/empty fields removed)
2025-07-01 09:45:27 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-07-01 09:45:27 - TransitionHandler - DEBUG - 📌 Added static parameter: separator = \n
2025-07-01 09:45:27 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'input_2': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'input_1': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'num_additional_inputs': '1', 'separator': '\\n'}
2025-07-01 09:45:27 - TransitionHandler - DEBUG - tool Parameters: {'input_2': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'input_1': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'num_additional_inputs': '1', 'separator': '\\n'}
2025-07-01 09:45:27 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'input_2': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'input_1': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'num_additional_inputs': '1', 'separator': '\\n'}
2025-07-01 09:45:27 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 30, corr_id 0dc0ac22-9bda-46bb-8437-3743103ed0a1):
2025-07-01 09:45:27 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dc0ac22-9bda-46bb-8437-3743103ed0a1, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 30, 'workflow_status': 'running'}
2025-07-01 09:45:27 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 88a78598-22da-4ac9-93d0-7a73f4e32845) using provided producer.
2025-07-01 09:45:27 - NodeExecutor - DEBUG - Added correlation_id 0dc0ac22-9bda-46bb-8437-3743103ed0a1 to payload
2025-07-01 09:45:27 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-************* to payload
2025-07-01 09:45:27 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'input_2': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'input_1': "I'm here to help you learn about nature! Is there a specific aspect of nature you are interested in, such as ecosystems, wildlife, plants, or conservation efforts? Let me know, and I can provide more information tailored to your interests.", 'num_additional_inputs': '1', 'separator': '\\n'}, 'request_id': '88a78598-22da-4ac9-93d0-7a73f4e32845', 'correlation_id': '0dc0ac22-9bda-46bb-8437-3743103ed0a1', 'transition_id': 'transition-CombineTextComponent-*************'}
2025-07-01 09:45:27 - NodeExecutor - DEBUG - Request 88a78598-22da-4ac9-93d0-7a73f4e32845 sent successfully using provided producer.
2025-07-01 09:45:27 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 88a78598-22da-4ac9-93d0-7a73f4e32845...
2025-07-01 09:45:27 - TransitionHandler - INFO - Starting parallel execution of transition: transition-MergeDataComponent-*************
2025-07-01 09:45:27 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 31, corr_id 0dc0ac22-9bda-46bb-8437-3743103ed0a1):
2025-07-01 09:45:27 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dc0ac22-9bda-46bb-8437-3743103ed0a1, response: {'result': 'Starting execution of transition: transition-MergeDataComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-MergeDataComponent-*************', 'status': 'started', 'sequence': 31, 'workflow_status': 'running'}
2025-07-01 09:45:27 - TransitionHandler - EXECUTE - Transition 'transition-MergeDataComponent-*************' (type=standard, execution_type=Components)
2025-07-01 09:45:27 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-07-01 09:45:27 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-MergeDataComponent-*************
2025-07-01 09:45:27 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-07-01 09:45:27 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-07-01 09:45:28 - StateManager - DEBUG - Retrieved result for transition transition-LoopNode-************* from Redis
2025-07-01 09:45:28 - StateManager - DEBUG - Extracted results for 1 tools in transition transition-LoopNode-*************
2025-07-01 09:45:28 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-07-01 09:45:28 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-07-01 09:45:28 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_results': {'final_results': []}
2025-07-01 09:45:28 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['final_results']
2025-07-01 09:45:28 - WorkflowUtils - DEBUG - Path tracking - Found handle 'final_results' directly in source_results
2025-07-01 09:45:28 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-07-01 09:45:28 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-07-01 09:45:28 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_results': {'final_results': []}
2025-07-01 09:45:28 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['final_results']
2025-07-01 09:45:28 - WorkflowUtils - DEBUG - Path tracking - Found handle 'final_results' directly in source_results
2025-07-01 09:45:28 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_results → main_input via path 'final_results': []
2025-07-01 09:45:28 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-07-01 09:45:28 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-07-01 09:45:28 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-07-01 09:45:28 - WorkflowUtils - DEBUG - Filtering out field 'output_key_1' with null/empty value: 
2025-07-01 09:45:28 - WorkflowUtils - DEBUG - Filtering out field 'output_key_2' with null/empty value: 
2025-07-01 09:45:28 - WorkflowUtils - DEBUG - Filtering out field 'output_key_3' with null/empty value: 
2025-07-01 09:45:28 - WorkflowUtils - DEBUG - Filtering out field 'output_key_4' with null/empty value: 
2025-07-01 09:45:28 - WorkflowUtils - DEBUG - Filtering out field 'output_key_5' with null/empty value: 
2025-07-01 09:45:28 - WorkflowUtils - DEBUG - Filtering out field 'output_key_6' with null/empty value: 
2025-07-01 09:45:28 - WorkflowUtils - DEBUG - Filtering out field 'output_key_7' with null/empty value: 
2025-07-01 09:45:28 - WorkflowUtils - DEBUG - Filtering out field 'output_key_8' with null/empty value: 
2025-07-01 09:45:28 - WorkflowUtils - DEBUG - Filtering out field 'output_key_9' with null/empty value: 
2025-07-01 09:45:28 - WorkflowUtils - DEBUG - Filtering out field 'output_key_10' with null/empty value: 
2025-07-01 09:45:28 - WorkflowUtils - DEBUG - Filtering out field 'output_key_11' with null/empty value: 
2025-07-01 09:45:28 - WorkflowUtils - DEBUG - Filtering out field 'input_1' with empty collection: {}
2025-07-01 09:45:28 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with empty collection: {}
2025-07-01 09:45:28 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with empty collection: {}
2025-07-01 09:45:28 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with empty collection: {}
2025-07-01 09:45:28 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with empty collection: {}
2025-07-01 09:45:28 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with empty collection: {}
2025-07-01 09:45:28 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with empty collection: {}
2025-07-01 09:45:28 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with empty collection: {}
2025-07-01 09:45:28 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with empty collection: {}
2025-07-01 09:45:28 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with empty collection: {}
2025-07-01 09:45:28 - WorkflowUtils - INFO - 🧹 Parameter filtering: 24 → 2 fields (22 null/empty fields removed)
2025-07-01 09:45:28 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-07-01 09:45:28 - TransitionHandler - DEBUG - 📌 Added static parameter: merge_strategy = Aggregate
2025-07-01 09:45:28 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'main_input': [], 'num_additional_inputs': '1', 'merge_strategy': 'Aggregate'}
2025-07-01 09:45:28 - TransitionHandler - DEBUG - tool Parameters: {'main_input': [], 'num_additional_inputs': '1', 'merge_strategy': 'Aggregate'}
2025-07-01 09:45:28 - TransitionHandler - INFO - Invoking tool 'MergeDataComponent' (tool_id: 1) for node 'MergeDataComponent' in transition 'transition-MergeDataComponent-*************' with parameters: {'main_input': [], 'num_additional_inputs': '1', 'merge_strategy': 'Aggregate'}
2025-07-01 09:45:28 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 32, corr_id 0dc0ac22-9bda-46bb-8437-3743103ed0a1):
2025-07-01 09:45:28 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dc0ac22-9bda-46bb-8437-3743103ed0a1, response: {'transition_id': 'transition-MergeDataComponent-*************', 'node_id': 'MergeDataComponent', 'tool_name': 'MergeDataComponent', 'message': 'Connecting to server', 'result': 'Connecting to server MergeDataComponent', 'status': 'connecting', 'sequence': 32, 'workflow_status': 'running'}
2025-07-01 09:45:28 - NodeExecutor - INFO - Executing tool 'MergeDataComponent' via Kafka (request_id: a8bdb290-4107-47e1-9673-6e0f1930152d) using provided producer.
2025-07-01 09:45:28 - NodeExecutor - DEBUG - Added correlation_id 0dc0ac22-9bda-46bb-8437-3743103ed0a1 to payload
2025-07-01 09:45:28 - NodeExecutor - DEBUG - Added transition_id transition-MergeDataComponent-************* to payload
2025-07-01 09:45:28 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'MergeDataComponent', 'tool_parameters': {'main_input': [], 'num_additional_inputs': '1', 'merge_strategy': 'Aggregate'}, 'request_id': 'a8bdb290-4107-47e1-9673-6e0f1930152d', 'correlation_id': '0dc0ac22-9bda-46bb-8437-3743103ed0a1', 'transition_id': 'transition-MergeDataComponent-*************'}
2025-07-01 09:45:28 - NodeExecutor - DEBUG - Request a8bdb290-4107-47e1-9673-6e0f1930152d sent successfully using provided producer.
2025-07-01 09:45:28 - NodeExecutor - DEBUG - Waiting indefinitely for result for request a8bdb290-4107-47e1-9673-6e0f1930152d...
2025-07-01 09:45:28 - NodeExecutor - DEBUG - Result consumer received message: Offset=989
2025-07-01 09:45:28 - NodeExecutor - WARNING - Received error response for request_id 88a78598-22da-4ac9-93d0-7a73f4e32845: Error combining text for request_id 88a78598-22da-4ac9-93d0-7a73f4e32845: "Required field 'main_input' not found in parameters"
2025-07-01 09:45:28 - NodeExecutor - DEBUG - Result consumer received message: Offset=990
2025-07-01 09:45:28 - NodeExecutor - DEBUG - Received valid result for request_id a8bdb290-4107-47e1-9673-6e0f1930152d
2025-07-01 09:45:28 - NodeExecutor - ERROR - Error during node execution 88a78598-22da-4ac9-93d0-7a73f4e32845: Node execution failed: Error combining text for request_id 88a78598-22da-4ac9-93d0-7a73f4e32845: "Required field 'main_input' not found in parameters"
Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id 88a78598-22da-4ac9-93d0-7a73f4e32845: "Required field 'main_input' not found in parameters"
2025-07-01 09:45:28 - TransitionHandler - ERROR - Tool execution failed for tool 'CombineTextComponent' (tool_id: 1) in node 'CombineTextComponent' of transition 'transition-CombineTextComponent-*************': Node execution failed: Error combining text for request_id 88a78598-22da-4ac9-93d0-7a73f4e32845: "Required field 'main_input' not found in parameters"Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 492, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 361, in execute_tool
    raise e
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id 88a78598-22da-4ac9-93d0-7a73f4e32845: "Required field 'main_input' not found in parameters"

2025-07-01 09:45:28 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 33, corr_id 0dc0ac22-9bda-46bb-8437-3743103ed0a1):
2025-07-01 09:45:28 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dc0ac22-9bda-46bb-8437-3743103ed0a1, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition faced an error during execution.', 'result': '[ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 88a78598-22da-4ac9-93d0-7a73f4e32845: "Required field \'main_input\' not found in parameters"', 'status': 'failed', 'sequence': 33, 'workflow_status': 'running'}
2025-07-01 09:45:28 - TransitionHandler - ERROR - Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 88a78598-22da-4ac9-93d0-7a73f4e32845: "Required field 'main_input' not found in parameters"
2025-07-01 09:45:28 - NodeExecutor - INFO - Result received for request a8bdb290-4107-47e1-9673-6e0f1930152d.
2025-07-01 09:45:28 - TransitionHandler - INFO - Execution result from Components executor: []
2025-07-01 09:45:28 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 34, corr_id 0dc0ac22-9bda-46bb-8437-3743103ed0a1):
2025-07-01 09:45:28 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dc0ac22-9bda-46bb-8437-3743103ed0a1, response: {'transition_id': 'transition-MergeDataComponent-*************', 'node_id': 'MergeDataComponent', 'tool_name': 'MergeDataComponent', 'message': 'Transition Result received.', 'result': [], 'status': 'completed', 'sequence': 34, 'workflow_status': 'running', 'approval_required': False}
2025-07-01 09:45:28 - StateManager - DEBUG - Stored result for transition transition-MergeDataComponent-************* in memory: {'MergeDataComponent': {'transition_id': 'transition-MergeDataComponent-*************', 'node_id': 'MergeDataComponent', 'tool_name': 'MergeDataComponent', 'result': {'result': []}, 'status': 'completed', 'timestamp': 1751343328.638062}}
2025-07-01 09:45:29 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-MergeDataComponent-*************'
2025-07-01 09:45:29 - RedisManager - DEBUG - Set key 'result:transition-MergeDataComponent-*************' with TTL of 300 seconds
2025-07-01 09:45:29 - StateManager - DEBUG - Stored result for transition transition-MergeDataComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-07-01 09:45:29 - StateManager - INFO - Marked transition transition-MergeDataComponent-************* as completed (was_pending=False, was_waiting=False)
2025-07-01 09:45:29 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-AgenticAI-*************', 'loop_iteration_transition-LoopNode-*************_0', 'transition-LoopNode-*************', 'transition-AgenticAI-*************', 'loop_iteration_0', 'backup_transition-LoopNode-*************_iteration_0', 'current_iteration', 'transition-MergeDataComponent-*************'}
2025-07-01 09:45:29 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-MergeDataComponent-*************
2025-07-01 09:45:29 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'list'>
2025-07-01 09:45:29 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-07-01 09:45:29 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'list'>
2025-07-01 09:45:29 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-MergeDataComponent-*************:
2025-07-01 09:45:29 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-07-01 09:45:29 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-07-01 09:45:29 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-MergeDataComponent-*************, returning empty list
2025-07-01 09:45:29 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-MergeDataComponent-*************
2025-07-01 09:45:29 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 0
2025-07-01 09:45:29 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: []
2025-07-01 09:45:29 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-07-01 09:45:29 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-07-01 09:45:29 - TransitionHandler - DEBUG - 🔗 Final next_transitions: []
2025-07-01 09:45:29 - TransitionHandler - INFO - Completed transition transition-MergeDataComponent-************* in 2.24 seconds
2025-07-01 09:45:29 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 35, corr_id 0dc0ac22-9bda-46bb-8437-3743103ed0a1):
2025-07-01 09:45:29 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dc0ac22-9bda-46bb-8437-3743103ed0a1, response: {'result': 'Completed transition in 2.24 seconds', 'message': 'Transition completed in 2.24 seconds', 'transition_id': 'transition-MergeDataComponent-*************', 'status': 'time_logged', 'sequence': 35, 'workflow_status': 'running'}
2025-07-01 09:45:29 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-MergeDataComponent-*************: []
2025-07-01 09:45:29 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 0
2025-07-01 09:45:29 - EnhancedWorkflowEngine - DEBUG - Results: [Exception('Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 88a78598-22da-4ac9-93d0-7a73f4e32845: "Required field \'main_input\' not found in parameters"'), []]
2025-07-01 09:45:29 - EnhancedWorkflowEngine - DEBUG - 🔄 Orchestration engine received result for transition-CombineTextComponent-*************: Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 88a78598-22da-4ac9-93d0-7a73f4e32845: "Required field 'main_input' not found in parameters"
2025-07-01 09:45:29 - EnhancedWorkflowEngine - DEBUG - 🔄 Result type: <class 'Exception'>, is_list: False
2025-07-01 09:45:29 - EnhancedWorkflowEngine - ERROR - Error in execution of transition transition-CombineTextComponent-*************: Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 88a78598-22da-4ac9-93d0-7a73f4e32845: "Required field 'main_input' not found in parameters"
2025-07-01 09:45:29 - EnhancedWorkflowEngine - ERROR - Traceback for transition transition-CombineTextComponent-*************: NoneType: None

2025-07-01 09:45:29 - EnhancedWorkflowEngine - ERROR - An unexpected error occurred during parallel execution of standard transitions: Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 88a78598-22da-4ac9-93d0-7a73f4e32845: "Required field 'main_input' not found in parameters"
2025-07-01 09:45:29 - EnhancedWorkflowEngine - ERROR - Traceback for unexpected error: Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 492, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 361, in execute_tool
    raise e
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id 88a78598-22da-4ac9-93d0-7a73f4e32845: "Required field 'main_input' not found in parameters"

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 204, in _execute_transition_with_tracking
    result = await self._execute_standard_or_reflection_transition(transition)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 703, in _execute_standard_or_reflection_transition
    raise Exception(f"Tool execution error: {error_message}")
Exception: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 88a78598-22da-4ac9-93d0-7a73f4e32845: "Required field 'main_input' not found in parameters"

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/executor_core.py", line 267, in execute
    raise result
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 230, in _execute_transition_with_tracking
    raise Exception(f"Exception in transition {transition_id}: {str(e)}")
Exception: Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 88a78598-22da-4ac9-93d0-7a73f4e32845: "Required field 'main_input' not found in parameters"

2025-07-01 09:45:29 - EnhancedWorkflowEngine - ERROR - An unexpected error occurred during workflow execution: Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 88a78598-22da-4ac9-93d0-7a73f4e32845: "Required field 'main_input' not found in parameters"
2025-07-01 09:45:29 - EnhancedWorkflowEngine - ERROR - Traceback for unexpected error: Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 492, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 361, in execute_tool
    raise e
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id 88a78598-22da-4ac9-93d0-7a73f4e32845: "Required field 'main_input' not found in parameters"

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 204, in _execute_transition_with_tracking
    result = await self._execute_standard_or_reflection_transition(transition)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 703, in _execute_standard_or_reflection_transition
    raise Exception(f"Tool execution error: {error_message}")
Exception: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 88a78598-22da-4ac9-93d0-7a73f4e32845: "Required field 'main_input' not found in parameters"

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/executor_core.py", line 285, in execute
    raise e
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/executor_core.py", line 267, in execute
    raise result
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 230, in _execute_transition_with_tracking
    raise Exception(f"Exception in transition {transition_id}: {str(e)}")
Exception: Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 88a78598-22da-4ac9-93d0-7a73f4e32845: "Required field 'main_input' not found in parameters"

2025-07-01 09:45:29 - KafkaWorkflowConsumer - ERROR - Exception in workflow execution: Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 88a78598-22da-4ac9-93d0-7a73f4e32845: "Required field 'main_input' not found in parameters"
2025-07-01 09:45:29 - KafkaWorkflowConsumer - INFO - Workflow '0c19c070-905e-46ef-9f57-62eb427bf396' final status: failed, result: Exception in workflow '0c19c070-905e-46ef-9f57-62eb427bf396': Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 88a78598-22da-4ac9-93d0-7a73f4e32845: "Required field 'main_input' not found in parameters"
2025-07-01 09:45:29 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 0dc0ac22-9bda-46bb-8437-3743103ed0a1, response: {'status': 'failed', 'result': 'Exception in workflow \'0c19c070-905e-46ef-9f57-62eb427bf396\': Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 88a78598-22da-4ac9-93d0-7a73f4e32845: "Required field \'main_input\' not found in parameters"', 'workflow_status': 'failed', 'error': 'Exception in transition transition-CombineTextComponent-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 88a78598-22da-4ac9-93d0-7a73f4e32845: "Required field \'main_input\' not found in parameters"', 'error_type': 'Exception'}
2025-07-01 09:45:29 - KafkaWorkflowConsumer - DEBUG - Stopped workflow with correlation_id: 0dc0ac22-9bda-46bb-8437-3743103ed0a1 
2025-07-01 09:45:34 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-07-01 09:45:34 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-01 09:45:34 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-01 09:45:34 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-07-01 09:46:34 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-07-01 09:46:34 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-01 09:46:34 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-01 09:46:34 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-07-01 09:47:34 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-07-01 09:47:34 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-01 09:47:34 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-01 09:47:34 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-07-01 09:48:34 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-07-01 09:48:34 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-01 09:48:34 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-01 09:48:34 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-07-01 09:49:20 - RedisEventListener - DEBUG - Raw results DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@5__:*', 'channel': b'__keyspace@5__:result:transition-AgenticAI-*************', 'data': b'expired'}
2025-07-01 09:49:20 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@5__:result:transition-AgenticAI-*************'
2025-07-01 09:49:20 - RedisEventListener - DEBUG - Decoded channel: __keyspace@5__:result:transition-AgenticAI-*************
2025-07-01 09:49:20 - RedisEventListener - DEBUG - Extracted key: result:transition-AgenticAI-*************
2025-07-01 09:49:20 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-07-01 09:49:20 - RedisEventListener - DEBUG - Decoded event: expired
2025-07-01 09:49:20 - RedisEventListener - INFO - Detected expired event for result of transition: transition-AgenticAI-*************
2025-07-01 09:49:20 - RedisEventListener - INFO - Archiving result for transition: transition-AgenticAI-*************
2025-07-01 09:49:20 - StateManager - DEBUG - Attempting to archive result for transition transition-AgenticAI-*************
2025-07-01 09:49:21 - StateManager - DEBUG - Provided result: False
2025-07-01 09:49:21 - StateManager - DEBUG - Trying to get result from Redis for transition transition-AgenticAI-*************
2025-07-01 09:49:22 - StateManager - DEBUG - No result found in Redis for transition transition-AgenticAI-*************
2025-07-01 09:49:22 - StateManager - DEBUG - Trying to get result from memory for transition transition-AgenticAI-*************
2025-07-01 09:49:22 - StateManager - DEBUG - No result found in memory for transition transition-AgenticAI-*************
2025-07-01 09:49:22 - StateManager - DEBUG - Available transition results in memory: []
2025-07-01 09:49:22 - StateManager - WARNING - No result found to archive for transition transition-AgenticAI-*************
2025-07-01 09:49:22 - RedisEventListener - DEBUG - Raw state DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@6__:*', 'channel': b'__keyspace@6__:workflow_state:1fcc8e97-8957-45b8-9529-154d89b8d86a', 'data': b'expired'}
2025-07-01 09:49:22 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@6__:workflow_state:1fcc8e97-8957-45b8-9529-154d89b8d86a'
2025-07-01 09:49:22 - RedisEventListener - DEBUG - Decoded channel: __keyspace@6__:workflow_state:1fcc8e97-8957-45b8-9529-154d89b8d86a
2025-07-01 09:49:22 - RedisEventListener - DEBUG - Extracted key: workflow_state:1fcc8e97-8957-45b8-9529-154d89b8d86a
2025-07-01 09:49:22 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-07-01 09:49:22 - RedisEventListener - DEBUG - Decoded event: expired
2025-07-01 09:49:22 - RedisEventListener - INFO - Detected expired event for workflow state of workflow: 1fcc8e97-8957-45b8-9529-154d89b8d86a
2025-07-01 09:49:22 - RedisEventListener - INFO - Archiving workflow state for workflow: 1fcc8e97-8957-45b8-9529-154d89b8d86a
2025-07-01 09:49:26 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-07-01 09:49:27 - PostgresManager - DEBUG - Inserted new workflow state for correlation_id: 0dc0ac22-9bda-46bb-8437-3743103ed0a1_loop_transition-LoopNode-*************_iteration_5
2025-07-01 09:49:27 - StateManager - INFO - Archived workflow state to PostgreSQL for workflow ID: 0dc0ac22-9bda-46bb-8437-3743103ed0a1_loop_transition-LoopNode-*************_iteration_5
2025-07-01 09:49:27 - RedisEventListener - DEBUG - Raw results DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@5__:*', 'channel': b'__keyspace@5__:result:transition-AgenticAI-*************', 'data': b'expired'}
2025-07-01 09:49:27 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@5__:result:transition-AgenticAI-*************'
2025-07-01 09:49:27 - RedisEventListener - DEBUG - Decoded channel: __keyspace@5__:result:transition-AgenticAI-*************
2025-07-01 09:49:27 - RedisEventListener - DEBUG - Extracted key: result:transition-AgenticAI-*************
2025-07-01 09:49:27 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-07-01 09:49:27 - RedisEventListener - DEBUG - Decoded event: expired
2025-07-01 09:49:27 - RedisEventListener - INFO - Detected expired event for result of transition: transition-AgenticAI-*************
2025-07-01 09:49:27 - RedisEventListener - INFO - Archiving result for transition: transition-AgenticAI-*************
2025-07-01 09:49:27 - StateManager - DEBUG - Attempting to archive result for transition transition-AgenticAI-*************
2025-07-01 09:49:28 - StateManager - DEBUG - Provided result: False
2025-07-01 09:49:28 - StateManager - DEBUG - Trying to get result from Redis for transition transition-AgenticAI-*************
2025-07-01 09:49:29 - StateManager - DEBUG - No result found in Redis for transition transition-AgenticAI-*************
2025-07-01 09:49:29 - StateManager - DEBUG - Trying to get result from memory for transition transition-AgenticAI-*************
2025-07-01 09:49:29 - StateManager - DEBUG - No result found in memory for transition transition-AgenticAI-*************
2025-07-01 09:49:29 - StateManager - DEBUG - Available transition results in memory: []
2025-07-01 09:49:29 - StateManager - WARNING - No result found to archive for transition transition-AgenticAI-*************
2025-07-01 09:49:32 - RedisEventListener - DEBUG - Raw results DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@5__:*', 'channel': b'__keyspace@5__:result:backup_transition-LoopNode-*************_iteration_0', 'data': b'expired'}
2025-07-01 09:49:32 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@5__:result:backup_transition-LoopNode-*************_iteration_0'
2025-07-01 09:49:32 - RedisEventListener - DEBUG - Decoded channel: __keyspace@5__:result:backup_transition-LoopNode-*************_iteration_0
2025-07-01 09:49:32 - RedisEventListener - DEBUG - Extracted key: result:backup_transition-LoopNode-*************_iteration_0
2025-07-01 09:49:32 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-07-01 09:49:32 - RedisEventListener - DEBUG - Decoded event: expired
2025-07-01 09:49:32 - RedisEventListener - INFO - Detected expired event for result of transition: backup_transition-LoopNode-*************_iteration_0
2025-07-01 09:49:32 - RedisEventListener - INFO - Archiving result for transition: backup_transition-LoopNode-*************_iteration_0
2025-07-01 09:49:32 - StateManager - DEBUG - Attempting to archive result for transition backup_transition-LoopNode-*************_iteration_0
2025-07-01 09:49:33 - StateManager - DEBUG - Provided result: False
2025-07-01 09:49:33 - StateManager - DEBUG - Trying to get result from Redis for transition backup_transition-LoopNode-*************_iteration_0
2025-07-01 09:49:34 - StateManager - DEBUG - No result found in Redis for transition backup_transition-LoopNode-*************_iteration_0
2025-07-01 09:49:34 - StateManager - DEBUG - Trying to get result from memory for transition backup_transition-LoopNode-*************_iteration_0
2025-07-01 09:49:34 - StateManager - DEBUG - No result found in memory for transition backup_transition-LoopNode-*************_iteration_0
2025-07-01 09:49:34 - StateManager - DEBUG - Available transition results in memory: []
2025-07-01 09:49:34 - StateManager - WARNING - No result found to archive for transition backup_transition-LoopNode-*************_iteration_0
2025-07-01 09:49:34 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-07-01 09:49:34 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-01 09:49:34 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-01 09:49:34 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
