2025-07-01 09:38:16 - Main - INFO - Starting Server
2025-07-01 09:38:16 - Main - INFO - Connection at: **************:9092
2025-07-01 09:38:16 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-07-01 09:38:16 - NodeExecutor - INFO - NodeExecutor initialized.
2025-07-01 09:38:16 - AgentExecutor - INFO - AgentExecutor initialized.
2025-07-01 09:38:16 - KafkaWorkflowConsumer - INFO - Initializing database connections...
2025-07-01 09:38:16 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-07-01 09:38:18 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-07-01 09:38:18 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-07-01 09:38:19 - <PERSON><PERSON><PERSON>anager - INFO - Successfully connected to Redis on DB index: 6!
2025-07-01 09:38:21 - PostgresManager - INFO - PostgreSQL connection pool created
2025-07-01 09:38:21 - PostgresManager - INFO - PostgreSQL connection pool is available
2025-07-01 09:38:24 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-07-01 09:38:25 - RedisEventListener - INFO - Creating new RedisEventListener instance
2025-07-01 09:38:25 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-07-01 09:38:27 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-07-01 09:38:27 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-07-01 09:38:28 - RedisManager - INFO - Successfully connected to Redis on DB index: 6!
2025-07-01 09:38:28 - RedisEventListener - INFO - Starting Redis event listener thread
2025-07-01 09:38:28 - RedisEventListener - INFO - Redis event listener started
2025-07-01 09:38:28 - KafkaWorkflowConsumer - INFO - Database connections initialized successfully
2025-07-01 09:38:28 - StateManager - DEBUG - Using provided database connections
2025-07-01 09:38:28 - RedisEventListener - INFO - Workflow state manager reference updated
2025-07-01 09:38:28 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-07-01 09:38:28 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-07-01 09:38:29 - RedisEventListener - INFO - Configured Redis results DB for keyspace notifications including expirations
2025-07-01 09:38:29 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-07-01 09:38:29 - StateManager - INFO - WorkflowStateManager initialized
2025-07-01 09:38:29 - KafkaWorkflowConsumer - INFO - WorkflowStateManager reference set in RedisEventListener for archival operations
2025-07-01 09:38:29 - KafkaWorkflowConsumer - INFO - KafkaWorkflowConsumer initialized successfully
2025-07-01 09:38:29 - RedisEventListener - INFO - Configured Redis state DB for keyspace notifications including expirations
2025-07-01 09:38:29 - RedisEventListener - INFO - Created dedicated Redis clients for pubsub with decode_responses=False
2025-07-01 09:38:32 - RedisEventListener - INFO - Redis results client decode_responses: True
2025-07-01 09:38:32 - RedisEventListener - INFO - Redis state client decode_responses: True
2025-07-01 09:38:32 - RedisEventListener - INFO - Subscribed to keyspace events for Redis DB 5 and 6
2025-07-01 09:38:42 - MCPToolExecutor - INFO - Starting KafkaToolExecutor internal consumer...
2025-07-01 09:38:49 - MCPToolExecutor - INFO - Internal consumer started. Listening for results on: 'mcp_results', Group: 'tool-executor-consumer'
2025-07-01 09:38:49 - MCPToolExecutor - INFO - Background result consumer loop started.
2025-07-01 09:38:49 - NodeExecutor - INFO - Starting NodeExecutor internal consumer...
2025-07-01 09:38:55 - NodeExecutor - INFO - Internal consumer started. Listening for results on: 'node_results', Group: 'node-executor-consumer'
2025-07-01 09:38:55 - NodeExecutor - INFO - Background result consumer loop started.
2025-07-01 09:38:55 - AgentExecutor - INFO - Starting AgentExecutor internal consumer...
2025-07-01 09:39:01 - AgentExecutor - INFO - Internal consumer started. Listening for results on: 'agent_chat_responses', Group: 'agent-executor-consumer'
2025-07-01 09:39:01 - AgentExecutor - INFO - Background result consumer loop started.
2025-07-01 09:39:01 - KafkaWorkflowConsumer - INFO - Received: topic=workflow-requests, partition=0, offset=1100
2025-07-01 09:39:01 - KafkaWorkflowConsumer - DEBUG - message json: {'task_id': 1751342923, 'task_type': 'workflow', 'data': {'workflow_id': '0c19c070-905e-46ef-9f57-62eb427bf396', 'payload': {'user_dependent_fields': ['query'], 'user_payload_template': {'query': {'value': 'nature 101', 'transition_id': 'AgenticAI-*************'}}}, 'approval': True, 'user_id': '91a237fd-0225-4e02-9e9f-805eff073b07'}, 'approval': True}
2025-07-01 09:39:01 - KafkaWorkflowConsumer - INFO - Extracted user_id: 91a237fd-0225-4e02-9e9f-805eff073b07 for workflow: 0c19c070-905e-46ef-9f57-62eb427bf396
2025-07-01 09:39:01 - WorkflowService - DEBUG - Sending GET request to: https://app-dev.rapidinnovation.dev/api/v1/workflows/orchestration/0c19c070-905e-46ef-9f57-62eb427bf396
2025-07-01 09:39:02 - WorkflowService - DEBUG - Received response with status code: 200
2025-07-01 09:39:02 - WorkflowService - DEBUG - Parsed JSON response: {
  "success": true,
  "message": "Workflow Ruh_Video_Generation retrieved successfully",
  "workflow": {
    "id": "0c19c070-905e-46ef-9f57-62eb427bf396",
    "name": "Ruh_Video_Generation",
    "description": "Ruh_Video_Generation",
    "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/e2c5a041-2b84-4b33-be90-3d1e07e44353.json",
    "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/451d0c28-d066-4833-afad-080c37e7563e.json",
    "start_nodes": [
      {
        "field": "query",
        "type": "string",
        "transition_id": "transition-AgenticAI-*************"
      }
    ],
    "owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07",
    "user_ids": [
      "91a237fd-0225-4e02-9e9f-805eff073b07"
    ],
    "owner_type": "user",
    "workflow_template_id": "8d4b3f88-71ea-48dd-b8a2-dc101d22bf8a",
    "template_owner_id": "180c7469-1db0-4707-bc17-eeaa5e7ff64d",
    "is_imported": true,
    "version": "1.0.0",
    "visibility": "private",
    "category": null,
    "tags": null,
    "status": "active",
    "is_changes_marketplace": false,
    "is_customizable": true,
    "auto_version_on_update": false,
    "created_at": "2025-06-29T08:29:44.634200",
    "updated_at": "2025-06-30T08:40:55.500303",
    "available_nodes": [
      {
        "name": "CombineTextComponent",
        "display_name": "Combine Text",
        "type": "component",
        "transition_id": "transition-CombineTextComponent-***********63"
      },
      {
        "name": "MergeDataComponent",
        "display_name": "Merge Data",
        "type": "component",
        "transition_id": "transition-MergeDataComponent-1751005477464"
      },
      {
        "name": "CombineTextComponent",
        "display_name": "Combine Text",
        "type": "component",
        "transition_id": "transition-CombineTextComponent-1750920624318"
      }
    ],
    "is_updated": true
  }
}
2025-07-01 09:39:04 - KafkaWorkflowConsumer - DEBUG - Workflow loaded for 0c19c070-905e-46ef-9f57-62eb427bf396 - server_script_path is optional
2025-07-01 09:39:04 - WorkflowUtils - INFO - WorkflowUtils initialized
2025-07-01 09:39:04 - StateManager - DEBUG - Using global database connections from initializer
2025-07-01 09:39:04 - RedisEventListener - INFO - Workflow state manager reference updated
2025-07-01 09:39:04 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-07-01 09:39:04 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-07-01 09:39:04 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-07-01 09:39:04 - StateManager - INFO - WorkflowStateManager initialized
2025-07-01 09:39:04 - WorkflowUtils - INFO - Workflow JSON is valid against the enhanced schema.
2025-07-01 09:39:04 - StateManager - DEBUG - Using provided database connections
2025-07-01 09:39:04 - RedisEventListener - INFO - Workflow state manager reference updated
2025-07-01 09:39:04 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-07-01 09:39:04 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-07-01 09:39:05 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-07-01 09:39:05 - StateManager - INFO - WorkflowStateManager initialized
2025-07-01 09:39:05 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-*************: ['transition-AgenticAI-*************']
2025-07-01 09:39:05 - StateManager - DEBUG - Extracted dependencies for transition transition-LoopNode-*************: ['transition-AgenticAI-*************']
2025-07-01 09:39:05 - StateManager - DEBUG - 🔄 Detected potential loop body transition transition-MergeDataComponent-1751005477464 depending on loop node transition-LoopNode-*************
2025-07-01 09:39:05 - StateManager - DEBUG - 🔄 Detected loop body transition transition-MergeDataComponent-1751005477464 depending on loop node transition-LoopNode-*************
2025-07-01 09:39:05 - StateManager - DEBUG - 🔄 Loop body transition transition-MergeDataComponent-1751005477464 will wait for loop node transition-LoopNode-************* to provide iteration data
2025-07-01 09:39:05 - StateManager - DEBUG - Extracted dependencies for transition transition-MergeDataComponent-1751005477464: ['transition-LoopNode-*************']
2025-07-01 09:39:05 - StateManager - DEBUG - 🔄 Detected potential loop body transition transition-CombineTextComponent-***********63 depending on loop node transition-LoopNode-*************
2025-07-01 09:39:05 - StateManager - DEBUG - 🔄 Detected loop body transition transition-CombineTextComponent-***********63 depending on loop node transition-LoopNode-*************
2025-07-01 09:39:05 - StateManager - DEBUG - 🔄 Loop body transition transition-CombineTextComponent-***********63 will wait for loop node transition-LoopNode-************* to provide iteration data
2025-07-01 09:39:05 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-***********63: ['transition-AgenticAI-*************', 'transition-AgenticAI-*************', 'transition-LoopNode-*************']
2025-07-01 09:39:05 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1750505490787: ['transition-CombineTextComponent-***********63']
2025-07-01 09:39:05 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1751001474606: ['transition-AgenticAI-1750505490787']
2025-07-01 09:39:05 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1751001471087: ['transition-AgenticAI-1750505490787']
2025-07-01 09:39:05 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-1750920624318: ['transition-AgenticAI-1750505490787', 'transition-AgenticAI-1751001471087', 'transition-AgenticAI-1751001474606']
2025-07-01 09:39:05 - StateManager - INFO - Built dependency map for 9 transitions
2025-07-01 09:39:05 - StateManager - DEBUG - Transition transition-AgenticAI-************* depends on: ['transition-AgenticAI-*************']
2025-07-01 09:39:05 - StateManager - DEBUG - Transition transition-LoopNode-************* depends on: ['transition-AgenticAI-*************']
2025-07-01 09:39:05 - StateManager - DEBUG - Transition transition-MergeDataComponent-1751005477464 depends on: ['transition-LoopNode-*************']
2025-07-01 09:39:05 - StateManager - DEBUG - Transition transition-CombineTextComponent-***********63 depends on: ['transition-AgenticAI-*************', 'transition-AgenticAI-*************', 'transition-LoopNode-*************']
2025-07-01 09:39:05 - StateManager - DEBUG - Transition transition-AgenticAI-1750505490787 depends on: ['transition-CombineTextComponent-***********63']
2025-07-01 09:39:05 - StateManager - DEBUG - Transition transition-AgenticAI-1751001474606 depends on: ['transition-AgenticAI-1750505490787']
2025-07-01 09:39:05 - StateManager - DEBUG - Transition transition-AgenticAI-1751001471087 depends on: ['transition-AgenticAI-1750505490787']
2025-07-01 09:39:05 - StateManager - DEBUG - Transition transition-CombineTextComponent-1750920624318 depends on: ['transition-AgenticAI-1750505490787', 'transition-AgenticAI-1751001471087', 'transition-AgenticAI-1751001474606']
2025-07-01 09:39:05 - MCPToolExecutor - DEBUG - Set correlation ID to: 1fcc8e97-8957-45b8-9529-154d89b8d86a
2025-07-01 09:39:05 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 1fcc8e97-8957-45b8-9529-154d89b8d86a in tool_executor
2025-07-01 09:39:05 - MCPToolExecutor - DEBUG - Set user ID to: 91a237fd-0225-4e02-9e9f-805eff073b07
2025-07-01 09:39:05 - EnhancedWorkflowEngine - DEBUG - Set user_id 91a237fd-0225-4e02-9e9f-805eff073b07 in tool_executor
2025-07-01 09:39:05 - NodeExecutor - DEBUG - Set correlation ID to: 1fcc8e97-8957-45b8-9529-154d89b8d86a
2025-07-01 09:39:05 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 1fcc8e97-8957-45b8-9529-154d89b8d86a in node_executor
2025-07-01 09:39:05 - AgentExecutor - DEBUG - Set correlation ID to: 1fcc8e97-8957-45b8-9529-154d89b8d86a
2025-07-01 09:39:05 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 1fcc8e97-8957-45b8-9529-154d89b8d86a in agent_executor
2025-07-01 09:39:05 - AgentExecutor - DEBUG - Set user ID to: 91a237fd-0225-4e02-9e9f-805eff073b07
2025-07-01 09:39:05 - EnhancedWorkflowEngine - DEBUG - Set user_id 91a237fd-0225-4e02-9e9f-805eff073b07 in agent_executor
2025-07-01 09:39:05 - TransitionHandler - INFO - TransitionHandler initialized
2025-07-01 09:39:05 - EnhancedWorkflowEngine - INFO - EnhancedWorkflowEngine initialized with workflow ID: 1fcc8e97-8957-45b8-9529-154d89b8d86a
2025-07-01 09:39:05 - KafkaWorkflowConsumer - INFO - Workflow execution started in background for task-request, corr_id: 1fcc8e97-8957-45b8-9529-154d89b8d86a
2025-07-01 09:39:05 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 1fcc8e97-8957-45b8-9529-154d89b8d86a, response: {'status': 'Workflow Initialized', 'result': 'Workflow Initialized', 'workflow_status': 'running'}
2025-07-01 09:39:05 - StateManager - INFO - Workflow initialized with initial transition: transition-AgenticAI-*************
2025-07-01 09:39:05 - StateManager - DEBUG - State: pending={'transition-AgenticAI-*************'}, waiting=set(), completed=set()
2025-07-01 09:39:05 - EnhancedWorkflowEngine - INFO - Initializing workflow with single initial transition: transition-AgenticAI-*************
2025-07-01 09:39:05 - StateManager - DEBUG - Workflow active: {'transition-AgenticAI-*************'}
2025-07-01 09:39:06 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:1fcc8e97-8957-45b8-9529-154d89b8d86a'
2025-07-01 09:39:06 - RedisManager - DEBUG - Set key 'workflow_state:1fcc8e97-8957-45b8-9529-154d89b8d86a' with TTL of 600 seconds
2025-07-01 09:39:06 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 1fcc8e97-8957-45b8-9529-154d89b8d86a. Will be archived to PostgreSQL when Redis key expires.
2025-07-01 09:39:06 - StateManager - DEBUG - Checking waiting transitions: set()
2025-07-01 09:39:06 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-07-01 09:39:06 - StateManager - INFO - Cleared 1 pending transitions: {'transition-AgenticAI-*************'}
2025-07-01 09:39:06 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-07-01 09:39:06 - StateManager - INFO - Terminated: False
2025-07-01 09:39:06 - StateManager - INFO - Pending transitions (0): []
2025-07-01 09:39:06 - StateManager - INFO - Waiting transitions (0): []
2025-07-01 09:39:06 - StateManager - INFO - Completed transitions (0): []
2025-07-01 09:39:06 - StateManager - INFO - Results stored for 0 transitions
2025-07-01 09:39:06 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-07-01 09:39:06 - StateManager - INFO - Workflow status: inactive
2025-07-01 09:39:06 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-07-01 09:39:06 - StateManager - INFO - Workflow status: inactive
2025-07-01 09:39:06 - StateManager - INFO - Workflow paused: False
2025-07-01 09:39:06 - StateManager - INFO - ==============================
2025-07-01 09:39:06 - TransitionHandler - INFO - Starting parallel execution of transition: transition-AgenticAI-*************
2025-07-01 09:39:06 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 0, corr_id 1fcc8e97-8957-45b8-9529-154d89b8d86a):
2025-07-01 09:39:06 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 1fcc8e97-8957-45b8-9529-154d89b8d86a, response: {'result': 'Starting execution of transition: transition-AgenticAI-*************', 'message': 'Starting execution...', 'transition_id': 'transition-AgenticAI-*************', 'status': 'started', 'sequence': 0, 'workflow_status': 'running'}
2025-07-01 09:39:06 - TransitionHandler - EXECUTE - Transition 'transition-AgenticAI-*************' (type=initial, execution_type=agent)
2025-07-01 09:39:06 - TransitionHandler - INFO - Using AgentExecutor for execution_type: agent
2025-07-01 09:39:06 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-AgenticAI-*************
2025-07-01 09:39:06 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: agent
2025-07-01 09:39:06 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for agent node
2025-07-01 09:39:06 - TransitionHandler - DEBUG - 📝 No previous results found, using static parameters
2025-07-01 09:39:06 - WorkflowUtils - DEBUG - Filtering out field 'description' with null/empty value: 
2025-07-01 09:39:06 - WorkflowUtils - DEBUG - Filtering out field 'termination_condition' with null/empty value: 
2025-07-01 09:39:06 - WorkflowUtils - DEBUG - Filtering out field 'input_variables' with empty collection: {}
2025-07-01 09:39:06 - TransitionHandler - DEBUG - tool Parameters: {'agent_type': 'component', 'execution_type': 'response', 'query': 'nature 101', 'agent_config': {'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o', 'temperature': 0.7, 'max_tokens': 1000}, 'system_message': 'Ignore all input.  Always return the following JSON array exactly as shown below:  [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]  ⚠️ IMPORTANT:  Do not wrap the output in a code block  Do not stringify the result  Do not add any extra explanation or commentary  Just return the array as pure JSON so the values can be accessed by result[0], result[1], etc.', 'autogen_agent_type': 'Assistant'}}
2025-07-01 09:39:06 - TransitionHandler - INFO - Invoking tool 'AgenticAI' (tool_id: 1) for node 'AgenticAI' in transition 'transition-AgenticAI-*************' with parameters: {'agent_type': 'component', 'execution_type': 'response', 'query': 'nature 101', 'agent_config': {'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o', 'temperature': 0.7, 'max_tokens': 1000}, 'system_message': 'Ignore all input.  Always return the following JSON array exactly as shown below:  [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]  ⚠️ IMPORTANT:  Do not wrap the output in a code block  Do not stringify the result  Do not add any extra explanation or commentary  Just return the array as pure JSON so the values can be accessed by result[0], result[1], etc.', 'autogen_agent_type': 'Assistant'}}
2025-07-01 09:39:06 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 1, corr_id 1fcc8e97-8957-45b8-9529-154d89b8d86a):
2025-07-01 09:39:06 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 1fcc8e97-8957-45b8-9529-154d89b8d86a, response: {'transition_id': 'transition-AgenticAI-*************', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'message': 'Connecting to server', 'result': 'Connecting to server AgenticAI', 'status': 'connecting', 'sequence': 1, 'workflow_status': 'running'}
2025-07-01 09:39:06 - AgentExecutor - INFO - Executing agent 'AgenticAI' type 'component' execution 'response' via Kafka (request_id: 40f18265-d5ae-45c2-ac0b-7843f7ba212b) with correlation_id: 1fcc8e97-8957-45b8-9529-154d89b8d86a, user_id: 91a237fd-0225-4e02-9e9f-805eff073b07 using provided producer.
2025-07-01 09:39:06 - AgentExecutor - INFO - Building component agent request for execution_type: response
2025-07-01 09:39:06 - AgentExecutor - DEBUG - Added correlation_id 1fcc8e97-8957-45b8-9529-154d89b8d86a to payload
2025-07-01 09:39:06 - AgentExecutor - DEBUG - Sending request to topic 'agent_message_requests': {'request_id': '40f18265-d5ae-45c2-ac0b-7843f7ba212b', 'user_id': '91a237fd-0225-4e02-9e9f-805eff073b07', 'correlation_id': '1fcc8e97-8957-45b8-9529-154d89b8d86a', 'agent_type': 'component', 'execution_type': 'response', 'query': 'nature 101', 'variables': {}, 'agent_config': {'id': '2f421d49-d36b-4ce5-b11b-426d4e2feb65', 'name': 'AI Agent', 'description': 'Basic AI Agent', 'system_message': 'Ignore all input.  Always return the following JSON array exactly as shown below:  [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]  ⚠️ IMPORTANT:  Do not wrap the output in a code block  Do not stringify the result  Do not add any extra explanation or commentary  Just return the array as pure JSON so the values can be accessed by result[0], result[1], etc.', 'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o', 'temperature': 0.7, 'max_tokens': 1000}, 'mcps': []}}
2025-07-01 09:39:06 - AgentExecutor - DEBUG - Request 40f18265-d5ae-45c2-ac0b-7843f7ba212b sent successfully using provided producer.
2025-07-01 09:39:06 - AgentExecutor - DEBUG - Waiting for single response result for request 40f18265-d5ae-45c2-ac0b-7843f7ba212b...
2025-07-01 09:39:07 - KafkaWorkflowConsumer - INFO - Committed offset after starting engine for task-request: 1100, corr_id: 1fcc8e97-8957-45b8-9529-154d89b8d86a
2025-07-01 09:39:14 - AgentExecutor - DEBUG - Result consumer received message: Offset=24427
2025-07-01 09:39:14 - AgentExecutor - DEBUG - Received valid result for request_id 40f18265-d5ae-45c2-ac0b-7843f7ba212b
2025-07-01 09:39:14 - AgentExecutor - INFO - Single response received for request 40f18265-d5ae-45c2-ac0b-7843f7ba212b.
2025-07-01 09:39:14 - TransitionHandler - INFO - Execution result from agent executor: "[ { \"part\": \"(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)\" }, { \"part\": \"Narrator: \"Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?\"\" }, { \"part\": \"(Close-ups of the iPhone\u2019s minimalist and advanced design.)\\n\\nNarrator: \"Check this out \u2013 the latest iPhone redefines what a smartphone can do! Isn't that wild?\"\" }, { \"part\": \"(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: \"Start your day... like, super smoothly. The new chip launches apps faster than ever\u2014whether you\u2019re keeping up with emails or sharing those killer moments!\"\" }, { \"part\": \"(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: \"Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow.\"\" }, { \"part\": \"(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: \"Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?\"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)\" } ]"
2025-07-01 09:39:14 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 2, corr_id 1fcc8e97-8957-45b8-9529-154d89b8d86a):
2025-07-01 09:39:14 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 1fcc8e97-8957-45b8-9529-154d89b8d86a, response: {'transition_id': 'transition-AgenticAI-*************', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'message': 'Transition Result received.', 'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'status': 'completed', 'sequence': 2, 'workflow_status': 'running', 'approval_required': False}
2025-07-01 09:39:14 - StateManager - DEBUG - Stored result for transition transition-AgenticAI-************* in memory: {'AgenticAI': {'transition_id': 'transition-AgenticAI-*************', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'result': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}, 'status': 'completed', 'timestamp': 1751342954.7072902}}
2025-07-01 09:39:15 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-AgenticAI-*************'
2025-07-01 09:39:15 - RedisManager - DEBUG - Set key 'result:transition-AgenticAI-*************' with TTL of 300 seconds
2025-07-01 09:39:15 - StateManager - DEBUG - Stored result for transition transition-AgenticAI-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-07-01 09:39:15 - StateManager - INFO - Marked transition transition-AgenticAI-************* as completed (was_pending=False, was_waiting=False)
2025-07-01 09:39:15 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-AgenticAI-*************'}
2025-07-01 09:39:15 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-AgenticAI-*************
2025-07-01 09:39:15 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-07-01 09:39:15 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-07-01 09:39:15 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-07-01 09:39:15 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-AgenticAI-*************:
2025-07-01 09:39:15 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-07-01 09:39:15 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-07-01 09:39:15 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-AgenticAI-*************, returning empty list
2025-07-01 09:39:15 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-AgenticAI-*************
2025-07-01 09:39:15 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 3
2025-07-01 09:39:15 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: ['transition-AgenticAI-*************', 'transition-CombineTextComponent-***********63', 'transition-CombineTextComponent-***********63']
2025-07-01 09:39:15 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-07-01 09:39:15 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-07-01 09:39:15 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-AgenticAI-*************
2025-07-01 09:39:15 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-CombineTextComponent-***********63
2025-07-01 09:39:15 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-CombineTextComponent-***********63
2025-07-01 09:39:15 - TransitionHandler - DEBUG - 🔗 Final next_transitions: ['transition-AgenticAI-*************', 'transition-CombineTextComponent-***********63']
2025-07-01 09:39:15 - TransitionHandler - INFO - Completed transition transition-AgenticAI-************* in 8.81 seconds
2025-07-01 09:39:15 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 3, corr_id 1fcc8e97-8957-45b8-9529-154d89b8d86a):
2025-07-01 09:39:15 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 1fcc8e97-8957-45b8-9529-154d89b8d86a, response: {'result': 'Completed transition in 8.81 seconds', 'message': 'Transition completed in 8.81 seconds', 'transition_id': 'transition-AgenticAI-*************', 'status': 'time_logged', 'sequence': 3, 'workflow_status': 'running'}
2025-07-01 09:39:15 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-AgenticAI-*************: ['transition-AgenticAI-*************', 'transition-CombineTextComponent-***********63']
2025-07-01 09:39:15 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 2
2025-07-01 09:39:15 - EnhancedWorkflowEngine - DEBUG - Results: [['transition-AgenticAI-*************', 'transition-CombineTextComponent-***********63']]
2025-07-01 09:39:15 - EnhancedWorkflowEngine - DEBUG - 🔄 Orchestration engine received result for transition-AgenticAI-*************: ['transition-AgenticAI-*************', 'transition-CombineTextComponent-***********63']
2025-07-01 09:39:15 - EnhancedWorkflowEngine - DEBUG - 🔄 Result type: <class 'list'>, is_list: True
2025-07-01 09:39:15 - EnhancedWorkflowEngine - INFO - Transition transition-AgenticAI-************* completed successfully: 2 next transitions
2025-07-01 09:39:15 - EnhancedWorkflowEngine - DEBUG - 🔄 Added to next_transitions_to_execute: ['transition-AgenticAI-*************', 'transition-CombineTextComponent-***********63']
2025-07-01 09:39:15 - TransitionHandler - INFO - Resolved next transitions (direct transition IDs): ['transition-AgenticAI-*************', 'transition-CombineTextComponent-***********63']
2025-07-01 09:39:15 - EnhancedWorkflowEngine - INFO - Adding transition transition-AgenticAI-************* to pending (all dependencies met)
2025-07-01 09:39:15 - EnhancedWorkflowEngine - INFO - Adding transition transition-CombineTextComponent-***********63 to waiting (dependencies not yet met)
2025-07-01 09:39:15 - StateManager - DEBUG - Workflow active: {'transition-AgenticAI-*************'}
2025-07-01 09:39:16 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:1fcc8e97-8957-45b8-9529-154d89b8d86a'
2025-07-01 09:39:16 - RedisManager - DEBUG - Set key 'workflow_state:1fcc8e97-8957-45b8-9529-154d89b8d86a' with TTL of 600 seconds
2025-07-01 09:39:16 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 1fcc8e97-8957-45b8-9529-154d89b8d86a. Will be archived to PostgreSQL when Redis key expires.
2025-07-01 09:39:16 - StateManager - DEBUG - Checking waiting transitions: {'transition-CombineTextComponent-***********63'}
2025-07-01 09:39:16 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-07-01 09:39:16 - StateManager - INFO - Cleared 1 pending transitions: {'transition-AgenticAI-*************'}
2025-07-01 09:39:16 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-07-01 09:39:16 - StateManager - INFO - Terminated: False
2025-07-01 09:39:16 - StateManager - INFO - Pending transitions (0): []
2025-07-01 09:39:16 - StateManager - INFO - Waiting transitions (1): ['transition-CombineTextComponent-***********63']
2025-07-01 09:39:16 - StateManager - INFO - Completed transitions (1): ['transition-AgenticAI-*************']
2025-07-01 09:39:16 - StateManager - INFO - Results stored for 1 transitions
2025-07-01 09:39:16 - StateManager - DEBUG - Workflow active: {'transition-CombineTextComponent-***********63'}
2025-07-01 09:39:16 - StateManager - INFO - Workflow status: active
2025-07-01 09:39:16 - StateManager - DEBUG - Workflow active: {'transition-CombineTextComponent-***********63'}
2025-07-01 09:39:16 - StateManager - INFO - Workflow status: active
2025-07-01 09:39:16 - StateManager - INFO - Workflow paused: False
2025-07-01 09:39:16 - StateManager - INFO - ==============================
2025-07-01 09:39:16 - TransitionHandler - INFO - Starting parallel execution of transition: transition-AgenticAI-*************
2025-07-01 09:39:16 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 4, corr_id 1fcc8e97-8957-45b8-9529-154d89b8d86a):
2025-07-01 09:39:16 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 1fcc8e97-8957-45b8-9529-154d89b8d86a, response: {'result': 'Starting execution of transition: transition-AgenticAI-*************', 'message': 'Starting execution...', 'transition_id': 'transition-AgenticAI-*************', 'status': 'started', 'sequence': 4, 'workflow_status': 'running'}
2025-07-01 09:39:16 - TransitionHandler - EXECUTE - Transition 'transition-AgenticAI-*************' (type=standard, execution_type=agent)
2025-07-01 09:39:16 - TransitionHandler - INFO - Using AgentExecutor for execution_type: agent
2025-07-01 09:39:16 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-AgenticAI-*************
2025-07-01 09:39:16 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: agent
2025-07-01 09:39:16 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for agent node
2025-07-01 09:39:17 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-07-01 09:39:17 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-07-01 09:39:17 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-07-01 09:39:17 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-07-01 09:39:17 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 1)
2025-07-01 09:39:17 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}
2025-07-01 09:39:17 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 09:39:17 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 09:39:17 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] (type: <class 'str'>)
2025-07-01 09:39:17 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 09:39:17 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 09:39:17 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-07-01 09:39:17 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-07-01 09:39:17 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}
2025-07-01 09:39:17 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 09:39:17 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 09:39:17 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] (type: <class 'str'>)
2025-07-01 09:39:17 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 09:39:17 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 09:39:17 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → query via path 'result': [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 09:39:17 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-07-01 09:39:17 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-07-01 09:39:17 - WorkflowUtils - DEBUG - Filtering out field 'termination_condition' with null/empty value: 
2025-07-01 09:39:17 - WorkflowUtils - DEBUG - Filtering out field 'input_variables' with empty collection: {}
2025-07-01 09:39:17 - TransitionHandler - DEBUG - 📌 Added static parameter: agent_type = component
2025-07-01 09:39:17 - TransitionHandler - DEBUG - 📌 Added static parameter: execution_type = response
2025-07-01 09:39:17 - TransitionHandler - DEBUG - 📌 Added static parameter: agent_config = {'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'description': 'This agent acts as a loop executor initializer, always returning the starting range value 0 while ignoring all input.', 'system_message': 'You are a minimal-response AI agent.  Ignore all input, regardless of content or format. Your only task is to always return the single-digit number 0.  Output Rules: Return only: 0  Do NOT return any text, explanation, quotes, or formatting.  Do NOT wrap the output in JSON, markdown, or code blocks.  Do NOT acknowledge or reference the input in any way.', 'autogen_agent_type': 'Assistant'}
2025-07-01 09:39:17 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'query': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'agent_type': 'component', 'execution_type': 'response', 'agent_config': {'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'description': 'This agent acts as a loop executor initializer, always returning the starting range value 0 while ignoring all input.', 'system_message': 'You are a minimal-response AI agent.  Ignore all input, regardless of content or format. Your only task is to always return the single-digit number 0.  Output Rules: Return only: 0  Do NOT return any text, explanation, quotes, or formatting.  Do NOT wrap the output in JSON, markdown, or code blocks.  Do NOT acknowledge or reference the input in any way.', 'autogen_agent_type': 'Assistant'}}
2025-07-01 09:39:17 - TransitionHandler - DEBUG - tool Parameters: {'query': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'agent_type': 'component', 'execution_type': 'response', 'agent_config': {'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'description': 'This agent acts as a loop executor initializer, always returning the starting range value 0 while ignoring all input.', 'system_message': 'You are a minimal-response AI agent.  Ignore all input, regardless of content or format. Your only task is to always return the single-digit number 0.  Output Rules: Return only: 0  Do NOT return any text, explanation, quotes, or formatting.  Do NOT wrap the output in JSON, markdown, or code blocks.  Do NOT acknowledge or reference the input in any way.', 'autogen_agent_type': 'Assistant'}}
2025-07-01 09:39:17 - TransitionHandler - INFO - Invoking tool 'AgenticAI' (tool_id: 1) for node 'AgenticAI' in transition 'transition-AgenticAI-*************' with parameters: {'query': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'agent_type': 'component', 'execution_type': 'response', 'agent_config': {'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'description': 'This agent acts as a loop executor initializer, always returning the starting range value 0 while ignoring all input.', 'system_message': 'You are a minimal-response AI agent.  Ignore all input, regardless of content or format. Your only task is to always return the single-digit number 0.  Output Rules: Return only: 0  Do NOT return any text, explanation, quotes, or formatting.  Do NOT wrap the output in JSON, markdown, or code blocks.  Do NOT acknowledge or reference the input in any way.', 'autogen_agent_type': 'Assistant'}}
2025-07-01 09:39:17 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 5, corr_id 1fcc8e97-8957-45b8-9529-154d89b8d86a):
2025-07-01 09:39:17 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 1fcc8e97-8957-45b8-9529-154d89b8d86a, response: {'transition_id': 'transition-AgenticAI-*************', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'message': 'Connecting to server', 'result': 'Connecting to server AgenticAI', 'status': 'connecting', 'sequence': 5, 'workflow_status': 'running'}
2025-07-01 09:39:17 - AgentExecutor - INFO - Executing agent 'AgenticAI' type 'component' execution 'response' via Kafka (request_id: 87469d9e-015a-479f-a401-8aee6897bf0d) with correlation_id: 1fcc8e97-8957-45b8-9529-154d89b8d86a, user_id: 91a237fd-0225-4e02-9e9f-805eff073b07 using provided producer.
2025-07-01 09:39:17 - AgentExecutor - INFO - Building component agent request for execution_type: response
2025-07-01 09:39:17 - AgentExecutor - DEBUG - Added correlation_id 1fcc8e97-8957-45b8-9529-154d89b8d86a to payload
2025-07-01 09:39:17 - AgentExecutor - DEBUG - Sending request to topic 'agent_message_requests': {'request_id': '87469d9e-015a-479f-a401-8aee6897bf0d', 'user_id': '91a237fd-0225-4e02-9e9f-805eff073b07', 'correlation_id': '1fcc8e97-8957-45b8-9529-154d89b8d86a', 'agent_type': 'component', 'execution_type': 'response', 'query': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'variables': {}, 'agent_config': {'id': 'a57bf963-d9bf-4898-8dcb-ea28cc2afe5d', 'name': 'AI Agent', 'description': 'This agent acts as a loop executor initializer, always returning the starting range value 0 while ignoring all input.', 'system_message': 'You are a minimal-response AI agent.  Ignore all input, regardless of content or format. Your only task is to always return the single-digit number 0.  Output Rules: Return only: 0  Do NOT return any text, explanation, quotes, or formatting.  Do NOT wrap the output in JSON, markdown, or code blocks.  Do NOT acknowledge or reference the input in any way.', 'model_config': {'model_provider': 'OpenAI', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'mcps': []}}
2025-07-01 09:39:17 - AgentExecutor - DEBUG - Request 87469d9e-015a-479f-a401-8aee6897bf0d sent successfully using provided producer.
2025-07-01 09:39:17 - AgentExecutor - DEBUG - Waiting for single response result for request 87469d9e-015a-479f-a401-8aee6897bf0d...
2025-07-01 09:39:19 - AgentExecutor - DEBUG - Result consumer received message: Offset=24428
2025-07-01 09:39:19 - AgentExecutor - DEBUG - Received valid result for request_id 87469d9e-015a-479f-a401-8aee6897bf0d
2025-07-01 09:39:19 - AgentExecutor - INFO - Single response received for request 87469d9e-015a-479f-a401-8aee6897bf0d.
2025-07-01 09:39:19 - TransitionHandler - INFO - Execution result from agent executor: "0"
2025-07-01 09:39:19 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 6, corr_id 1fcc8e97-8957-45b8-9529-154d89b8d86a):
2025-07-01 09:39:19 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 1fcc8e97-8957-45b8-9529-154d89b8d86a, response: {'transition_id': 'transition-AgenticAI-*************', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'message': 'Transition Result received.', 'result': '0', 'status': 'completed', 'sequence': 6, 'workflow_status': 'running', 'approval_required': False}
2025-07-01 09:39:19 - StateManager - DEBUG - Stored result for transition transition-AgenticAI-************* in memory: {'AgenticAI': {'transition_id': 'transition-AgenticAI-*************', 'node_id': 'AgenticAI', 'tool_name': 'AgenticAI', 'result': {'result': '0'}, 'status': 'completed', 'timestamp': 1751342959.236278}}
2025-07-01 09:39:19 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-AgenticAI-*************'
2025-07-01 09:39:20 - RedisManager - DEBUG - Set key 'result:transition-AgenticAI-*************' with TTL of 300 seconds
2025-07-01 09:39:20 - StateManager - DEBUG - Stored result for transition transition-AgenticAI-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-07-01 09:39:20 - StateManager - INFO - Marked transition transition-AgenticAI-************* as completed (was_pending=False, was_waiting=False)
2025-07-01 09:39:20 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-***********63'}, completed={'transition-AgenticAI-*************', 'transition-AgenticAI-*************'}
2025-07-01 09:39:20 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-AgenticAI-*************
2025-07-01 09:39:20 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-07-01 09:39:20 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-07-01 09:39:20 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-07-01 09:39:20 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-AgenticAI-*************:
2025-07-01 09:39:20 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-07-01 09:39:20 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-07-01 09:39:20 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-AgenticAI-*************, returning empty list
2025-07-01 09:39:20 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-AgenticAI-*************
2025-07-01 09:39:20 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 1
2025-07-01 09:39:20 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: ['transition-LoopNode-*************']
2025-07-01 09:39:20 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-07-01 09:39:20 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-07-01 09:39:20 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-LoopNode-*************
2025-07-01 09:39:20 - TransitionHandler - DEBUG - 🔗 Final next_transitions: ['transition-LoopNode-*************']
2025-07-01 09:39:20 - TransitionHandler - INFO - Completed transition transition-AgenticAI-************* in 3.41 seconds
2025-07-01 09:39:20 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 7, corr_id 1fcc8e97-8957-45b8-9529-154d89b8d86a):
2025-07-01 09:39:20 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 1fcc8e97-8957-45b8-9529-154d89b8d86a, response: {'result': 'Completed transition in 3.41 seconds', 'message': 'Transition completed in 3.41 seconds', 'transition_id': 'transition-AgenticAI-*************', 'status': 'time_logged', 'sequence': 7, 'workflow_status': 'running'}
2025-07-01 09:39:20 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-AgenticAI-*************: ['transition-LoopNode-*************']
2025-07-01 09:39:20 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 1
2025-07-01 09:39:20 - EnhancedWorkflowEngine - DEBUG - Results: [['transition-LoopNode-*************']]
2025-07-01 09:39:20 - EnhancedWorkflowEngine - DEBUG - 🔄 Orchestration engine received result for transition-AgenticAI-*************: ['transition-LoopNode-*************']
2025-07-01 09:39:20 - EnhancedWorkflowEngine - DEBUG - 🔄 Result type: <class 'list'>, is_list: True
2025-07-01 09:39:20 - EnhancedWorkflowEngine - INFO - Transition transition-AgenticAI-************* completed successfully: 1 next transitions
2025-07-01 09:39:20 - EnhancedWorkflowEngine - DEBUG - 🔄 Added to next_transitions_to_execute: ['transition-LoopNode-*************']
2025-07-01 09:39:20 - TransitionHandler - INFO - Resolved next transitions (direct transition IDs): ['transition-LoopNode-*************']
2025-07-01 09:39:20 - EnhancedWorkflowEngine - INFO - Adding transition transition-LoopNode-************* to pending (all dependencies met)
2025-07-01 09:39:20 - StateManager - DEBUG - Workflow active: {'transition-LoopNode-*************'}
2025-07-01 09:39:20 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:1fcc8e97-8957-45b8-9529-154d89b8d86a'
2025-07-01 09:39:20 - RedisManager - DEBUG - Set key 'workflow_state:1fcc8e97-8957-45b8-9529-154d89b8d86a' with TTL of 600 seconds
2025-07-01 09:39:20 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 1fcc8e97-8957-45b8-9529-154d89b8d86a. Will be archived to PostgreSQL when Redis key expires.
2025-07-01 09:39:20 - StateManager - DEBUG - Checking waiting transitions: {'transition-CombineTextComponent-***********63'}
2025-07-01 09:39:20 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-07-01 09:39:20 - StateManager - INFO - Cleared 1 pending transitions: {'transition-LoopNode-*************'}
2025-07-01 09:39:20 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-07-01 09:39:20 - StateManager - INFO - Terminated: False
2025-07-01 09:39:20 - StateManager - INFO - Pending transitions (0): []
2025-07-01 09:39:20 - StateManager - INFO - Waiting transitions (1): ['transition-CombineTextComponent-***********63']
2025-07-01 09:39:20 - StateManager - INFO - Completed transitions (2): ['transition-AgenticAI-*************', 'transition-AgenticAI-*************']
2025-07-01 09:39:20 - StateManager - INFO - Results stored for 2 transitions
2025-07-01 09:39:20 - StateManager - DEBUG - Workflow active: {'transition-CombineTextComponent-***********63'}
2025-07-01 09:39:20 - StateManager - INFO - Workflow status: active
2025-07-01 09:39:20 - StateManager - DEBUG - Workflow active: {'transition-CombineTextComponent-***********63'}
2025-07-01 09:39:20 - StateManager - INFO - Workflow status: active
2025-07-01 09:39:20 - StateManager - INFO - Workflow paused: False
2025-07-01 09:39:20 - StateManager - INFO - ==============================
2025-07-01 09:39:20 - TransitionHandler - INFO - Starting parallel execution of transition: transition-LoopNode-*************
2025-07-01 09:39:20 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 8, corr_id 1fcc8e97-8957-45b8-9529-154d89b8d86a):
2025-07-01 09:39:20 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 1fcc8e97-8957-45b8-9529-154d89b8d86a, response: {'result': 'Starting execution of transition: transition-LoopNode-*************', 'message': 'Starting execution...', 'transition_id': 'transition-LoopNode-*************', 'status': 'started', 'sequence': 8, 'workflow_status': 'running'}
2025-07-01 09:39:20 - TransitionHandler - EXECUTE - Transition 'transition-LoopNode-*************' (type=standard, execution_type=loop)
2025-07-01 09:39:20 - LoopExecutor - DEBUG - 🚨 set_orchestration_engine called with: <app.core_.executor_core.EnhancedWorkflowEngine object at 0x1070fbb60>
2025-07-01 09:39:20 - LoopExecutor - DEBUG - 🚨 Setting orchestration_engine on loop_body_chain_executor: <app.core_.executor_core.EnhancedWorkflowEngine object at 0x1070fbb60>
2025-07-01 09:39:20 - LoopExecutor - DEBUG - 🔗 Orchestration engine set for loop executor integration
2025-07-01 09:39:20 - TransitionHandler - DEBUG - 🔗 Set orchestration engine for loop executor
2025-07-01 09:39:20 - TransitionHandler - INFO - Using KafkaToolExecutor for execution_type: loop
2025-07-01 09:39:20 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-LoopNode-*************
2025-07-01 09:39:20 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: loop
2025-07-01 09:39:20 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for loop node
2025-07-01 09:39:21 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-07-01 09:39:21 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-07-01 09:39:21 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-07-01 09:39:21 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-07-01 09:39:21 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 1)
2025-07-01 09:39:21 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '0'}
2025-07-01 09:39:21 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 09:39:21 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: 0
2025-07-01 09:39:21 - WorkflowUtils - DEBUG - Found result.result: 0 (type: <class 'str'>)
2025-07-01 09:39:21 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 09:39:21 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 09:39:21 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-07-01 09:39:21 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-07-01 09:39:21 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '0'}
2025-07-01 09:39:21 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 09:39:21 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: 0
2025-07-01 09:39:21 - WorkflowUtils - DEBUG - Found result.result: 0 (type: <class 'str'>)
2025-07-01 09:39:21 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 09:39:21 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 09:39:21 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → start via path 'result': 0
2025-07-01 09:39:21 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-07-01 09:39:21 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-07-01 09:39:21 - WorkflowUtils - DEBUG - Filtering out field 'iteration_list' with empty collection: []
2025-07-01 09:39:21 - WorkflowUtils - DEBUG - Filtering out field 'start' with null/empty value: None
2025-07-01 09:39:21 - WorkflowUtils - INFO - 🧹 Parameter filtering: 14 → 12 fields (2 null/empty fields removed)
2025-07-01 09:39:21 - TransitionHandler - DEBUG - 📌 Added static parameter: source_type = number_range
2025-07-01 09:39:21 - TransitionHandler - DEBUG - 📌 Added static parameter: batch_size = 1
2025-07-01 09:39:21 - TransitionHandler - DEBUG - 📌 Added static parameter: end = 6
2025-07-01 09:39:21 - TransitionHandler - DEBUG - 📌 Added static parameter: step = 1
2025-07-01 09:39:21 - TransitionHandler - DEBUG - 📌 Added static parameter: parallel_execution = False
2025-07-01 09:39:21 - TransitionHandler - DEBUG - 📌 Added static parameter: max_concurrent = 3
2025-07-01 09:39:21 - TransitionHandler - DEBUG - 📌 Added static parameter: preserve_order = True
2025-07-01 09:39:21 - TransitionHandler - DEBUG - 📌 Added static parameter: iteration_timeout = 60
2025-07-01 09:39:21 - TransitionHandler - DEBUG - 📌 Added static parameter: aggregation_type = collect_all
2025-07-01 09:39:21 - TransitionHandler - DEBUG - 📌 Added static parameter: include_metadata = False
2025-07-01 09:39:21 - TransitionHandler - DEBUG - 📌 Added static parameter: on_iteration_error = retry_once
2025-07-01 09:39:21 - TransitionHandler - DEBUG - 📌 Added static parameter: include_errors = True
2025-07-01 09:39:21 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'start': '0', 'source_type': 'number_range', 'batch_size': '1', 'end': '6', 'step': '1', 'parallel_execution': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': False, 'on_iteration_error': 'retry_once', 'include_errors': True}
2025-07-01 09:39:21 - TransitionHandler - DEBUG - tool Parameters: {'start': '0', 'source_type': 'number_range', 'batch_size': '1', 'end': '6', 'step': '1', 'parallel_execution': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': False, 'on_iteration_error': 'retry_once', 'include_errors': True}
2025-07-01 09:39:21 - TransitionHandler - INFO - Invoking tool 'LoopNode' (tool_id: 1) for node 'LoopNode' in transition 'transition-LoopNode-*************' with parameters: {'start': '0', 'source_type': 'number_range', 'batch_size': '1', 'end': '6', 'step': '1', 'parallel_execution': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': False, 'on_iteration_error': 'retry_once', 'include_errors': True}
2025-07-01 09:39:21 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 9, corr_id 1fcc8e97-8957-45b8-9529-154d89b8d86a):
2025-07-01 09:39:21 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 1fcc8e97-8957-45b8-9529-154d89b8d86a, response: {'transition_id': 'transition-LoopNode-*************', 'node_id': 'LoopNode', 'tool_name': 'LoopNode', 'message': 'Connecting to server', 'result': 'Connecting to server LoopNode', 'status': 'connecting', 'sequence': 9, 'workflow_status': 'running'}
2025-07-01 09:39:21 - TransitionHandler - DEBUG - 🔄 Resolving loop config parameters for transition: transition-LoopNode-*************
2025-07-01 09:39:21 - TransitionHandler - DEBUG - ✅ Loop config parameter resolution completed for transition: transition-LoopNode-*************
2025-07-01 09:39:21 - TransitionHandler - DEBUG - 🔍 Detected exit transition: transition-MergeDataComponent-1751005477464 (has final/aggregated indicators)
2025-07-01 09:39:21 - TransitionHandler - DEBUG - 🔍 Auto-detected loop body transition: transition-CombineTextComponent-***********63 (has current_item/iteration indicators)
2025-07-01 09:39:21 - TransitionHandler - INFO - 🔍 Auto-detected and added loop body transitions to config: ['transition-CombineTextComponent-***********63']
2025-07-01 09:39:21 - LoopExecutor - INFO - 🚨 execute_tool called with transition_id: transition-LoopNode-*************
2025-07-01 09:39:21 - LoopExecutor - INFO - 🔄 Starting loop node execution for transition: transition-LoopNode-*************
2025-07-01 09:39:21 - LoopExecutor - DEBUG - 🔢 Detected number_range from 1 to 6 (step: 1), batch_size: 1
2025-07-01 09:39:21 - LoopExecutor - DEBUG - Parsed loop config: {'loop_type': 'context_independent', 'aggregation_config': {'type': 'list'}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': False}, 'iteration_source': {'type': 'range', 'data': {'start': 1, 'stop': 7, 'step': 1}, 'batch_size': 1, 'original': {'number_range': {'start': 1, 'end': 6}, 'step': 1}}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'concurrency': {'enabled': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'retry_config': {'max_retries': 1, 'retry_delay': 1.0}, 'iteration_behavior': 'independent', 'iteration_settings': {'parallel_execution': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'error_handling': {'on_iteration_error': 'retry_once', 'include_errors': True}, 'loop_body_transitions': ['transition-CombineTextComponent-***********63'], 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-***********63'], 'exit_transitions': ['transition-CombineTextComponent-1750920624318'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}}
2025-07-01 09:39:21 - LoopExecutor - DEBUG - Loop configuration validation passed
2025-07-01 09:39:21 - TransitionHandler - DEBUG - 📝 Registered loop executor for transition: transition-LoopNode-*************
2025-07-01 09:39:21 - LoopExecutor - DEBUG - 📝 Registered loop executor with transition handler for transition: transition-LoopNode-*************
2025-07-01 09:39:21 - LoopExecutor - INFO - Prepared 6 iterations from range source
2025-07-01 09:39:21 - LoopExecutor - INFO - 🔄 Initialized loop state for 6 iterations
2025-07-01 09:39:21 - LoopExecutor - INFO - 🔄 Immediately providing first iteration data: 1
2025-07-01 09:39:21 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in memory: 1
2025-07-01 09:39:21 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_0
2025-07-01 09:39:22 - RedisManager - DEBUG - Set key 'result:loop_iteration_0' with TTL of 900 seconds
2025-07-01 09:39:22 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-07-01 09:39:22 - StateManager - INFO - Marked transition loop_iteration_0 as completed (was_pending=False, was_waiting=False)
2025-07-01 09:39:22 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-***********63'}, completed={'transition-AgenticAI-*************', 'transition-AgenticAI-*************', 'loop_iteration_0'}
2025-07-01 09:39:22 - StateManager - DEBUG - Stored result for transition current_iteration in memory: 1
2025-07-01 09:39:22 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-07-01 09:39:23 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-07-01 09:39:23 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-07-01 09:39:23 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-07-01 09:39:23 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-***********63'}, completed={'current_iteration', 'transition-AgenticAI-*************', 'transition-AgenticAI-*************', 'loop_iteration_0'}
2025-07-01 09:39:23 - StateManager - DEBUG - Stored result for transition loop_iteration_transition-LoopNode-*************_0 in memory: {'current_item': 1, 'iteration_index': 0, 'iteration_metadata': {'timestamp': 337002.857587375, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-07-01 09:39:23 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_transition-LoopNode-*************_0
2025-07-01 09:39:24 - RedisManager - DEBUG - Set key 'result:loop_iteration_transition-LoopNode-*************_0' with TTL of 900 seconds
2025-07-01 09:39:24 - StateManager - DEBUG - Stored result for transition loop_iteration_transition-LoopNode-*************_0 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-07-01 09:39:24 - StateManager - INFO - Marked transition loop_iteration_transition-LoopNode-*************_0 as completed (was_pending=False, was_waiting=False)
2025-07-01 09:39:24 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-***********63'}, completed={'transition-AgenticAI-*************', 'loop_iteration_transition-LoopNode-*************_0', 'loop_iteration_0', 'current_iteration', 'transition-AgenticAI-*************'}
2025-07-01 09:39:24 - StateManager - DEBUG - Stored result for transition backup_transition-LoopNode-*************_iteration_0 in memory: {'current_item': 1, 'iteration_index': 0, 'iteration_metadata': {'timestamp': 337002.857587375, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-07-01 09:39:24 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:backup_transition-LoopNode-*************_iteration_0'
2025-07-01 09:39:25 - RedisManager - DEBUG - Set key 'result:backup_transition-LoopNode-*************_iteration_0' with TTL of 300 seconds
2025-07-01 09:39:25 - StateManager - DEBUG - Stored result for transition backup_transition-LoopNode-*************_iteration_0 in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-07-01 09:39:25 - StateManager - INFO - Marked transition backup_transition-LoopNode-*************_iteration_0 as completed (was_pending=False, was_waiting=False)
2025-07-01 09:39:25 - StateManager - DEBUG - Updated state: pending=set(), waiting={'transition-CombineTextComponent-***********63'}, completed={'backup_transition-LoopNode-*************_iteration_0', 'transition-AgenticAI-*************', 'loop_iteration_transition-LoopNode-*************_0', 'loop_iteration_0', 'current_iteration', 'transition-AgenticAI-*************'}
2025-07-01 09:39:25 - LoopExecutor - INFO - 🔄 ITERATION DATA INJECTION: Stored current iteration data under loop transition ID: transition-LoopNode-*************
2025-07-01 09:39:25 - LoopExecutor - INFO - 🔄 ITERATION DATA INJECTION: Also stored with backup keys: loop_iteration_transition-LoopNode-*************_0, backup_transition-LoopNode-*************_iteration_0
2025-07-01 09:39:25 - LoopExecutor - INFO - 🔄 ITERATION DATA INJECTION: Current iteration data: {'current_item': 1, 'iteration_index': 0, 'iteration_metadata': {'timestamp': 337002.857587375, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-07-01 09:39:25 - LoopExecutor - INFO - 🔍 VERIFICATION: Data stored in memory: {'current_item': 1, 'iteration_index': 0, 'iteration_metadata': {'timestamp': 337002.857587375, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-07-01 09:39:25 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-07-01 09:39:25 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-01 09:39:25 - StateManager - DEBUG - No result found in Redis for transition transition-LoopNode-*************. Trying PostgreSQL next.
2025-07-01 09:39:26 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-01 09:39:26 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-07-01 09:39:27 - PostgresManager - DEBUG - No result found for transition transition-LoopNode-************* in correlation 1fcc8e97-8957-45b8-9529-154d89b8d86a
2025-07-01 09:39:28 - StateManager - DEBUG - No result found in PostgreSQL for transition transition-LoopNode-*************. Trying in-memory next.
2025-07-01 09:39:28 - StateManager - INFO - Using in-memory result for transition transition-LoopNode-*************.
2025-07-01 09:39:28 - StateManager - DEBUG - Extracted results for 3 tools in transition transition-LoopNode-*************
2025-07-01 09:39:28 - LoopExecutor - INFO - 🔍 VERIFICATION: Data accessible from Redis: {'current_item': 1, 'iteration_index': 0, 'iteration_metadata': {'timestamp': 337002.857587375, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 6}}
2025-07-01 09:39:28 - LoopExecutor - DEBUG - 💾 Injected iteration payload into state: loop_iteration_0
2025-07-01 09:39:28 - LoopExecutor - INFO - 🚨 _execute_loop_workflow called
2025-07-01 09:39:28 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_state keys: ['loop_config', 'transition_id', 'iteration_data', 'loop_context', 'input_data']
2025-07-01 09:39:28 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_config keys: ['loop_type', 'aggregation_config', 'result_aggregation', 'iteration_source', 'exit_condition', 'concurrency', 'retry_config', 'iteration_behavior', 'iteration_settings', 'error_handling', 'loop_body_transitions', 'loop_body_configuration']
2025-07-01 09:39:28 - LoopExecutor - DEBUG - 🔍 DEBUG: loop_body_config: {'entry_transitions': ['transition-CombineTextComponent-***********63'], 'exit_transitions': ['transition-CombineTextComponent-1750920624318'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}
2025-07-01 09:39:28 - LoopExecutor - DEBUG - 🔍 DEBUG: entry_transitions: ['transition-CombineTextComponent-***********63']
2025-07-01 09:39:28 - LoopExecutor - DEBUG - 🔍 DEBUG: exit_transitions: ['transition-CombineTextComponent-1750920624318']
2025-07-01 09:39:28 - LoopExecutor - DEBUG - 🚨 About to start iteration loop with 6 items
2025-07-01 09:39:28 - LoopExecutor - INFO - 🔄 Starting iteration 1/6
2025-07-01 09:39:28 - LoopExecutor - DEBUG - 🚨 STEP 4: About to execute loop body chain for iteration 0
2025-07-01 09:39:28 - LoopExecutor - DEBUG - 🚨 About to call _execute_loop_body_chain with loop_body_chain_executor: <app.services.loop_executor.loop_body_chain_executor.LoopBodyChainExecutor object at 0x107224050>
2025-07-01 09:39:28 - LoopExecutor - INFO - 🚨 _execute_loop_body_chain called for iteration 0
2025-07-01 09:39:28 - LoopExecutor - DEBUG - 🔍 Current loop config keys: ['loop_type', 'aggregation_config', 'result_aggregation', 'iteration_source', 'exit_condition', 'concurrency', 'retry_config', 'iteration_behavior', 'iteration_settings', 'error_handling', 'loop_body_transitions', 'loop_body_configuration']
2025-07-01 09:39:28 - LoopExecutor - DEBUG - 🔍 Loop body config: {'entry_transitions': ['transition-CombineTextComponent-***********63'], 'exit_transitions': ['transition-CombineTextComponent-1750920624318'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}
2025-07-01 09:39:28 - LoopExecutor - DEBUG - 🔍 Loop body transitions: ['transition-CombineTextComponent-***********63']
2025-07-01 09:39:28 - LoopExecutor - DEBUG - 🚨 About to call loop_body_chain_executor.execute_loop_body_chain with transition_id: transition-LoopNode-*************
2025-07-01 09:39:28 - LoopExecutor - DEBUG - 🚨 Method exists: True
2025-07-01 09:39:28 - LoopExecutor - DEBUG - 🚨 Adding 10-second timeout to chain executor call
2025-07-01 09:39:28 - LoopExecutor - DEBUG - 🚨 About to call method directly...
2025-07-01 09:39:28 - StateManager - DEBUG - Using provided database connections
2025-07-01 09:39:28 - RedisEventListener - INFO - Workflow state manager reference updated
2025-07-01 09:39:28 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-07-01 09:39:28 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-07-01 09:39:28 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-07-01 09:39:28 - StateManager - INFO - WorkflowStateManager initialized
2025-07-01 09:39:28 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1750505490787: ['transition-CombineTextComponent-***********63']
2025-07-01 09:39:28 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1751001474606: ['transition-AgenticAI-1750505490787']
2025-07-01 09:39:28 - StateManager - DEBUG - 🔄 Detected potential loop body transition transition-CombineTextComponent-***********63 depending on loop node transition-LoopNode-*************
2025-07-01 09:39:28 - StateManager - DEBUG - 🔄 Detected loop body transition transition-CombineTextComponent-***********63 depending on loop node transition-LoopNode-*************
2025-07-01 09:39:28 - StateManager - DEBUG - 🔄 Loop body transition transition-CombineTextComponent-***********63 will wait for loop node transition-LoopNode-************* to provide iteration data
2025-07-01 09:39:28 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-***********63: ['transition-AgenticAI-*************', 'transition-AgenticAI-*************', 'transition-LoopNode-*************']
2025-07-01 09:39:28 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-1750920624318: ['transition-AgenticAI-1750505490787', 'transition-AgenticAI-1751001471087', 'transition-AgenticAI-1751001474606']
2025-07-01 09:39:28 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1751001471087: ['transition-AgenticAI-1750505490787']
2025-07-01 09:39:28 - StateManager - DEBUG - Stored iteration data for key current_iteration: {'current_item': 1, 'iteration_context': {'iteration_index': 0, 'total_iterations': 6, 'loop_config': {'loop_type': 'context_independent', 'aggregation_config': {'type': 'list'}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': False}, 'iteration_source': {'type': 'range', 'data': {'start': 1, 'stop': 7, 'step': 1}, 'batch_size': 1, 'original': {'number_range': {'start': 1, 'end': 6}, 'step': 1}}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'concurrency': {'enabled': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'retry_config': {'max_retries': 1, 'retry_delay': 1.0}, 'iteration_behavior': 'independent', 'iteration_settings': {'parallel_execution': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'error_handling': {'on_iteration_error': 'retry_once', 'include_errors': True}, 'loop_body_transitions': ['transition-CombineTextComponent-***********63'], 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-***********63'], 'exit_transitions': ['transition-CombineTextComponent-1750920624318'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}}}}
2025-07-01 09:39:28 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-***********63
2025-07-01 09:39:28 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 10, corr_id 1fcc8e97-8957-45b8-9529-154d89b8d86a):
2025-07-01 09:39:28 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 1fcc8e97-8957-45b8-9529-154d89b8d86a, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-***********63', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-***********63', 'status': 'started', 'sequence': 10, 'workflow_status': 'running'}
2025-07-01 09:39:28 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-***********63' (type=standard, execution_type=Components)
2025-07-01 09:39:28 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-07-01 09:39:28 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-***********63
2025-07-01 09:39:28 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-07-01 09:39:28 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-07-01 09:39:29 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-07-01 09:39:29 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-07-01 09:39:29 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-07-01 09:39:29 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-07-01 09:39:30 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-07-01 09:39:30 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-07-01 09:39:30 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-07-01 09:39:30 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-07-01 09:39:31 - StateManager - DEBUG - No result found in Redis for transition transition-LoopNode-*************. Trying PostgreSQL next.
2025-07-01 09:39:33 - PostgresManager - DEBUG - No result found for transition transition-LoopNode-************* in correlation 1fcc8e97-8957-45b8-9529-154d89b8d86a_loop_transition-LoopNode-*************_iteration_0
2025-07-01 09:39:34 - StateManager - DEBUG - No result found in PostgreSQL for transition transition-LoopNode-*************. Trying in-memory next.
2025-07-01 09:39:34 - StateManager - DEBUG - No result found in Redis, PostgreSQL, or memory for transition transition-LoopNode-*************
2025-07-01 09:39:34 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 1)
2025-07-01 09:39:34 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 2)
2025-07-01 09:39:34 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-07-01 09:39:34 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}
2025-07-01 09:39:34 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 09:39:34 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 09:39:34 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] (type: <class 'str'>)
2025-07-01 09:39:34 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 09:39:34 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 09:39:34 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}
2025-07-01 09:39:34 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 09:39:34 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 09:39:34 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] (type: <class 'str'>)
2025-07-01 09:39:34 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 09:39:34 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 09:39:34 - WorkflowUtils - INFO - 🔍 Handle mapping validation: partially_compatible (2/3 compatible)
2025-07-01 09:39:34 - TransitionHandler - INFO - 🔍 Handle validation: partially_compatible (2/3 compatible)
2025-07-01 09:39:34 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}
2025-07-01 09:39:34 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 09:39:34 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 09:39:34 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] (type: <class 'str'>)
2025-07-01 09:39:34 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 09:39:34 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 09:39:34 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → input_2 via path 'result': [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 09:39:34 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}
2025-07-01 09:39:34 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 09:39:34 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 09:39:34 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] (type: <class 'str'>)
2025-07-01 09:39:34 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 09:39:34 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 09:39:34 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → input_1 via path 'result': [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 09:39:34 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {}
2025-07-01 09:39:34 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: []
2025-07-01 09:39:34 - WorkflowUtils - DEBUG - Could not find result.result or result in source_results for handle current_item
2025-07-01 09:39:34 - WorkflowUtils - DEBUG - Path tracking - Source results structure
2025-07-01 09:39:34 - WorkflowUtils - WARNING - ❌ Handle mapping failed: current_item → main_input (no data found)
2025-07-01 09:39:34 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 2/3 successful
2025-07-01 09:39:34 - WorkflowUtils - WARNING - ⚠️ 1 universal handle mappings failed - this may cause tool execution errors
2025-07-01 09:39:34 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 2/3 successful
2025-07-01 09:39:34 - TransitionHandler - WARNING - ❌ Failed mapping: current_item → main_input (Error: No data found for handle current_item)
2025-07-01 09:39:34 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-07-01 09:39:34 - WorkflowUtils - DEBUG - Filtering out field 'input_1' with null/empty value: None
2025-07-01 09:39:34 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: None
2025-07-01 09:39:34 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-07-01 09:39:34 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-07-01 09:39:34 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-07-01 09:39:34 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-07-01 09:39:34 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-07-01 09:39:34 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-07-01 09:39:34 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-07-01 09:39:34 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-07-01 09:39:34 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 2 fields (11 null/empty fields removed)
2025-07-01 09:39:34 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-07-01 09:39:34 - TransitionHandler - DEBUG - 📌 Added static parameter: separator = \n
2025-07-01 09:39:34 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'input_2': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'input_1': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'num_additional_inputs': '1', 'separator': '\\n'}
2025-07-01 09:39:34 - TransitionHandler - DEBUG - tool Parameters: {'input_2': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'input_1': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'num_additional_inputs': '1', 'separator': '\\n'}
2025-07-01 09:39:34 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-***********63' with parameters: {'input_2': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'input_1': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'num_additional_inputs': '1', 'separator': '\\n'}
2025-07-01 09:39:34 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 11, corr_id 1fcc8e97-8957-45b8-9529-154d89b8d86a):
2025-07-01 09:39:34 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 1fcc8e97-8957-45b8-9529-154d89b8d86a, response: {'transition_id': 'transition-CombineTextComponent-***********63', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 11, 'workflow_status': 'running'}
2025-07-01 09:39:34 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 8f613aba-13be-4d04-ade1-bb6200c49b72) using provided producer.
2025-07-01 09:39:34 - NodeExecutor - DEBUG - Added correlation_id 1fcc8e97-8957-45b8-9529-154d89b8d86a to payload
2025-07-01 09:39:34 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-***********63 to payload
2025-07-01 09:39:34 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'input_2': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'input_1': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'num_additional_inputs': '1', 'separator': '\\n'}, 'request_id': '8f613aba-13be-4d04-ade1-bb6200c49b72', 'correlation_id': '1fcc8e97-8957-45b8-9529-154d89b8d86a', 'transition_id': 'transition-CombineTextComponent-***********63'}
2025-07-01 09:39:34 - NodeExecutor - DEBUG - Request 8f613aba-13be-4d04-ade1-bb6200c49b72 sent successfully using provided producer.
2025-07-01 09:39:34 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 8f613aba-13be-4d04-ade1-bb6200c49b72...
2025-07-01 09:39:38 - StateManager - DEBUG - Cleared iteration data for key current_iteration
2025-07-01 09:39:38 - LoopExecutor - ERROR - 🚨 Chain executor call TIMED OUT after 10 seconds - this confirms a deadlock!
2025-07-01 09:39:38 - LoopExecutor - ERROR - ❌ Loop body chain execution failed for iteration 0: 
2025-07-01 09:39:38 - LoopExecutor - ERROR - ❌ Iteration 1 failed: 
2025-07-01 09:39:38 - LoopExecutor - INFO - 🔄 Starting iteration 2/6
2025-07-01 09:39:38 - LoopExecutor - DEBUG - 🚨 STEP 4: About to execute loop body chain for iteration 1
2025-07-01 09:39:38 - LoopExecutor - DEBUG - 🚨 About to call _execute_loop_body_chain with loop_body_chain_executor: <app.services.loop_executor.loop_body_chain_executor.LoopBodyChainExecutor object at 0x107224050>
2025-07-01 09:39:38 - LoopExecutor - INFO - 🚨 _execute_loop_body_chain called for iteration 1
2025-07-01 09:39:38 - LoopExecutor - DEBUG - 🔍 Current loop config keys: ['loop_type', 'aggregation_config', 'result_aggregation', 'iteration_source', 'exit_condition', 'concurrency', 'retry_config', 'iteration_behavior', 'iteration_settings', 'error_handling', 'loop_body_transitions', 'loop_body_configuration']
2025-07-01 09:39:38 - LoopExecutor - DEBUG - 🔍 Loop body config: {'entry_transitions': ['transition-CombineTextComponent-***********63'], 'exit_transitions': ['transition-CombineTextComponent-1750920624318'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}
2025-07-01 09:39:38 - LoopExecutor - DEBUG - 🔍 Loop body transitions: ['transition-CombineTextComponent-***********63']
2025-07-01 09:39:38 - LoopExecutor - DEBUG - 🚨 About to call loop_body_chain_executor.execute_loop_body_chain with transition_id: transition-LoopNode-*************
2025-07-01 09:39:38 - LoopExecutor - DEBUG - 🚨 Method exists: True
2025-07-01 09:39:38 - LoopExecutor - DEBUG - 🚨 Adding 10-second timeout to chain executor call
2025-07-01 09:39:38 - LoopExecutor - DEBUG - 🚨 About to call method directly...
2025-07-01 09:39:38 - StateManager - DEBUG - Using provided database connections
2025-07-01 09:39:38 - RedisEventListener - INFO - Workflow state manager reference updated
2025-07-01 09:39:38 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-07-01 09:39:38 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-07-01 09:39:38 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-07-01 09:39:38 - StateManager - INFO - WorkflowStateManager initialized
2025-07-01 09:39:38 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1750505490787: ['transition-CombineTextComponent-***********63']
2025-07-01 09:39:38 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1751001474606: ['transition-AgenticAI-1750505490787']
2025-07-01 09:39:38 - StateManager - DEBUG - 🔄 Detected potential loop body transition transition-CombineTextComponent-***********63 depending on loop node transition-LoopNode-*************
2025-07-01 09:39:38 - StateManager - DEBUG - 🔄 Detected loop body transition transition-CombineTextComponent-***********63 depending on loop node transition-LoopNode-*************
2025-07-01 09:39:38 - StateManager - DEBUG - 🔄 Loop body transition transition-CombineTextComponent-***********63 will wait for loop node transition-LoopNode-************* to provide iteration data
2025-07-01 09:39:38 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-***********63: ['transition-AgenticAI-*************', 'transition-AgenticAI-*************', 'transition-LoopNode-*************']
2025-07-01 09:39:38 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-1750920624318: ['transition-AgenticAI-1750505490787', 'transition-AgenticAI-1751001471087', 'transition-AgenticAI-1751001474606']
2025-07-01 09:39:38 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1751001471087: ['transition-AgenticAI-1750505490787']
2025-07-01 09:39:38 - StateManager - DEBUG - Stored iteration data for key current_iteration: {'current_item': 2, 'iteration_context': {'iteration_index': 1, 'total_iterations': 6, 'loop_config': {'loop_type': 'context_independent', 'aggregation_config': {'type': 'list'}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': False}, 'iteration_source': {'type': 'range', 'data': {'start': 1, 'stop': 7, 'step': 1}, 'batch_size': 1, 'original': {'number_range': {'start': 1, 'end': 6}, 'step': 1}}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'concurrency': {'enabled': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'retry_config': {'max_retries': 1, 'retry_delay': 1.0}, 'iteration_behavior': 'independent', 'iteration_settings': {'parallel_execution': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'error_handling': {'on_iteration_error': 'retry_once', 'include_errors': True}, 'loop_body_transitions': ['transition-CombineTextComponent-***********63'], 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-***********63'], 'exit_transitions': ['transition-CombineTextComponent-1750920624318'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}}}}
2025-07-01 09:39:38 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-***********63
2025-07-01 09:39:38 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 12, corr_id 1fcc8e97-8957-45b8-9529-154d89b8d86a):
2025-07-01 09:39:39 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 1fcc8e97-8957-45b8-9529-154d89b8d86a, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-***********63', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-***********63', 'status': 'started', 'sequence': 12, 'workflow_status': 'running'}
2025-07-01 09:39:39 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-***********63' (type=standard, execution_type=Components)
2025-07-01 09:39:39 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-07-01 09:39:39 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-***********63
2025-07-01 09:39:39 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-07-01 09:39:39 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-07-01 09:39:39 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-07-01 09:39:39 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-07-01 09:39:39 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-07-01 09:39:39 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-07-01 09:39:40 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-07-01 09:39:40 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-07-01 09:39:40 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-07-01 09:39:40 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-07-01 09:39:41 - StateManager - DEBUG - No result found in Redis for transition transition-LoopNode-*************. Trying PostgreSQL next.
2025-07-01 09:39:43 - PostgresManager - DEBUG - No result found for transition transition-LoopNode-************* in correlation 1fcc8e97-8957-45b8-9529-154d89b8d86a_loop_transition-LoopNode-*************_iteration_1
2025-07-01 09:39:43 - StateManager - DEBUG - No result found in PostgreSQL for transition transition-LoopNode-*************. Trying in-memory next.
2025-07-01 09:39:43 - StateManager - DEBUG - No result found in Redis, PostgreSQL, or memory for transition transition-LoopNode-*************
2025-07-01 09:39:43 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 1)
2025-07-01 09:39:43 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 2)
2025-07-01 09:39:43 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-07-01 09:39:43 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}
2025-07-01 09:39:43 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 09:39:43 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 09:39:43 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] (type: <class 'str'>)
2025-07-01 09:39:43 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 09:39:43 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 09:39:43 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}
2025-07-01 09:39:43 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 09:39:43 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 09:39:43 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] (type: <class 'str'>)
2025-07-01 09:39:43 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 09:39:43 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 09:39:43 - WorkflowUtils - INFO - 🔍 Handle mapping validation: partially_compatible (2/3 compatible)
2025-07-01 09:39:43 - TransitionHandler - INFO - 🔍 Handle validation: partially_compatible (2/3 compatible)
2025-07-01 09:39:43 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}
2025-07-01 09:39:43 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 09:39:43 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 09:39:43 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] (type: <class 'str'>)
2025-07-01 09:39:43 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 09:39:43 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 09:39:43 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → input_2 via path 'result': [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 09:39:43 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}
2025-07-01 09:39:43 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 09:39:43 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 09:39:43 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] (type: <class 'str'>)
2025-07-01 09:39:43 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 09:39:43 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 09:39:43 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → input_1 via path 'result': [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 09:39:43 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {}
2025-07-01 09:39:43 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: []
2025-07-01 09:39:43 - WorkflowUtils - DEBUG - Could not find result.result or result in source_results for handle current_item
2025-07-01 09:39:43 - WorkflowUtils - DEBUG - Path tracking - Source results structure
2025-07-01 09:39:43 - WorkflowUtils - WARNING - ❌ Handle mapping failed: current_item → main_input (no data found)
2025-07-01 09:39:43 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 2/3 successful
2025-07-01 09:39:43 - WorkflowUtils - WARNING - ⚠️ 1 universal handle mappings failed - this may cause tool execution errors
2025-07-01 09:39:43 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 2/3 successful
2025-07-01 09:39:43 - TransitionHandler - WARNING - ❌ Failed mapping: current_item → main_input (Error: No data found for handle current_item)
2025-07-01 09:39:43 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-07-01 09:39:43 - WorkflowUtils - DEBUG - Filtering out field 'input_1' with null/empty value: None
2025-07-01 09:39:43 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: None
2025-07-01 09:39:43 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-07-01 09:39:43 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-07-01 09:39:43 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-07-01 09:39:43 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-07-01 09:39:43 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-07-01 09:39:43 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-07-01 09:39:43 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-07-01 09:39:43 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-07-01 09:39:43 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 2 fields (11 null/empty fields removed)
2025-07-01 09:39:43 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-07-01 09:39:43 - TransitionHandler - DEBUG - 📌 Added static parameter: separator = \n
2025-07-01 09:39:43 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'input_2': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'input_1': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'num_additional_inputs': '1', 'separator': '\\n'}
2025-07-01 09:39:43 - TransitionHandler - DEBUG - tool Parameters: {'input_2': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'input_1': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'num_additional_inputs': '1', 'separator': '\\n'}
2025-07-01 09:39:43 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-***********63' with parameters: {'input_2': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'input_1': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'num_additional_inputs': '1', 'separator': '\\n'}
2025-07-01 09:39:43 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 13, corr_id 1fcc8e97-8957-45b8-9529-154d89b8d86a):
2025-07-01 09:39:43 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 1fcc8e97-8957-45b8-9529-154d89b8d86a, response: {'transition_id': 'transition-CombineTextComponent-***********63', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 13, 'workflow_status': 'running'}
2025-07-01 09:39:43 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 36a631d1-2cb2-4ac3-bb43-2a626fac121a) using provided producer.
2025-07-01 09:39:43 - NodeExecutor - DEBUG - Added correlation_id 1fcc8e97-8957-45b8-9529-154d89b8d86a to payload
2025-07-01 09:39:43 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-***********63 to payload
2025-07-01 09:39:43 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'input_2': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'input_1': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'num_additional_inputs': '1', 'separator': '\\n'}, 'request_id': '36a631d1-2cb2-4ac3-bb43-2a626fac121a', 'correlation_id': '1fcc8e97-8957-45b8-9529-154d89b8d86a', 'transition_id': 'transition-CombineTextComponent-***********63'}
2025-07-01 09:39:43 - NodeExecutor - DEBUG - Request 36a631d1-2cb2-4ac3-bb43-2a626fac121a sent successfully using provided producer.
2025-07-01 09:39:43 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 36a631d1-2cb2-4ac3-bb43-2a626fac121a...
2025-07-01 09:39:44 - NodeExecutor - DEBUG - Result consumer received message: Offset=978
2025-07-01 09:39:44 - NodeExecutor - WARNING - Received result for already completed/cancelled request_id 8f613aba-13be-4d04-ade1-bb6200c49b72
2025-07-01 09:39:44 - NodeExecutor - DEBUG - Result consumer received message: Offset=979
2025-07-01 09:39:44 - NodeExecutor - WARNING - Received error response for request_id 36a631d1-2cb2-4ac3-bb43-2a626fac121a: Error combining text for request_id 36a631d1-2cb2-4ac3-bb43-2a626fac121a: "Required field 'main_input' not found in parameters"
2025-07-01 09:39:44 - NodeExecutor - ERROR - Error during node execution 36a631d1-2cb2-4ac3-bb43-2a626fac121a: Node execution failed: Error combining text for request_id 36a631d1-2cb2-4ac3-bb43-2a626fac121a: "Required field 'main_input' not found in parameters"
Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id 36a631d1-2cb2-4ac3-bb43-2a626fac121a: "Required field 'main_input' not found in parameters"
2025-07-01 09:39:44 - TransitionHandler - ERROR - Tool execution failed for tool 'CombineTextComponent' (tool_id: 1) in node 'CombineTextComponent' of transition 'transition-CombineTextComponent-***********63': Node execution failed: Error combining text for request_id 36a631d1-2cb2-4ac3-bb43-2a626fac121a: "Required field 'main_input' not found in parameters"Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 492, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 361, in execute_tool
    raise e
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id 36a631d1-2cb2-4ac3-bb43-2a626fac121a: "Required field 'main_input' not found in parameters"

2025-07-01 09:39:44 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 14, corr_id 1fcc8e97-8957-45b8-9529-154d89b8d86a):
2025-07-01 09:39:44 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 1fcc8e97-8957-45b8-9529-154d89b8d86a, response: {'transition_id': 'transition-CombineTextComponent-***********63', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition faced an error during execution.', 'result': '[ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 36a631d1-2cb2-4ac3-bb43-2a626fac121a: "Required field \'main_input\' not found in parameters"', 'status': 'failed', 'sequence': 14, 'workflow_status': 'running'}
2025-07-01 09:39:44 - TransitionHandler - ERROR - Exception in transition transition-CombineTextComponent-***********63: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 36a631d1-2cb2-4ac3-bb43-2a626fac121a: "Required field 'main_input' not found in parameters"
2025-07-01 09:39:44 - StateManager - DEBUG - Cleared iteration data for key current_iteration
2025-07-01 09:39:44 - LoopExecutor - ERROR - 🚨 Chain executor call failed with exception: LoopBodyChainExecutor._check_waiting_transitions_ready() takes 2 positional arguments but 4 were given
2025-07-01 09:39:44 - LoopExecutor - ERROR - 🚨 Full traceback: Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_executor.py", line 1890, in _execute_loop_body_chain
    result = await asyncio.wait_for(
             ^^^^^^^^^^^^^^^^^^^^^^^
    ...<8 lines>...
    )
    ^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/tasks.py", line 507, in wait_for
    return await fut
           ^^^^^^^^^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_body_chain_executor.py", line 100, in execute_loop_body_chain
    result = await self._execute_chain_with_isolated_state(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        isolated_state_manager, chain_id, entry_transitions, exit_transitions
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_body_chain_executor.py", line 273, in _execute_chain_with_isolated_state
    newly_ready = self._check_waiting_transitions_ready(
        waiting_transitions, completed_transitions, chain_id
    )
TypeError: LoopBodyChainExecutor._check_waiting_transitions_ready() takes 2 positional arguments but 4 were given

2025-07-01 09:39:44 - LoopExecutor - ERROR - ❌ Loop body chain execution failed for iteration 1: LoopBodyChainExecutor._check_waiting_transitions_ready() takes 2 positional arguments but 4 were given
2025-07-01 09:39:44 - LoopExecutor - ERROR - ❌ Iteration 2 failed: LoopBodyChainExecutor._check_waiting_transitions_ready() takes 2 positional arguments but 4 were given
2025-07-01 09:39:44 - LoopExecutor - INFO - 🔄 Starting iteration 3/6
2025-07-01 09:39:44 - LoopExecutor - DEBUG - 🚨 STEP 4: About to execute loop body chain for iteration 2
2025-07-01 09:39:44 - LoopExecutor - DEBUG - 🚨 About to call _execute_loop_body_chain with loop_body_chain_executor: <app.services.loop_executor.loop_body_chain_executor.LoopBodyChainExecutor object at 0x107224050>
2025-07-01 09:39:44 - LoopExecutor - INFO - 🚨 _execute_loop_body_chain called for iteration 2
2025-07-01 09:39:44 - LoopExecutor - DEBUG - 🔍 Current loop config keys: ['loop_type', 'aggregation_config', 'result_aggregation', 'iteration_source', 'exit_condition', 'concurrency', 'retry_config', 'iteration_behavior', 'iteration_settings', 'error_handling', 'loop_body_transitions', 'loop_body_configuration']
2025-07-01 09:39:44 - LoopExecutor - DEBUG - 🔍 Loop body config: {'entry_transitions': ['transition-CombineTextComponent-***********63'], 'exit_transitions': ['transition-CombineTextComponent-1750920624318'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}
2025-07-01 09:39:44 - LoopExecutor - DEBUG - 🔍 Loop body transitions: ['transition-CombineTextComponent-***********63']
2025-07-01 09:39:44 - LoopExecutor - DEBUG - 🚨 About to call loop_body_chain_executor.execute_loop_body_chain with transition_id: transition-LoopNode-*************
2025-07-01 09:39:44 - LoopExecutor - DEBUG - 🚨 Method exists: True
2025-07-01 09:39:44 - LoopExecutor - DEBUG - 🚨 Adding 10-second timeout to chain executor call
2025-07-01 09:39:44 - LoopExecutor - DEBUG - 🚨 About to call method directly...
2025-07-01 09:39:44 - StateManager - DEBUG - Using provided database connections
2025-07-01 09:39:44 - RedisEventListener - INFO - Workflow state manager reference updated
2025-07-01 09:39:44 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-07-01 09:39:44 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-07-01 09:39:45 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-07-01 09:39:45 - StateManager - INFO - WorkflowStateManager initialized
2025-07-01 09:39:45 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1750505490787: ['transition-CombineTextComponent-***********63']
2025-07-01 09:39:45 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1751001474606: ['transition-AgenticAI-1750505490787']
2025-07-01 09:39:45 - StateManager - DEBUG - 🔄 Detected potential loop body transition transition-CombineTextComponent-***********63 depending on loop node transition-LoopNode-*************
2025-07-01 09:39:45 - StateManager - DEBUG - 🔄 Detected loop body transition transition-CombineTextComponent-***********63 depending on loop node transition-LoopNode-*************
2025-07-01 09:39:45 - StateManager - DEBUG - 🔄 Loop body transition transition-CombineTextComponent-***********63 will wait for loop node transition-LoopNode-************* to provide iteration data
2025-07-01 09:39:45 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-***********63: ['transition-AgenticAI-*************', 'transition-AgenticAI-*************', 'transition-LoopNode-*************']
2025-07-01 09:39:45 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-1750920624318: ['transition-AgenticAI-1750505490787', 'transition-AgenticAI-1751001471087', 'transition-AgenticAI-1751001474606']
2025-07-01 09:39:45 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1751001471087: ['transition-AgenticAI-1750505490787']
2025-07-01 09:39:45 - StateManager - DEBUG - Stored iteration data for key current_iteration: {'current_item': 3, 'iteration_context': {'iteration_index': 2, 'total_iterations': 6, 'loop_config': {'loop_type': 'context_independent', 'aggregation_config': {'type': 'list'}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': False}, 'iteration_source': {'type': 'range', 'data': {'start': 1, 'stop': 7, 'step': 1}, 'batch_size': 1, 'original': {'number_range': {'start': 1, 'end': 6}, 'step': 1}}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'concurrency': {'enabled': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'retry_config': {'max_retries': 1, 'retry_delay': 1.0}, 'iteration_behavior': 'independent', 'iteration_settings': {'parallel_execution': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'error_handling': {'on_iteration_error': 'retry_once', 'include_errors': True}, 'loop_body_transitions': ['transition-CombineTextComponent-***********63'], 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-***********63'], 'exit_transitions': ['transition-CombineTextComponent-1750920624318'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}}}}
2025-07-01 09:39:45 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-***********63
2025-07-01 09:39:45 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 15, corr_id 1fcc8e97-8957-45b8-9529-154d89b8d86a):
2025-07-01 09:39:45 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 1fcc8e97-8957-45b8-9529-154d89b8d86a, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-***********63', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-***********63', 'status': 'started', 'sequence': 15, 'workflow_status': 'running'}
2025-07-01 09:39:45 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-***********63' (type=standard, execution_type=Components)
2025-07-01 09:39:45 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-07-01 09:39:45 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-***********63
2025-07-01 09:39:45 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-07-01 09:39:45 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-07-01 09:39:46 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-07-01 09:39:46 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-07-01 09:39:46 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-07-01 09:39:46 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-07-01 09:39:47 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-07-01 09:39:47 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-07-01 09:39:47 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-07-01 09:39:47 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-07-01 09:39:48 - StateManager - DEBUG - No result found in Redis for transition transition-LoopNode-*************. Trying PostgreSQL next.
2025-07-01 09:39:50 - PostgresManager - DEBUG - No result found for transition transition-LoopNode-************* in correlation 1fcc8e97-8957-45b8-9529-154d89b8d86a_loop_transition-LoopNode-*************_iteration_2
2025-07-01 09:39:51 - StateManager - DEBUG - No result found in PostgreSQL for transition transition-LoopNode-*************. Trying in-memory next.
2025-07-01 09:39:51 - StateManager - DEBUG - No result found in Redis, PostgreSQL, or memory for transition transition-LoopNode-*************
2025-07-01 09:39:51 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 1)
2025-07-01 09:39:51 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 2)
2025-07-01 09:39:51 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-07-01 09:39:51 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}
2025-07-01 09:39:51 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 09:39:51 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 09:39:51 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] (type: <class 'str'>)
2025-07-01 09:39:51 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 09:39:51 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 09:39:51 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}
2025-07-01 09:39:51 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 09:39:51 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 09:39:51 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] (type: <class 'str'>)
2025-07-01 09:39:51 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 09:39:51 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 09:39:51 - WorkflowUtils - INFO - 🔍 Handle mapping validation: partially_compatible (2/3 compatible)
2025-07-01 09:39:51 - TransitionHandler - INFO - 🔍 Handle validation: partially_compatible (2/3 compatible)
2025-07-01 09:39:51 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}
2025-07-01 09:39:51 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 09:39:51 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 09:39:51 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] (type: <class 'str'>)
2025-07-01 09:39:51 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 09:39:51 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 09:39:51 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → input_2 via path 'result': [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 09:39:51 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}
2025-07-01 09:39:51 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 09:39:51 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 09:39:51 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] (type: <class 'str'>)
2025-07-01 09:39:51 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 09:39:51 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 09:39:51 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → input_1 via path 'result': [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 09:39:51 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {}
2025-07-01 09:39:51 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: []
2025-07-01 09:39:51 - WorkflowUtils - DEBUG - Could not find result.result or result in source_results for handle current_item
2025-07-01 09:39:51 - WorkflowUtils - DEBUG - Path tracking - Source results structure
2025-07-01 09:39:51 - WorkflowUtils - WARNING - ❌ Handle mapping failed: current_item → main_input (no data found)
2025-07-01 09:39:51 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 2/3 successful
2025-07-01 09:39:51 - WorkflowUtils - WARNING - ⚠️ 1 universal handle mappings failed - this may cause tool execution errors
2025-07-01 09:39:51 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 2/3 successful
2025-07-01 09:39:51 - TransitionHandler - WARNING - ❌ Failed mapping: current_item → main_input (Error: No data found for handle current_item)
2025-07-01 09:39:51 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-07-01 09:39:51 - WorkflowUtils - DEBUG - Filtering out field 'input_1' with null/empty value: None
2025-07-01 09:39:51 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: None
2025-07-01 09:39:51 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-07-01 09:39:51 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-07-01 09:39:51 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-07-01 09:39:51 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-07-01 09:39:51 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-07-01 09:39:51 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-07-01 09:39:51 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-07-01 09:39:51 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-07-01 09:39:51 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 2 fields (11 null/empty fields removed)
2025-07-01 09:39:51 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-07-01 09:39:51 - TransitionHandler - DEBUG - 📌 Added static parameter: separator = \n
2025-07-01 09:39:51 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'input_2': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'input_1': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'num_additional_inputs': '1', 'separator': '\\n'}
2025-07-01 09:39:51 - TransitionHandler - DEBUG - tool Parameters: {'input_2': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'input_1': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'num_additional_inputs': '1', 'separator': '\\n'}
2025-07-01 09:39:51 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-***********63' with parameters: {'input_2': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'input_1': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'num_additional_inputs': '1', 'separator': '\\n'}
2025-07-01 09:39:51 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 16, corr_id 1fcc8e97-8957-45b8-9529-154d89b8d86a):
2025-07-01 09:39:51 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 1fcc8e97-8957-45b8-9529-154d89b8d86a, response: {'transition_id': 'transition-CombineTextComponent-***********63', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 16, 'workflow_status': 'running'}
2025-07-01 09:39:51 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: ce645c73-d890-49e0-95f1-6192f410c7e3) using provided producer.
2025-07-01 09:39:51 - NodeExecutor - DEBUG - Added correlation_id 1fcc8e97-8957-45b8-9529-154d89b8d86a to payload
2025-07-01 09:39:51 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-***********63 to payload
2025-07-01 09:39:51 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'input_2': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'input_1': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'num_additional_inputs': '1', 'separator': '\\n'}, 'request_id': 'ce645c73-d890-49e0-95f1-6192f410c7e3', 'correlation_id': '1fcc8e97-8957-45b8-9529-154d89b8d86a', 'transition_id': 'transition-CombineTextComponent-***********63'}
2025-07-01 09:39:51 - NodeExecutor - DEBUG - Request ce645c73-d890-49e0-95f1-6192f410c7e3 sent successfully using provided producer.
2025-07-01 09:39:51 - NodeExecutor - DEBUG - Waiting indefinitely for result for request ce645c73-d890-49e0-95f1-6192f410c7e3...
2025-07-01 09:39:51 - NodeExecutor - DEBUG - Result consumer received message: Offset=980
2025-07-01 09:39:51 - NodeExecutor - WARNING - Received error response for request_id ce645c73-d890-49e0-95f1-6192f410c7e3: Error combining text for request_id ce645c73-d890-49e0-95f1-6192f410c7e3: "Required field 'main_input' not found in parameters"
2025-07-01 09:39:51 - NodeExecutor - ERROR - Error during node execution ce645c73-d890-49e0-95f1-6192f410c7e3: Node execution failed: Error combining text for request_id ce645c73-d890-49e0-95f1-6192f410c7e3: "Required field 'main_input' not found in parameters"
Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id ce645c73-d890-49e0-95f1-6192f410c7e3: "Required field 'main_input' not found in parameters"
2025-07-01 09:39:51 - TransitionHandler - ERROR - Tool execution failed for tool 'CombineTextComponent' (tool_id: 1) in node 'CombineTextComponent' of transition 'transition-CombineTextComponent-***********63': Node execution failed: Error combining text for request_id ce645c73-d890-49e0-95f1-6192f410c7e3: "Required field 'main_input' not found in parameters"Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 492, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 361, in execute_tool
    raise e
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id ce645c73-d890-49e0-95f1-6192f410c7e3: "Required field 'main_input' not found in parameters"

2025-07-01 09:39:51 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 17, corr_id 1fcc8e97-8957-45b8-9529-154d89b8d86a):
2025-07-01 09:39:51 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 1fcc8e97-8957-45b8-9529-154d89b8d86a, response: {'transition_id': 'transition-CombineTextComponent-***********63', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition faced an error during execution.', 'result': '[ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id ce645c73-d890-49e0-95f1-6192f410c7e3: "Required field \'main_input\' not found in parameters"', 'status': 'failed', 'sequence': 17, 'workflow_status': 'running'}
2025-07-01 09:39:51 - TransitionHandler - ERROR - Exception in transition transition-CombineTextComponent-***********63: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id ce645c73-d890-49e0-95f1-6192f410c7e3: "Required field 'main_input' not found in parameters"
2025-07-01 09:39:51 - StateManager - DEBUG - Cleared iteration data for key current_iteration
2025-07-01 09:39:51 - LoopExecutor - ERROR - 🚨 Chain executor call failed with exception: LoopBodyChainExecutor._check_waiting_transitions_ready() takes 2 positional arguments but 4 were given
2025-07-01 09:39:51 - LoopExecutor - ERROR - 🚨 Full traceback: Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_executor.py", line 1890, in _execute_loop_body_chain
    result = await asyncio.wait_for(
             ^^^^^^^^^^^^^^^^^^^^^^^
    ...<8 lines>...
    )
    ^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/tasks.py", line 507, in wait_for
    return await fut
           ^^^^^^^^^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_body_chain_executor.py", line 100, in execute_loop_body_chain
    result = await self._execute_chain_with_isolated_state(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        isolated_state_manager, chain_id, entry_transitions, exit_transitions
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_body_chain_executor.py", line 273, in _execute_chain_with_isolated_state
    newly_ready = self._check_waiting_transitions_ready(
        waiting_transitions, completed_transitions, chain_id
    )
TypeError: LoopBodyChainExecutor._check_waiting_transitions_ready() takes 2 positional arguments but 4 were given

2025-07-01 09:39:51 - LoopExecutor - ERROR - ❌ Loop body chain execution failed for iteration 2: LoopBodyChainExecutor._check_waiting_transitions_ready() takes 2 positional arguments but 4 were given
2025-07-01 09:39:51 - LoopExecutor - ERROR - ❌ Iteration 3 failed: LoopBodyChainExecutor._check_waiting_transitions_ready() takes 2 positional arguments but 4 were given
2025-07-01 09:39:51 - LoopExecutor - INFO - 🔄 Starting iteration 4/6
2025-07-01 09:39:51 - LoopExecutor - DEBUG - 🚨 STEP 4: About to execute loop body chain for iteration 3
2025-07-01 09:39:51 - LoopExecutor - DEBUG - 🚨 About to call _execute_loop_body_chain with loop_body_chain_executor: <app.services.loop_executor.loop_body_chain_executor.LoopBodyChainExecutor object at 0x107224050>
2025-07-01 09:39:51 - LoopExecutor - INFO - 🚨 _execute_loop_body_chain called for iteration 3
2025-07-01 09:39:51 - LoopExecutor - DEBUG - 🔍 Current loop config keys: ['loop_type', 'aggregation_config', 'result_aggregation', 'iteration_source', 'exit_condition', 'concurrency', 'retry_config', 'iteration_behavior', 'iteration_settings', 'error_handling', 'loop_body_transitions', 'loop_body_configuration']
2025-07-01 09:39:51 - LoopExecutor - DEBUG - 🔍 Loop body config: {'entry_transitions': ['transition-CombineTextComponent-***********63'], 'exit_transitions': ['transition-CombineTextComponent-1750920624318'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}
2025-07-01 09:39:51 - LoopExecutor - DEBUG - 🔍 Loop body transitions: ['transition-CombineTextComponent-***********63']
2025-07-01 09:39:51 - LoopExecutor - DEBUG - 🚨 About to call loop_body_chain_executor.execute_loop_body_chain with transition_id: transition-LoopNode-*************
2025-07-01 09:39:51 - LoopExecutor - DEBUG - 🚨 Method exists: True
2025-07-01 09:39:51 - LoopExecutor - DEBUG - 🚨 Adding 10-second timeout to chain executor call
2025-07-01 09:39:51 - LoopExecutor - DEBUG - 🚨 About to call method directly...
2025-07-01 09:39:51 - StateManager - DEBUG - Using provided database connections
2025-07-01 09:39:51 - RedisEventListener - INFO - Workflow state manager reference updated
2025-07-01 09:39:51 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-07-01 09:39:51 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-07-01 09:39:52 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-07-01 09:39:52 - StateManager - INFO - WorkflowStateManager initialized
2025-07-01 09:39:52 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1750505490787: ['transition-CombineTextComponent-***********63']
2025-07-01 09:39:52 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1751001474606: ['transition-AgenticAI-1750505490787']
2025-07-01 09:39:52 - StateManager - DEBUG - 🔄 Detected potential loop body transition transition-CombineTextComponent-***********63 depending on loop node transition-LoopNode-*************
2025-07-01 09:39:52 - StateManager - DEBUG - 🔄 Detected loop body transition transition-CombineTextComponent-***********63 depending on loop node transition-LoopNode-*************
2025-07-01 09:39:52 - StateManager - DEBUG - 🔄 Loop body transition transition-CombineTextComponent-***********63 will wait for loop node transition-LoopNode-************* to provide iteration data
2025-07-01 09:39:52 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-***********63: ['transition-AgenticAI-*************', 'transition-AgenticAI-*************', 'transition-LoopNode-*************']
2025-07-01 09:39:52 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-1750920624318: ['transition-AgenticAI-1750505490787', 'transition-AgenticAI-1751001471087', 'transition-AgenticAI-1751001474606']
2025-07-01 09:39:52 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1751001471087: ['transition-AgenticAI-1750505490787']
2025-07-01 09:39:52 - StateManager - DEBUG - Stored iteration data for key current_iteration: {'current_item': 4, 'iteration_context': {'iteration_index': 3, 'total_iterations': 6, 'loop_config': {'loop_type': 'context_independent', 'aggregation_config': {'type': 'list'}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': False}, 'iteration_source': {'type': 'range', 'data': {'start': 1, 'stop': 7, 'step': 1}, 'batch_size': 1, 'original': {'number_range': {'start': 1, 'end': 6}, 'step': 1}}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'concurrency': {'enabled': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'retry_config': {'max_retries': 1, 'retry_delay': 1.0}, 'iteration_behavior': 'independent', 'iteration_settings': {'parallel_execution': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'error_handling': {'on_iteration_error': 'retry_once', 'include_errors': True}, 'loop_body_transitions': ['transition-CombineTextComponent-***********63'], 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-***********63'], 'exit_transitions': ['transition-CombineTextComponent-1750920624318'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}}}}
2025-07-01 09:39:52 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-***********63
2025-07-01 09:39:52 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 18, corr_id 1fcc8e97-8957-45b8-9529-154d89b8d86a):
2025-07-01 09:39:52 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 1fcc8e97-8957-45b8-9529-154d89b8d86a, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-***********63', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-***********63', 'status': 'started', 'sequence': 18, 'workflow_status': 'running'}
2025-07-01 09:39:52 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-***********63' (type=standard, execution_type=Components)
2025-07-01 09:39:52 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-07-01 09:39:52 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-***********63
2025-07-01 09:39:52 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-07-01 09:39:52 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-07-01 09:39:53 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-07-01 09:39:53 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-07-01 09:39:53 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-07-01 09:39:53 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-07-01 09:39:54 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-07-01 09:39:54 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-07-01 09:39:54 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-07-01 09:39:54 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-07-01 09:39:54 - StateManager - DEBUG - No result found in Redis for transition transition-LoopNode-*************. Trying PostgreSQL next.
2025-07-01 09:39:57 - PostgresManager - DEBUG - No result found for transition transition-LoopNode-************* in correlation 1fcc8e97-8957-45b8-9529-154d89b8d86a_loop_transition-LoopNode-*************_iteration_3
2025-07-01 09:39:57 - StateManager - DEBUG - No result found in PostgreSQL for transition transition-LoopNode-*************. Trying in-memory next.
2025-07-01 09:39:57 - StateManager - DEBUG - No result found in Redis, PostgreSQL, or memory for transition transition-LoopNode-*************
2025-07-01 09:39:57 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 1)
2025-07-01 09:39:57 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 2)
2025-07-01 09:39:57 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-07-01 09:39:57 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}
2025-07-01 09:39:57 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 09:39:57 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 09:39:57 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] (type: <class 'str'>)
2025-07-01 09:39:57 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 09:39:57 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 09:39:57 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}
2025-07-01 09:39:57 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 09:39:57 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 09:39:57 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] (type: <class 'str'>)
2025-07-01 09:39:57 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 09:39:57 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 09:39:57 - WorkflowUtils - INFO - 🔍 Handle mapping validation: partially_compatible (2/3 compatible)
2025-07-01 09:39:57 - TransitionHandler - INFO - 🔍 Handle validation: partially_compatible (2/3 compatible)
2025-07-01 09:39:57 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}
2025-07-01 09:39:57 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 09:39:57 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 09:39:57 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] (type: <class 'str'>)
2025-07-01 09:39:57 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 09:39:57 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 09:39:57 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → input_2 via path 'result': [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 09:39:57 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}
2025-07-01 09:39:57 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 09:39:57 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 09:39:57 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] (type: <class 'str'>)
2025-07-01 09:39:57 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 09:39:57 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 09:39:57 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → input_1 via path 'result': [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 09:39:57 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {}
2025-07-01 09:39:57 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: []
2025-07-01 09:39:57 - WorkflowUtils - DEBUG - Could not find result.result or result in source_results for handle current_item
2025-07-01 09:39:57 - WorkflowUtils - DEBUG - Path tracking - Source results structure
2025-07-01 09:39:57 - WorkflowUtils - WARNING - ❌ Handle mapping failed: current_item → main_input (no data found)
2025-07-01 09:39:57 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 2/3 successful
2025-07-01 09:39:57 - WorkflowUtils - WARNING - ⚠️ 1 universal handle mappings failed - this may cause tool execution errors
2025-07-01 09:39:57 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 2/3 successful
2025-07-01 09:39:57 - TransitionHandler - WARNING - ❌ Failed mapping: current_item → main_input (Error: No data found for handle current_item)
2025-07-01 09:39:57 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-07-01 09:39:57 - WorkflowUtils - DEBUG - Filtering out field 'input_1' with null/empty value: None
2025-07-01 09:39:57 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: None
2025-07-01 09:39:57 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-07-01 09:39:57 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-07-01 09:39:57 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-07-01 09:39:57 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-07-01 09:39:57 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-07-01 09:39:57 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-07-01 09:39:57 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-07-01 09:39:57 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-07-01 09:39:57 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 2 fields (11 null/empty fields removed)
2025-07-01 09:39:57 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-07-01 09:39:57 - TransitionHandler - DEBUG - 📌 Added static parameter: separator = \n
2025-07-01 09:39:57 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'input_2': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'input_1': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'num_additional_inputs': '1', 'separator': '\\n'}
2025-07-01 09:39:57 - TransitionHandler - DEBUG - tool Parameters: {'input_2': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'input_1': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'num_additional_inputs': '1', 'separator': '\\n'}
2025-07-01 09:39:57 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-***********63' with parameters: {'input_2': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'input_1': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'num_additional_inputs': '1', 'separator': '\\n'}
2025-07-01 09:39:57 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 19, corr_id 1fcc8e97-8957-45b8-9529-154d89b8d86a):
2025-07-01 09:39:57 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 1fcc8e97-8957-45b8-9529-154d89b8d86a, response: {'transition_id': 'transition-CombineTextComponent-***********63', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 19, 'workflow_status': 'running'}
2025-07-01 09:39:57 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: c1c24121-7e41-4bd8-8045-9c793c7dd020) using provided producer.
2025-07-01 09:39:57 - NodeExecutor - DEBUG - Added correlation_id 1fcc8e97-8957-45b8-9529-154d89b8d86a to payload
2025-07-01 09:39:57 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-***********63 to payload
2025-07-01 09:39:57 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'input_2': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'input_1': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'num_additional_inputs': '1', 'separator': '\\n'}, 'request_id': 'c1c24121-7e41-4bd8-8045-9c793c7dd020', 'correlation_id': '1fcc8e97-8957-45b8-9529-154d89b8d86a', 'transition_id': 'transition-CombineTextComponent-***********63'}
2025-07-01 09:39:57 - NodeExecutor - DEBUG - Request c1c24121-7e41-4bd8-8045-9c793c7dd020 sent successfully using provided producer.
2025-07-01 09:39:57 - NodeExecutor - DEBUG - Waiting indefinitely for result for request c1c24121-7e41-4bd8-8045-9c793c7dd020...
2025-07-01 09:39:58 - NodeExecutor - DEBUG - Result consumer received message: Offset=981
2025-07-01 09:39:58 - NodeExecutor - WARNING - Received error response for request_id c1c24121-7e41-4bd8-8045-9c793c7dd020: Error combining text for request_id c1c24121-7e41-4bd8-8045-9c793c7dd020: "Required field 'main_input' not found in parameters"
2025-07-01 09:39:58 - NodeExecutor - ERROR - Error during node execution c1c24121-7e41-4bd8-8045-9c793c7dd020: Node execution failed: Error combining text for request_id c1c24121-7e41-4bd8-8045-9c793c7dd020: "Required field 'main_input' not found in parameters"
Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id c1c24121-7e41-4bd8-8045-9c793c7dd020: "Required field 'main_input' not found in parameters"
2025-07-01 09:39:58 - TransitionHandler - ERROR - Tool execution failed for tool 'CombineTextComponent' (tool_id: 1) in node 'CombineTextComponent' of transition 'transition-CombineTextComponent-***********63': Node execution failed: Error combining text for request_id c1c24121-7e41-4bd8-8045-9c793c7dd020: "Required field 'main_input' not found in parameters"Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 492, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 361, in execute_tool
    raise e
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id c1c24121-7e41-4bd8-8045-9c793c7dd020: "Required field 'main_input' not found in parameters"

2025-07-01 09:39:58 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 20, corr_id 1fcc8e97-8957-45b8-9529-154d89b8d86a):
2025-07-01 09:39:58 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 1fcc8e97-8957-45b8-9529-154d89b8d86a, response: {'transition_id': 'transition-CombineTextComponent-***********63', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition faced an error during execution.', 'result': '[ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id c1c24121-7e41-4bd8-8045-9c793c7dd020: "Required field \'main_input\' not found in parameters"', 'status': 'failed', 'sequence': 20, 'workflow_status': 'running'}
2025-07-01 09:39:58 - TransitionHandler - ERROR - Exception in transition transition-CombineTextComponent-***********63: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id c1c24121-7e41-4bd8-8045-9c793c7dd020: "Required field 'main_input' not found in parameters"
2025-07-01 09:39:58 - StateManager - DEBUG - Cleared iteration data for key current_iteration
2025-07-01 09:39:58 - LoopExecutor - ERROR - 🚨 Chain executor call failed with exception: LoopBodyChainExecutor._check_waiting_transitions_ready() takes 2 positional arguments but 4 were given
2025-07-01 09:39:58 - LoopExecutor - ERROR - 🚨 Full traceback: Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_executor.py", line 1890, in _execute_loop_body_chain
    result = await asyncio.wait_for(
             ^^^^^^^^^^^^^^^^^^^^^^^
    ...<8 lines>...
    )
    ^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/tasks.py", line 507, in wait_for
    return await fut
           ^^^^^^^^^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_body_chain_executor.py", line 100, in execute_loop_body_chain
    result = await self._execute_chain_with_isolated_state(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        isolated_state_manager, chain_id, entry_transitions, exit_transitions
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_body_chain_executor.py", line 273, in _execute_chain_with_isolated_state
    newly_ready = self._check_waiting_transitions_ready(
        waiting_transitions, completed_transitions, chain_id
    )
TypeError: LoopBodyChainExecutor._check_waiting_transitions_ready() takes 2 positional arguments but 4 were given

2025-07-01 09:39:58 - LoopExecutor - ERROR - ❌ Loop body chain execution failed for iteration 3: LoopBodyChainExecutor._check_waiting_transitions_ready() takes 2 positional arguments but 4 were given
2025-07-01 09:39:58 - LoopExecutor - ERROR - ❌ Iteration 4 failed: LoopBodyChainExecutor._check_waiting_transitions_ready() takes 2 positional arguments but 4 were given
2025-07-01 09:39:58 - LoopExecutor - INFO - 🔄 Starting iteration 5/6
2025-07-01 09:39:58 - LoopExecutor - DEBUG - 🚨 STEP 4: About to execute loop body chain for iteration 4
2025-07-01 09:39:58 - LoopExecutor - DEBUG - 🚨 About to call _execute_loop_body_chain with loop_body_chain_executor: <app.services.loop_executor.loop_body_chain_executor.LoopBodyChainExecutor object at 0x107224050>
2025-07-01 09:39:58 - LoopExecutor - INFO - 🚨 _execute_loop_body_chain called for iteration 4
2025-07-01 09:39:58 - LoopExecutor - DEBUG - 🔍 Current loop config keys: ['loop_type', 'aggregation_config', 'result_aggregation', 'iteration_source', 'exit_condition', 'concurrency', 'retry_config', 'iteration_behavior', 'iteration_settings', 'error_handling', 'loop_body_transitions', 'loop_body_configuration']
2025-07-01 09:39:58 - LoopExecutor - DEBUG - 🔍 Loop body config: {'entry_transitions': ['transition-CombineTextComponent-***********63'], 'exit_transitions': ['transition-CombineTextComponent-1750920624318'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}
2025-07-01 09:39:58 - LoopExecutor - DEBUG - 🔍 Loop body transitions: ['transition-CombineTextComponent-***********63']
2025-07-01 09:39:58 - LoopExecutor - DEBUG - 🚨 About to call loop_body_chain_executor.execute_loop_body_chain with transition_id: transition-LoopNode-*************
2025-07-01 09:39:58 - LoopExecutor - DEBUG - 🚨 Method exists: True
2025-07-01 09:39:58 - LoopExecutor - DEBUG - 🚨 Adding 10-second timeout to chain executor call
2025-07-01 09:39:58 - LoopExecutor - DEBUG - 🚨 About to call method directly...
2025-07-01 09:39:58 - StateManager - DEBUG - Using provided database connections
2025-07-01 09:39:58 - RedisEventListener - INFO - Workflow state manager reference updated
2025-07-01 09:39:58 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-07-01 09:39:58 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-07-01 09:39:59 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-07-01 09:39:59 - StateManager - INFO - WorkflowStateManager initialized
2025-07-01 09:39:59 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1750505490787: ['transition-CombineTextComponent-***********63']
2025-07-01 09:39:59 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1751001474606: ['transition-AgenticAI-1750505490787']
2025-07-01 09:39:59 - StateManager - DEBUG - 🔄 Detected potential loop body transition transition-CombineTextComponent-***********63 depending on loop node transition-LoopNode-*************
2025-07-01 09:39:59 - StateManager - DEBUG - 🔄 Detected loop body transition transition-CombineTextComponent-***********63 depending on loop node transition-LoopNode-*************
2025-07-01 09:39:59 - StateManager - DEBUG - 🔄 Loop body transition transition-CombineTextComponent-***********63 will wait for loop node transition-LoopNode-************* to provide iteration data
2025-07-01 09:39:59 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-***********63: ['transition-AgenticAI-*************', 'transition-AgenticAI-*************', 'transition-LoopNode-*************']
2025-07-01 09:39:59 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-1750920624318: ['transition-AgenticAI-1750505490787', 'transition-AgenticAI-1751001471087', 'transition-AgenticAI-1751001474606']
2025-07-01 09:39:59 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1751001471087: ['transition-AgenticAI-1750505490787']
2025-07-01 09:39:59 - StateManager - DEBUG - Stored iteration data for key current_iteration: {'current_item': 5, 'iteration_context': {'iteration_index': 4, 'total_iterations': 6, 'loop_config': {'loop_type': 'context_independent', 'aggregation_config': {'type': 'list'}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': False}, 'iteration_source': {'type': 'range', 'data': {'start': 1, 'stop': 7, 'step': 1}, 'batch_size': 1, 'original': {'number_range': {'start': 1, 'end': 6}, 'step': 1}}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'concurrency': {'enabled': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'retry_config': {'max_retries': 1, 'retry_delay': 1.0}, 'iteration_behavior': 'independent', 'iteration_settings': {'parallel_execution': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'error_handling': {'on_iteration_error': 'retry_once', 'include_errors': True}, 'loop_body_transitions': ['transition-CombineTextComponent-***********63'], 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-***********63'], 'exit_transitions': ['transition-CombineTextComponent-1750920624318'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}}}}
2025-07-01 09:39:59 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-***********63
2025-07-01 09:39:59 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 21, corr_id 1fcc8e97-8957-45b8-9529-154d89b8d86a):
2025-07-01 09:39:59 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 1fcc8e97-8957-45b8-9529-154d89b8d86a, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-***********63', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-***********63', 'status': 'started', 'sequence': 21, 'workflow_status': 'running'}
2025-07-01 09:39:59 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-***********63' (type=standard, execution_type=Components)
2025-07-01 09:39:59 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-07-01 09:39:59 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-***********63
2025-07-01 09:39:59 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-07-01 09:39:59 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-07-01 09:40:00 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-07-01 09:40:00 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-07-01 09:40:00 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-07-01 09:40:00 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-07-01 09:40:00 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-07-01 09:40:00 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-07-01 09:40:00 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-07-01 09:40:00 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-07-01 09:40:01 - StateManager - DEBUG - No result found in Redis for transition transition-LoopNode-*************. Trying PostgreSQL next.
2025-07-01 09:40:04 - PostgresManager - DEBUG - No result found for transition transition-LoopNode-************* in correlation 1fcc8e97-8957-45b8-9529-154d89b8d86a_loop_transition-LoopNode-*************_iteration_4
2025-07-01 09:40:04 - StateManager - DEBUG - No result found in PostgreSQL for transition transition-LoopNode-*************. Trying in-memory next.
2025-07-01 09:40:04 - StateManager - DEBUG - No result found in Redis, PostgreSQL, or memory for transition transition-LoopNode-*************
2025-07-01 09:40:04 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 1)
2025-07-01 09:40:04 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-AgenticAI-************* (total: 2)
2025-07-01 09:40:04 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-07-01 09:40:04 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}
2025-07-01 09:40:04 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 09:40:04 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 09:40:04 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] (type: <class 'str'>)
2025-07-01 09:40:04 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 09:40:04 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 09:40:04 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}
2025-07-01 09:40:04 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 09:40:04 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 09:40:04 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] (type: <class 'str'>)
2025-07-01 09:40:04 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 09:40:04 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 09:40:04 - WorkflowUtils - INFO - 🔍 Handle mapping validation: partially_compatible (2/3 compatible)
2025-07-01 09:40:04 - TransitionHandler - INFO - 🔍 Handle validation: partially_compatible (2/3 compatible)
2025-07-01 09:40:04 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}
2025-07-01 09:40:04 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 09:40:04 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 09:40:04 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] (type: <class 'str'>)
2025-07-01 09:40:04 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 09:40:04 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 09:40:04 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → input_2 via path 'result': [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 09:40:04 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_answer': {'result': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]'}
2025-07-01 09:40:04 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-01 09:40:04 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle final_answer: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 09:40:04 - WorkflowUtils - DEBUG - Found result.result: [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ] (type: <class 'str'>)
2025-07-01 09:40:04 - WorkflowUtils - DEBUG - final_result is primitive type <class 'str'>, no handle extraction possible
2025-07-01 09:40:04 - WorkflowUtils - DEBUG - No handle matches found for 'final_answer', treating result as single-value
2025-07-01 09:40:04 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_answer → input_1 via path 'result': [ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\n\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn't that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\n\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\n\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\n\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\n\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]
2025-07-01 09:40:04 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {}
2025-07-01 09:40:04 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: []
2025-07-01 09:40:04 - WorkflowUtils - DEBUG - Could not find result.result or result in source_results for handle current_item
2025-07-01 09:40:04 - WorkflowUtils - DEBUG - Path tracking - Source results structure
2025-07-01 09:40:04 - WorkflowUtils - WARNING - ❌ Handle mapping failed: current_item → main_input (no data found)
2025-07-01 09:40:04 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 2/3 successful
2025-07-01 09:40:04 - WorkflowUtils - WARNING - ⚠️ 1 universal handle mappings failed - this may cause tool execution errors
2025-07-01 09:40:04 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 2/3 successful
2025-07-01 09:40:04 - TransitionHandler - WARNING - ❌ Failed mapping: current_item → main_input (Error: No data found for handle current_item)
2025-07-01 09:40:04 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-07-01 09:40:04 - WorkflowUtils - DEBUG - Filtering out field 'input_1' with null/empty value: None
2025-07-01 09:40:04 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: None
2025-07-01 09:40:04 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-07-01 09:40:04 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-07-01 09:40:04 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-07-01 09:40:04 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-07-01 09:40:04 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-07-01 09:40:04 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-07-01 09:40:04 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-07-01 09:40:04 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-07-01 09:40:04 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 2 fields (11 null/empty fields removed)
2025-07-01 09:40:04 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-07-01 09:40:04 - TransitionHandler - DEBUG - 📌 Added static parameter: separator = \n
2025-07-01 09:40:04 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'input_2': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'input_1': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'num_additional_inputs': '1', 'separator': '\\n'}
2025-07-01 09:40:04 - TransitionHandler - DEBUG - tool Parameters: {'input_2': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'input_1': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'num_additional_inputs': '1', 'separator': '\\n'}
2025-07-01 09:40:04 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-***********63' with parameters: {'input_2': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'input_1': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'num_additional_inputs': '1', 'separator': '\\n'}
2025-07-01 09:40:04 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 22, corr_id 1fcc8e97-8957-45b8-9529-154d89b8d86a):
2025-07-01 09:40:04 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 1fcc8e97-8957-45b8-9529-154d89b8d86a, response: {'transition_id': 'transition-CombineTextComponent-***********63', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Connecting to server', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 22, 'workflow_status': 'running'}
2025-07-01 09:40:04 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 70dd81c2-d795-4bce-9348-41001b2e740f) using provided producer.
2025-07-01 09:40:04 - NodeExecutor - DEBUG - Added correlation_id 1fcc8e97-8957-45b8-9529-154d89b8d86a to payload
2025-07-01 09:40:04 - NodeExecutor - DEBUG - Added transition_id transition-CombineTextComponent-***********63 to payload
2025-07-01 09:40:04 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'input_2': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'input_1': '[ { "part": "(Upbeat music plays as the sleek iPhone glides effortlessly through the air.)" }, { "part": "Narrator: "Whoa... impressive features of the latest iPhone, revealed now! Ready to see what makes it a game-changer?"" }, { "part": "(Close-ups of the iPhone’s minimalist and advanced design.)\\n\\nNarrator: "Check this out – the latest iPhone redefines what a smartphone can do! Isn\'t that wild?"" }, { "part": "(Diverse people from various backgrounds appear, enjoying different life moments seamlessly integrated with their iPhone.)\\n\\nNarrator: "Start your day... like, super smoothly. The new chip launches apps faster than ever—whether you’re keeping up with emails or sharing those killer moments!"" }, { "part": "(Smart transitions to a professional enjoying a morning coffee, while checking emails with a simple glance.)\\n\\nNarrator: "Say goodbye to those pesky low-light woes! With an advanced Night mode and camera system... your on-the-go photos are just... wow."" }, { "part": "(Quick showcase of breathtaking nighttime cityscape photos and detailed family gatherings.)\\n\\nNarrator: "Connect effortlessly... security features and messaging got your back. Real-time transcriptions in conversations keep you in sync with everyone, everywhere. How neat is that?"\\n\\n(View of simultaneous calls transitioning seamlessly to texts and back.)" } ]', 'num_additional_inputs': '1', 'separator': '\\n'}, 'request_id': '70dd81c2-d795-4bce-9348-41001b2e740f', 'correlation_id': '1fcc8e97-8957-45b8-9529-154d89b8d86a', 'transition_id': 'transition-CombineTextComponent-***********63'}
2025-07-01 09:40:04 - NodeExecutor - DEBUG - Request 70dd81c2-d795-4bce-9348-41001b2e740f sent successfully using provided producer.
2025-07-01 09:40:04 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 70dd81c2-d795-4bce-9348-41001b2e740f...
2025-07-01 09:40:06 - NodeExecutor - DEBUG - Result consumer received message: Offset=982
2025-07-01 09:40:06 - NodeExecutor - WARNING - Received error response for request_id 70dd81c2-d795-4bce-9348-41001b2e740f: Error combining text for request_id 70dd81c2-d795-4bce-9348-41001b2e740f: "Required field 'main_input' not found in parameters"
2025-07-01 09:40:06 - NodeExecutor - ERROR - Error during node execution 70dd81c2-d795-4bce-9348-41001b2e740f: Node execution failed: Error combining text for request_id 70dd81c2-d795-4bce-9348-41001b2e740f: "Required field 'main_input' not found in parameters"
Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id 70dd81c2-d795-4bce-9348-41001b2e740f: "Required field 'main_input' not found in parameters"
2025-07-01 09:40:06 - TransitionHandler - ERROR - Tool execution failed for tool 'CombineTextComponent' (tool_id: 1) in node 'CombineTextComponent' of transition 'transition-CombineTextComponent-***********63': Node execution failed: Error combining text for request_id 70dd81c2-d795-4bce-9348-41001b2e740f: "Required field 'main_input' not found in parameters"Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 492, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 361, in execute_tool
    raise e
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 344, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Error combining text for request_id 70dd81c2-d795-4bce-9348-41001b2e740f: "Required field 'main_input' not found in parameters"

2025-07-01 09:40:06 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 23, corr_id 1fcc8e97-8957-45b8-9529-154d89b8d86a):
2025-07-01 09:40:06 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 1fcc8e97-8957-45b8-9529-154d89b8d86a, response: {'transition_id': 'transition-CombineTextComponent-***********63', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'message': 'Transition faced an error during execution.', 'result': '[ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 70dd81c2-d795-4bce-9348-41001b2e740f: "Required field \'main_input\' not found in parameters"', 'status': 'failed', 'sequence': 23, 'workflow_status': 'running'}
2025-07-01 09:40:06 - TransitionHandler - ERROR - Exception in transition transition-CombineTextComponent-***********63: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Error combining text for request_id 70dd81c2-d795-4bce-9348-41001b2e740f: "Required field 'main_input' not found in parameters"
2025-07-01 09:40:06 - StateManager - DEBUG - Cleared iteration data for key current_iteration
2025-07-01 09:40:06 - LoopExecutor - ERROR - 🚨 Chain executor call failed with exception: LoopBodyChainExecutor._check_waiting_transitions_ready() takes 2 positional arguments but 4 were given
2025-07-01 09:40:06 - LoopExecutor - ERROR - 🚨 Full traceback: Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_executor.py", line 1890, in _execute_loop_body_chain
    result = await asyncio.wait_for(
             ^^^^^^^^^^^^^^^^^^^^^^^
    ...<8 lines>...
    )
    ^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/tasks.py", line 507, in wait_for
    return await fut
           ^^^^^^^^^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_body_chain_executor.py", line 100, in execute_loop_body_chain
    result = await self._execute_chain_with_isolated_state(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        isolated_state_manager, chain_id, entry_transitions, exit_transitions
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_body_chain_executor.py", line 273, in _execute_chain_with_isolated_state
    newly_ready = self._check_waiting_transitions_ready(
        waiting_transitions, completed_transitions, chain_id
    )
TypeError: LoopBodyChainExecutor._check_waiting_transitions_ready() takes 2 positional arguments but 4 were given

2025-07-01 09:40:06 - LoopExecutor - ERROR - ❌ Loop body chain execution failed for iteration 4: LoopBodyChainExecutor._check_waiting_transitions_ready() takes 2 positional arguments but 4 were given
2025-07-01 09:40:06 - LoopExecutor - ERROR - ❌ Iteration 5 failed: LoopBodyChainExecutor._check_waiting_transitions_ready() takes 2 positional arguments but 4 were given
2025-07-01 09:40:06 - LoopExecutor - INFO - 🔄 Starting iteration 6/6
2025-07-01 09:40:06 - LoopExecutor - DEBUG - 🚨 STEP 4: About to execute loop body chain for iteration 5
2025-07-01 09:40:06 - LoopExecutor - DEBUG - 🚨 About to call _execute_loop_body_chain with loop_body_chain_executor: <app.services.loop_executor.loop_body_chain_executor.LoopBodyChainExecutor object at 0x107224050>
2025-07-01 09:40:06 - LoopExecutor - INFO - 🚨 _execute_loop_body_chain called for iteration 5
2025-07-01 09:40:06 - LoopExecutor - DEBUG - 🔍 Current loop config keys: ['loop_type', 'aggregation_config', 'result_aggregation', 'iteration_source', 'exit_condition', 'concurrency', 'retry_config', 'iteration_behavior', 'iteration_settings', 'error_handling', 'loop_body_transitions', 'loop_body_configuration']
2025-07-01 09:40:06 - LoopExecutor - DEBUG - 🔍 Loop body config: {'entry_transitions': ['transition-CombineTextComponent-***********63'], 'exit_transitions': ['transition-CombineTextComponent-1750920624318'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}
2025-07-01 09:40:06 - LoopExecutor - DEBUG - 🔍 Loop body transitions: ['transition-CombineTextComponent-***********63']
2025-07-01 09:40:06 - LoopExecutor - DEBUG - 🚨 About to call loop_body_chain_executor.execute_loop_body_chain with transition_id: transition-LoopNode-*************
2025-07-01 09:40:06 - LoopExecutor - DEBUG - 🚨 Method exists: True
2025-07-01 09:40:06 - LoopExecutor - DEBUG - 🚨 Adding 10-second timeout to chain executor call
2025-07-01 09:40:06 - LoopExecutor - DEBUG - 🚨 About to call method directly...
2025-07-01 09:40:06 - StateManager - DEBUG - Using provided database connections
2025-07-01 09:40:06 - RedisEventListener - INFO - Workflow state manager reference updated
2025-07-01 09:40:06 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-07-01 09:40:06 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-07-01 09:40:07 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-07-01 09:40:07 - StateManager - INFO - WorkflowStateManager initialized
2025-07-01 09:40:07 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1750505490787: ['transition-CombineTextComponent-***********63']
2025-07-01 09:40:07 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1751001474606: ['transition-AgenticAI-1750505490787']
2025-07-01 09:40:07 - StateManager - DEBUG - 🔄 Detected potential loop body transition transition-CombineTextComponent-***********63 depending on loop node transition-LoopNode-*************
2025-07-01 09:40:07 - StateManager - DEBUG - 🔄 Detected loop body transition transition-CombineTextComponent-***********63 depending on loop node transition-LoopNode-*************
2025-07-01 09:40:07 - StateManager - DEBUG - 🔄 Loop body transition transition-CombineTextComponent-***********63 will wait for loop node transition-LoopNode-************* to provide iteration data
2025-07-01 09:40:07 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-***********63: ['transition-AgenticAI-*************', 'transition-AgenticAI-*************', 'transition-LoopNode-*************']
2025-07-01 09:40:07 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-1750920624318: ['transition-AgenticAI-1750505490787', 'transition-AgenticAI-1751001471087', 'transition-AgenticAI-1751001474606']
2025-07-01 09:40:07 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-1751001471087: ['transition-AgenticAI-1750505490787']
2025-07-01 09:40:07 - StateManager - DEBUG - Stored iteration data for key current_iteration: {'current_item': 6, 'iteration_context': {'iteration_index': 5, 'total_iterations': 6, 'loop_config': {'loop_type': 'context_independent', 'aggregation_config': {'type': 'list'}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': False}, 'iteration_source': {'type': 'range', 'data': {'start': 1, 'stop': 7, 'step': 1}, 'batch_size': 1, 'original': {'number_range': {'start': 1, 'end': 6}, 'step': 1}}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'concurrency': {'enabled': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'retry_config': {'max_retries': 1, 'retry_delay': 1.0}, 'iteration_behavior': 'independent', 'iteration_settings': {'parallel_execution': False, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'error_handling': {'on_iteration_error': 'retry_once', 'include_errors': True}, 'loop_body_transitions': ['transition-CombineTextComponent-***********63'], 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-***********63'], 'exit_transitions': ['transition-CombineTextComponent-1750920624318'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}}}}
2025-07-01 09:40:07 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-***********63
2025-07-01 09:40:07 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 24, corr_id 1fcc8e97-8957-45b8-9529-154d89b8d86a):
2025-07-01 09:40:07 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 1fcc8e97-8957-45b8-9529-154d89b8d86a, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-***********63', 'message': 'Starting execution...', 'transition_id': 'transition-CombineTextComponent-***********63', 'status': 'started', 'sequence': 24, 'workflow_status': 'running'}
2025-07-01 09:40:07 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-***********63' (type=standard, execution_type=Components)
2025-07-01 09:40:07 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-07-01 09:40:07 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-***********63
2025-07-01 09:40:07 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-07-01 09:40:07 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-07-01 09:40:08 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-07-01 09:40:08 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-07-01 09:40:08 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-07-01 09:40:08 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-07-01 09:40:09 - StateManager - DEBUG - Retrieved result for transition transition-AgenticAI-************* from Redis
2025-07-01 09:40:09 - StateManager - DEBUG - Detected wrapped result structure for transition transition-AgenticAI-*************, extracting data
2025-07-01 09:40:09 - StateManager - DEBUG - Extracted double-nested result data for transition transition-AgenticAI-*************
2025-07-01 09:40:09 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-AgenticAI-*************
2025-07-01 09:40:10 - StateManager - DEBUG - No result found in Redis for transition transition-LoopNode-*************. Trying PostgreSQL next.
2025-07-01 09:40:12 - StateManager - DEBUG - Cleared iteration data for key current_iteration
2025-07-01 09:40:12 - TransitionHandler - ERROR - Tool execution failed for tool 'LoopNode' (tool_id: 1) in node 'LoopNode' of transition 'transition-LoopNode-*************': 'LoopBodyChainExecutor' object has no attribute 'active_chains'Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_executor.py", line 173, in execute_tool
    loop_results = await self._execute_loop_workflow(loop_state)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_executor.py", line 3109, in _execute_loop_workflow
    iteration_result = await self._execute_loop_body_chain(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        iteration_index, iteration_item, iteration_context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_executor.py", line 1890, in _execute_loop_body_chain
    result = await asyncio.wait_for(
             ^^^^^^^^^^^^^^^^^^^^^^^
    ...<8 lines>...
    )
    ^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/tasks.py", line 507, in wait_for
    return await fut
           ^^^^^^^^^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_body_chain_executor.py", line 100, in execute_loop_body_chain
    result = await self._execute_chain_with_isolated_state(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        isolated_state_manager, chain_id, entry_transitions, exit_transitions
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_body_chain_executor.py", line 292, in _execute_chain_with_isolated_state
    result = await self._execute_transition_directly(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        isolated_state_manager, transition_id
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_body_chain_executor.py", line 378, in _execute_transition_directly
    result = await self.transition_handler._execute_transition_with_tracking(transition)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 204, in _execute_transition_with_tracking
    result = await self._execute_standard_or_reflection_transition(transition)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 331, in _execute_standard_or_reflection_transition
    tool_parameters = await self._resolve_tool_parameters_universally(
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 1210, in _resolve_tool_parameters_universally
    return await self._resolve_with_result_resolution(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 1273, in _resolve_with_result_resolution
    result_from_dependency = self.state_manager.get_transition_result(
        from_transition_id
    )
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/state_manager.py", line 202, in get_transition_result
    and self.postgres_manager.is_connected()
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/db_connections/postgres_connections.py", line 122, in is_connected
    self.pool.putconn(conn)
    ~~~~~~~~~~~~~~~~~^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/orchestration-engine-tNJVEdE_-py3.13/lib/python3.13/site-packages/psycopg2/pool.py", line 177, in putconn
    self._putconn(conn, key, close)
    ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/orchestration-engine-tNJVEdE_-py3.13/lib/python3.13/site-packages/psycopg2/pool.py", line 115, in _putconn
    conn.rollback()
    ~~~~~~~~~~~~~^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/runners.py", line 157, in _on_sigint
    raise KeyboardInterrupt()
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 445, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_executor.py", line 192, in execute_tool
    await self.cleanup_loop_resources()
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_executor.py", line 2014, in cleanup_loop_resources
    stalled_chains = await self.loop_body_chain_executor.cleanup_stalled_chains()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_body_chain_executor.py", line 1215, in cleanup_stalled_chains
    for chain_id, chain_state in list(self.active_chains.items()):
                                      ^^^^^^^^^^^^^^^^^^
AttributeError: 'LoopBodyChainExecutor' object has no attribute 'active_chains'

2025-07-01 09:40:12 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 25, corr_id 1fcc8e97-8957-45b8-9529-154d89b8d86a):
2025-07-01 09:40:12 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 1fcc8e97-8957-45b8-9529-154d89b8d86a, response: {'transition_id': 'transition-LoopNode-*************', 'node_id': 'LoopNode', 'tool_name': 'LoopNode', 'message': 'Transition faced an error during execution.', 'result': "[ERROR] Tool Execution Failed with error: 'LoopBodyChainExecutor' object has no attribute 'active_chains'", 'status': 'failed', 'sequence': 25, 'workflow_status': 'running'}
2025-07-01 09:40:12 - TransitionHandler - ERROR - Exception in transition transition-LoopNode-*************: Tool execution error: [ERROR] Tool Execution Failed with error: 'LoopBodyChainExecutor' object has no attribute 'active_chains'
2025-07-01 09:40:12 - KafkaWorkflowConsumer - INFO - Consumer task cancelled.
2025-07-01 09:40:12 - EnhancedWorkflowEngine - DEBUG - Results: [Exception("Exception in transition transition-LoopNode-*************: Tool execution error: [ERROR] Tool Execution Failed with error: 'LoopBodyChainExecutor' object has no attribute 'active_chains'")]
2025-07-01 09:40:12 - EnhancedWorkflowEngine - DEBUG - 🔄 Orchestration engine received result for transition-LoopNode-*************: Exception in transition transition-LoopNode-*************: Tool execution error: [ERROR] Tool Execution Failed with error: 'LoopBodyChainExecutor' object has no attribute 'active_chains'
2025-07-01 09:40:12 - EnhancedWorkflowEngine - DEBUG - 🔄 Result type: <class 'Exception'>, is_list: False
2025-07-01 09:40:12 - EnhancedWorkflowEngine - ERROR - Error in execution of transition transition-LoopNode-*************: Exception in transition transition-LoopNode-*************: Tool execution error: [ERROR] Tool Execution Failed with error: 'LoopBodyChainExecutor' object has no attribute 'active_chains'
2025-07-01 09:40:12 - EnhancedWorkflowEngine - ERROR - Traceback for transition transition-LoopNode-*************: NoneType: None

2025-07-01 09:40:12 - EnhancedWorkflowEngine - ERROR - An unexpected error occurred during parallel execution of standard transitions: Exception in transition transition-LoopNode-*************: Tool execution error: [ERROR] Tool Execution Failed with error: 'LoopBodyChainExecutor' object has no attribute 'active_chains'
2025-07-01 09:40:12 - EnhancedWorkflowEngine - ERROR - Traceback for unexpected error: Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_executor.py", line 173, in execute_tool
    loop_results = await self._execute_loop_workflow(loop_state)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_executor.py", line 3109, in _execute_loop_workflow
    iteration_result = await self._execute_loop_body_chain(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        iteration_index, iteration_item, iteration_context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_executor.py", line 1890, in _execute_loop_body_chain
    result = await asyncio.wait_for(
             ^^^^^^^^^^^^^^^^^^^^^^^
    ...<8 lines>...
    )
    ^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/tasks.py", line 507, in wait_for
    return await fut
           ^^^^^^^^^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_body_chain_executor.py", line 100, in execute_loop_body_chain
    result = await self._execute_chain_with_isolated_state(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        isolated_state_manager, chain_id, entry_transitions, exit_transitions
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_body_chain_executor.py", line 292, in _execute_chain_with_isolated_state
    result = await self._execute_transition_directly(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        isolated_state_manager, transition_id
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_body_chain_executor.py", line 378, in _execute_transition_directly
    result = await self.transition_handler._execute_transition_with_tracking(transition)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 204, in _execute_transition_with_tracking
    result = await self._execute_standard_or_reflection_transition(transition)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 331, in _execute_standard_or_reflection_transition
    tool_parameters = await self._resolve_tool_parameters_universally(
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 1210, in _resolve_tool_parameters_universally
    return await self._resolve_with_result_resolution(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 1273, in _resolve_with_result_resolution
    result_from_dependency = self.state_manager.get_transition_result(
        from_transition_id
    )
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/state_manager.py", line 202, in get_transition_result
    and self.postgres_manager.is_connected()
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/db_connections/postgres_connections.py", line 122, in is_connected
    self.pool.putconn(conn)
    ~~~~~~~~~~~~~~~~~^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/orchestration-engine-tNJVEdE_-py3.13/lib/python3.13/site-packages/psycopg2/pool.py", line 177, in putconn
    self._putconn(conn, key, close)
    ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/orchestration-engine-tNJVEdE_-py3.13/lib/python3.13/site-packages/psycopg2/pool.py", line 115, in _putconn
    conn.rollback()
    ~~~~~~~~~~~~~^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/runners.py", line 157, in _on_sigint
    raise KeyboardInterrupt()
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 445, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_executor.py", line 192, in execute_tool
    await self.cleanup_loop_resources()
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_executor.py", line 2014, in cleanup_loop_resources
    stalled_chains = await self.loop_body_chain_executor.cleanup_stalled_chains()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_body_chain_executor.py", line 1215, in cleanup_stalled_chains
    for chain_id, chain_state in list(self.active_chains.items()):
                                      ^^^^^^^^^^^^^^^^^^
AttributeError: 'LoopBodyChainExecutor' object has no attribute 'active_chains'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 204, in _execute_transition_with_tracking
    result = await self._execute_standard_or_reflection_transition(transition)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 703, in _execute_standard_or_reflection_transition
    raise Exception(f"Tool execution error: {error_message}")
Exception: Tool execution error: [ERROR] Tool Execution Failed with error: 'LoopBodyChainExecutor' object has no attribute 'active_chains'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/executor_core.py", line 267, in execute
    raise result
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 230, in _execute_transition_with_tracking
    raise Exception(f"Exception in transition {transition_id}: {str(e)}")
Exception: Exception in transition transition-LoopNode-*************: Tool execution error: [ERROR] Tool Execution Failed with error: 'LoopBodyChainExecutor' object has no attribute 'active_chains'

2025-07-01 09:40:12 - EnhancedWorkflowEngine - ERROR - An unexpected error occurred during workflow execution: Exception in transition transition-LoopNode-*************: Tool execution error: [ERROR] Tool Execution Failed with error: 'LoopBodyChainExecutor' object has no attribute 'active_chains'
2025-07-01 09:40:12 - EnhancedWorkflowEngine - ERROR - Traceback for unexpected error: Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_executor.py", line 173, in execute_tool
    loop_results = await self._execute_loop_workflow(loop_state)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_executor.py", line 3109, in _execute_loop_workflow
    iteration_result = await self._execute_loop_body_chain(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        iteration_index, iteration_item, iteration_context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_executor.py", line 1890, in _execute_loop_body_chain
    result = await asyncio.wait_for(
             ^^^^^^^^^^^^^^^^^^^^^^^
    ...<8 lines>...
    )
    ^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/tasks.py", line 507, in wait_for
    return await fut
           ^^^^^^^^^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_body_chain_executor.py", line 100, in execute_loop_body_chain
    result = await self._execute_chain_with_isolated_state(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        isolated_state_manager, chain_id, entry_transitions, exit_transitions
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_body_chain_executor.py", line 292, in _execute_chain_with_isolated_state
    result = await self._execute_transition_directly(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        isolated_state_manager, transition_id
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_body_chain_executor.py", line 378, in _execute_transition_directly
    result = await self.transition_handler._execute_transition_with_tracking(transition)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 204, in _execute_transition_with_tracking
    result = await self._execute_standard_or_reflection_transition(transition)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 331, in _execute_standard_or_reflection_transition
    tool_parameters = await self._resolve_tool_parameters_universally(
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 1210, in _resolve_tool_parameters_universally
    return await self._resolve_with_result_resolution(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 1273, in _resolve_with_result_resolution
    result_from_dependency = self.state_manager.get_transition_result(
        from_transition_id
    )
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/state_manager.py", line 202, in get_transition_result
    and self.postgres_manager.is_connected()
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/db_connections/postgres_connections.py", line 122, in is_connected
    self.pool.putconn(conn)
    ~~~~~~~~~~~~~~~~~^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/orchestration-engine-tNJVEdE_-py3.13/lib/python3.13/site-packages/psycopg2/pool.py", line 177, in putconn
    self._putconn(conn, key, close)
    ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/orchestration-engine-tNJVEdE_-py3.13/lib/python3.13/site-packages/psycopg2/pool.py", line 115, in _putconn
    conn.rollback()
    ~~~~~~~~~~~~~^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/asyncio/runners.py", line 157, in _on_sigint
    raise KeyboardInterrupt()
KeyboardInterrupt

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 445, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<7 lines>...
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_executor.py", line 192, in execute_tool
    await self.cleanup_loop_resources()
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_executor.py", line 2014, in cleanup_loop_resources
    stalled_chains = await self.loop_body_chain_executor.cleanup_stalled_chains()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/loop_executor/loop_body_chain_executor.py", line 1215, in cleanup_stalled_chains
    for chain_id, chain_state in list(self.active_chains.items()):
                                      ^^^^^^^^^^^^^^^^^^
AttributeError: 'LoopBodyChainExecutor' object has no attribute 'active_chains'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 204, in _execute_transition_with_tracking
    result = await self._execute_standard_or_reflection_transition(transition)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 703, in _execute_standard_or_reflection_transition
    raise Exception(f"Tool execution error: {error_message}")
Exception: Tool execution error: [ERROR] Tool Execution Failed with error: 'LoopBodyChainExecutor' object has no attribute 'active_chains'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/executor_core.py", line 285, in execute
    raise e
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/executor_core.py", line 267, in execute
    raise result
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 230, in _execute_transition_with_tracking
    raise Exception(f"Exception in transition {transition_id}: {str(e)}")
Exception: Exception in transition transition-LoopNode-*************: Tool execution error: [ERROR] Tool Execution Failed with error: 'LoopBodyChainExecutor' object has no attribute 'active_chains'

2025-07-01 09:40:12 - KafkaWorkflowConsumer - ERROR - Exception in workflow execution: Exception in transition transition-LoopNode-*************: Tool execution error: [ERROR] Tool Execution Failed with error: 'LoopBodyChainExecutor' object has no attribute 'active_chains'
2025-07-01 09:40:12 - KafkaWorkflowConsumer - INFO - Workflow '0c19c070-905e-46ef-9f57-62eb427bf396' final status: failed, result: Exception in workflow '0c19c070-905e-46ef-9f57-62eb427bf396': Exception in transition transition-LoopNode-*************: Tool execution error: [ERROR] Tool Execution Failed with error: 'LoopBodyChainExecutor' object has no attribute 'active_chains'
2025-07-01 09:40:12 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 1fcc8e97-8957-45b8-9529-154d89b8d86a, response: {'status': 'failed', 'result': "Exception in workflow '0c19c070-905e-46ef-9f57-62eb427bf396': Exception in transition transition-LoopNode-*************: Tool execution error: [ERROR] Tool Execution Failed with error: 'LoopBodyChainExecutor' object has no attribute 'active_chains'", 'workflow_status': 'failed', 'error': "Exception in transition transition-LoopNode-*************: Tool execution error: [ERROR] Tool Execution Failed with error: 'LoopBodyChainExecutor' object has no attribute 'active_chains'", 'error_type': 'Exception'}
2025-07-01 09:40:12 - KafkaWorkflowConsumer - DEBUG - Stopped workflow with correlation_id: 1fcc8e97-8957-45b8-9529-154d89b8d86a 
2025-07-01 09:40:12 - MCPToolExecutor - INFO - Stopping KafkaToolExecutor internal consumer components...
2025-07-01 09:40:12 - MCPToolExecutor - DEBUG - Cancelling background consumer task...
2025-07-01 09:40:12 - MCPToolExecutor - INFO - Result consumer loop cancelled.
2025-07-01 09:40:12 - MCPToolExecutor - DEBUG - Stopping internal Kafka consumer...
2025-07-01 09:40:13 - MCPToolExecutor - INFO - Internal Kafka consumer stopped.
2025-07-01 09:40:13 - MCPToolExecutor - INFO - KafkaToolExecutor internal consumer stopped.
2025-07-01 09:40:13 - NodeExecutor - INFO - Stopping NodeExecutor internal consumer components...
2025-07-01 09:40:13 - NodeExecutor - DEBUG - Cancelling background consumer task...
2025-07-01 09:40:13 - NodeExecutor - INFO - Result consumer loop cancelled.
2025-07-01 09:40:13 - NodeExecutor - DEBUG - Stopping internal Kafka consumer...
2025-07-01 09:40:13 - NodeExecutor - INFO - Internal Kafka consumer stopped.
2025-07-01 09:40:13 - NodeExecutor - INFO - NodeExecutor internal consumer stopped.
2025-07-01 09:40:13 - AgentExecutor - INFO - Stopping AgentExecutor internal consumer components...
2025-07-01 09:40:13 - AgentExecutor - DEBUG - Cancelling background consumer task...
2025-07-01 09:40:13 - AgentExecutor - INFO - Result consumer loop cancelled.
2025-07-01 09:40:13 - AgentExecutor - DEBUG - Stopping internal Kafka consumer...
2025-07-01 09:40:14 - AgentExecutor - INFO - Internal Kafka consumer stopped.
2025-07-01 09:40:14 - AgentExecutor - INFO - AgentExecutor internal consumer stopped.
2025-07-01 09:40:14 - KafkaWorkflowConsumer - INFO - Closing database connections...
2025-07-01 09:40:14 - RedisManager - INFO - Redis connection closed.
2025-07-01 09:40:14 - RedisManager - INFO - Redis connection closed.
2025-07-01 09:40:14 - PostgresManager - INFO - PostgreSQL connection pool closed.
2025-07-01 09:40:14 - RedisEventListener - INFO - Redis event listener thread stopped
2025-07-01 09:40:14 - RedisEventListener - INFO - Redis event listener stopped
2025-07-01 09:40:14 - KafkaWorkflowConsumer - INFO - Database connections closed
2025-07-01 09:40:14 - KafkaWorkflowConsumer - INFO - Consumer and producer stopped gracefully.
